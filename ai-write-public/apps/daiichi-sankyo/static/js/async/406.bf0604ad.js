"use strict";(self.webpackChunkdify_chat_web=self.webpackChunkdify_chat_web||[]).push([["406"],{16213:function(e,t,r){var n,i;t.CancellationTokenSource=t.CancellationToken=void 0;let a=r(52647),s=r(36261),o=r(38711);(i=n||(t.CancellationToken=n={})).None=Object.freeze({isCancellationRequested:!1,onCancellationRequested:o.Event.None}),i.Cancelled=Object.freeze({isCancellationRequested:!0,onCancellationRequested:o.Event.None}),i.is=function(e){return e&&(e===i.None||e===i.Cancelled||s.boolean(e.isCancellationRequested)&&!!e.onCancellationRequested)};let l=Object.freeze(function(e,t){let r=(0,a.default)().timer.setTimeout(e.bind(t),0);return{dispose(){r.dispose()}}});class u{constructor(){this._isCancelled=!1}cancel(){!this._isCancelled&&(this._isCancelled=!0,this._emitter&&(this._emitter.fire(void 0),this.dispose()))}get isCancellationRequested(){return this._isCancelled}get onCancellationRequested(){return this._isCancelled?l:(this._emitter||(this._emitter=new o.Emitter),this._emitter.event)}dispose(){this._emitter&&(this._emitter.dispose(),this._emitter=void 0)}}t.CancellationTokenSource=class{get token(){return this._token||(this._token=new u),this._token}cancel(){this._token?this._token.cancel():this._token=n.Cancelled}dispose(){this._token?this._token instanceof u&&this._token.dispose():this._token=n.None}}},38711:function(e,t,r){Object.defineProperty(t,"__esModule",{value:!0}),t.Emitter=t.Event=void 0;let n=r(52647);var i,a=i||(t.Event=i={});let s={dispose(){}};a.None=function(){return s};class o{add(e,t=null,r){this._callbacks||(this._callbacks=[],this._contexts=[]),this._callbacks.push(e),this._contexts.push(t),Array.isArray(r)&&r.push({dispose:()=>this.remove(e,t)})}remove(e,t=null){if(!this._callbacks)return;let r=!1;for(let n=0,i=this._callbacks.length;n<i;n++)if(this._callbacks[n]===e)if(this._contexts[n]===t){this._callbacks.splice(n,1),this._contexts.splice(n,1);return}else r=!0;if(r)throw Error("When adding a listener with a context, you should remove it with the same context")}invoke(...e){if(!this._callbacks)return[];let t=[],r=this._callbacks.slice(0),i=this._contexts.slice(0);for(let a=0,s=r.length;a<s;a++)try{t.push(r[a].apply(i[a],e))}catch(e){(0,n.default)().console.error(e)}return t}isEmpty(){return!this._callbacks||0===this._callbacks.length}dispose(){this._callbacks=void 0,this._contexts=void 0}}class l{constructor(e){this._options=e}get event(){return this._event||(this._event=(e,t,r)=>{this._callbacks||(this._callbacks=new o),this._options&&this._options.onFirstListenerAdd&&this._callbacks.isEmpty()&&this._options.onFirstListenerAdd(this),this._callbacks.add(e,t);let n={dispose:()=>{this._callbacks&&(this._callbacks.remove(e,t),n.dispose=l._noop,this._options&&this._options.onLastListenerRemove&&this._callbacks.isEmpty()&&this._options.onLastListenerRemove(this))}};return Array.isArray(r)&&r.push(n),n}),this._event}fire(e){this._callbacks&&this._callbacks.invoke.call(this._callbacks,e)}dispose(){this._callbacks&&(this._callbacks.dispose(),this._callbacks=void 0)}}t.Emitter=l,l._noop=function(){}},36261:function(e,t){function r(e){return"string"==typeof e||e instanceof String}function n(e){return Array.isArray(e)}Object.defineProperty(t,"__esModule",{value:!0}),t.stringArray=t.array=t.func=t.error=t.number=t.string=t.boolean=void 0,t.boolean=function(e){return!0===e||!1===e},t.string=r,t.number=function(e){return"number"==typeof e||e instanceof Number},t.error=function(e){return e instanceof Error},t.func=function(e){return"function"==typeof e},t.array=n,t.stringArray=function(e){return n(e)&&e.every(e=>r(e))}},52647:function(e,t){let r;function n(){if(void 0===r)throw Error("No runtime abstraction layer installed");return r}Object.defineProperty(t,"__esModule",{value:!0}),(n||(n={})).install=function(e){if(void 0===e)throw Error("No runtime abstraction layer provided");r=e},t.default=n},51026:function(e,t,r){function n(e){return e.charCodeAt(0)}function i(e,t){Array.isArray(e)?e.forEach(function(e){t.push(e)}):t.push(e)}function a(e,t){if(!0===e[t])throw"duplicate flag "+t;e[t],e[t]=!0}function s(e){if(void 0===e)throw Error("Internal Error - Should never get here!");return!0}function o(){throw Error("Internal Error - Should never get here!")}function l(e){return"Character"===e.type}r.d(t,{e:()=>g,O:()=>m});let u=[];for(let e=n("0");e<=n("9");e++)u.push(e);let c=[n("_")].concat(u);for(let e=n("a");e<=n("z");e++)c.push(e);for(let e=n("A");e<=n("Z");e++)c.push(e);let d=[n(" "),n("\f"),n("\n"),n("\r"),n("	"),n("\v"),n("	"),n("\xa0"),n(" "),n(" "),n(" "),n(" "),n(" "),n(" "),n(" "),n(" "),n(" "),n(" "),n(" "),n(" "),n("\u2028"),n("\u2029"),n(" "),n(" "),n("　"),n("\uFEFF")],h=/[0-9a-fA-F]/,f=/[0-9]/,p=/[1-9]/;class m{constructor(){this.idx=0,this.input="",this.groupIdx=0}saveState(){return{idx:this.idx,input:this.input,groupIdx:this.groupIdx}}restoreState(e){this.idx=e.idx,this.input=e.input,this.groupIdx=e.groupIdx}pattern(e){this.idx=0,this.input=e,this.groupIdx=0,this.consumeChar("/");let t=this.disjunction();this.consumeChar("/");let r={type:"Flags",loc:{begin:this.idx,end:e.length},global:!1,ignoreCase:!1,multiLine:!1,unicode:!1,sticky:!1};for(;this.isRegExpFlag();)switch(this.popChar()){case"g":a(r,"global");break;case"i":a(r,"ignoreCase");break;case"m":a(r,"multiLine");break;case"u":a(r,"unicode");break;case"y":a(r,"sticky")}if(this.idx!==this.input.length)throw Error("Redundant input: "+this.input.substring(this.idx));return{type:"Pattern",flags:r,value:t,loc:this.loc(0)}}disjunction(){let e=[],t=this.idx;for(e.push(this.alternative());"|"===this.peekChar();)this.consumeChar("|"),e.push(this.alternative());return{type:"Disjunction",value:e,loc:this.loc(t)}}alternative(){let e=[],t=this.idx;for(;this.isTerm();)e.push(this.term());return{type:"Alternative",value:e,loc:this.loc(t)}}term(){return this.isAssertion()?this.assertion():this.atom()}assertion(){let e=this.idx;switch(this.popChar()){case"^":return{type:"StartAnchor",loc:this.loc(e)};case"$":return{type:"EndAnchor",loc:this.loc(e)};case"\\":switch(this.popChar()){case"b":return{type:"WordBoundary",loc:this.loc(e)};case"B":return{type:"NonWordBoundary",loc:this.loc(e)}}throw Error("Invalid Assertion Escape");case"(":let t;switch(this.consumeChar("?"),this.popChar()){case"=":t="Lookahead";break;case"!":t="NegativeLookahead"}s(t);let r=this.disjunction();return this.consumeChar(")"),{type:t,value:r,loc:this.loc(e)}}return o()}quantifier(e=!1){let t,r=this.idx;switch(this.popChar()){case"*":t={atLeast:0,atMost:1/0};break;case"+":t={atLeast:1,atMost:1/0};break;case"?":t={atLeast:0,atMost:1};break;case"{":let n=this.integerIncludingZero();switch(this.popChar()){case"}":t={atLeast:n,atMost:n};break;case",":t=this.isDigit()?{atLeast:n,atMost:this.integerIncludingZero()}:{atLeast:n,atMost:1/0},this.consumeChar("}")}if(!0===e&&void 0===t)return;s(t)}if((!0!==e||void 0!==t)&&s(t))return"?"===this.peekChar(0)?(this.consumeChar("?"),t.greedy=!1):t.greedy=!0,t.type="Quantifier",t.loc=this.loc(r),t}atom(){let e,t=this.idx;switch(this.peekChar()){case".":e=this.dotAll();break;case"\\":e=this.atomEscape();break;case"[":e=this.characterClass();break;case"(":e=this.group()}return(void 0===e&&this.isPatternCharacter()&&(e=this.patternCharacter()),s(e))?(e.loc=this.loc(t),this.isQuantifier()&&(e.quantifier=this.quantifier()),e):o()}dotAll(){return this.consumeChar("."),{type:"Set",complement:!0,value:[n("\n"),n("\r"),n("\u2028"),n("\u2029")]}}atomEscape(){switch(this.consumeChar("\\"),this.peekChar()){case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":return this.decimalEscapeAtom();case"d":case"D":case"s":case"S":case"w":case"W":return this.characterClassEscape();case"f":case"n":case"r":case"t":case"v":return this.controlEscapeAtom();case"c":return this.controlLetterEscapeAtom();case"0":return this.nulCharacterAtom();case"x":return this.hexEscapeSequenceAtom();case"u":return this.regExpUnicodeEscapeSequenceAtom();default:return this.identityEscapeAtom()}}decimalEscapeAtom(){return{type:"GroupBackReference",value:this.positiveInteger()}}characterClassEscape(){let e,t=!1;switch(this.popChar()){case"d":e=u;break;case"D":e=u,t=!0;break;case"s":e=d;break;case"S":e=d,t=!0;break;case"w":e=c;break;case"W":e=c,t=!0}return s(e)?{type:"Set",value:e,complement:t}:o()}controlEscapeAtom(){let e;switch(this.popChar()){case"f":e=n("\f");break;case"n":e=n("\n");break;case"r":e=n("\r");break;case"t":e=n("	");break;case"v":e=n("\v")}return s(e)?{type:"Character",value:e}:o()}controlLetterEscapeAtom(){this.consumeChar("c");let e=this.popChar();if(!1===/[a-zA-Z]/.test(e))throw Error("Invalid ");return{type:"Character",value:e.toUpperCase().charCodeAt(0)-64}}nulCharacterAtom(){return this.consumeChar("0"),{type:"Character",value:n("\0")}}hexEscapeSequenceAtom(){return this.consumeChar("x"),this.parseHexDigits(2)}regExpUnicodeEscapeSequenceAtom(){return this.consumeChar("u"),this.parseHexDigits(4)}identityEscapeAtom(){return{type:"Character",value:n(this.popChar())}}classPatternCharacterAtom(){switch(this.peekChar()){case"\n":case"\r":case"\u2028":case"\u2029":case"\\":case"]":throw Error("TBD");default:return{type:"Character",value:n(this.popChar())}}}characterClass(){let e=[],t=!1;for(this.consumeChar("["),"^"===this.peekChar(0)&&(this.consumeChar("^"),t=!0);this.isClassAtom();){let t=this.classAtom();if(t.type,l(t)&&this.isRangeDash()){this.consumeChar("-");let r=this.classAtom();if(r.type,l(r)){if(r.value<t.value)throw Error("Range out of order in character class");e.push({from:t.value,to:r.value})}else i(t.value,e),e.push(n("-")),i(r.value,e)}else i(t.value,e)}return this.consumeChar("]"),{type:"Set",complement:t,value:e}}classAtom(){switch(this.peekChar()){case"]":case"\n":case"\r":case"\u2028":case"\u2029":throw Error("TBD");case"\\":return this.classEscape();default:return this.classPatternCharacterAtom()}}classEscape(){switch(this.consumeChar("\\"),this.peekChar()){case"b":return this.consumeChar("b"),{type:"Character",value:n("\b")};case"d":case"D":case"s":case"S":case"w":case"W":return this.characterClassEscape();case"f":case"n":case"r":case"t":case"v":return this.controlEscapeAtom();case"c":return this.controlLetterEscapeAtom();case"0":return this.nulCharacterAtom();case"x":return this.hexEscapeSequenceAtom();case"u":return this.regExpUnicodeEscapeSequenceAtom();default:return this.identityEscapeAtom()}}group(){let e=!0;this.consumeChar("("),"?"===this.peekChar(0)?(this.consumeChar("?"),this.consumeChar(":"),e=!1):this.groupIdx++;let t=this.disjunction();this.consumeChar(")");let r={type:"Group",capturing:e,value:t};return e&&(r.idx=this.groupIdx),r}positiveInteger(){let e=this.popChar();if(!1===p.test(e))throw Error("Expecting a positive integer");for(;f.test(this.peekChar(0));)e+=this.popChar();return parseInt(e,10)}integerIncludingZero(){let e=this.popChar();if(!1===f.test(e))throw Error("Expecting an integer");for(;f.test(this.peekChar(0));)e+=this.popChar();return parseInt(e,10)}patternCharacter(){let e=this.popChar();switch(e){case"\n":case"\r":case"\u2028":case"\u2029":case"^":case"$":case"\\":case".":case"*":case"+":case"?":case"(":case")":case"[":case"|":throw Error("TBD");default:return{type:"Character",value:n(e)}}}isRegExpFlag(){switch(this.peekChar(0)){case"g":case"i":case"m":case"u":case"y":return!0;default:return!1}}isRangeDash(){return"-"===this.peekChar()&&this.isClassAtom(1)}isDigit(){return f.test(this.peekChar(0))}isClassAtom(e=0){switch(this.peekChar(e)){case"]":case"\n":case"\r":case"\u2028":case"\u2029":return!1;default:return!0}}isTerm(){return this.isAtom()||this.isAssertion()}isAtom(){if(this.isPatternCharacter())return!0;switch(this.peekChar(0)){case".":case"\\":case"[":case"(":return!0;default:return!1}}isAssertion(){switch(this.peekChar(0)){case"^":case"$":return!0;case"\\":switch(this.peekChar(1)){case"b":case"B":return!0;default:return!1}case"(":return"?"===this.peekChar(1)&&("="===this.peekChar(2)||"!"===this.peekChar(2));default:return!1}}isQuantifier(){let e=this.saveState();try{return void 0!==this.quantifier(!0)}catch(e){return!1}finally{this.restoreState(e)}}isPatternCharacter(){switch(this.peekChar()){case"^":case"$":case"\\":case".":case"*":case"+":case"?":case"(":case")":case"[":case"|":case"/":case"\n":case"\r":case"\u2028":case"\u2029":return!1;default:return!0}}parseHexDigits(e){let t="";for(let r=0;r<e;r++){let e=this.popChar();if(!1===h.test(e))throw Error("Expecting a HexDecimal digits");t+=e}return{type:"Character",value:parseInt(t,16)}}peekChar(e=0){return this.input[this.idx+e]}popChar(){let e=this.peekChar(0);return this.consumeChar(void 0),e}consumeChar(e){if(void 0!==e&&this.input[this.idx]!==e)throw Error("Expected: '"+e+"' but found: '"+this.input[this.idx]+"' at offset: "+this.idx);if(this.idx>=this.input.length)throw Error("Unexpected end of input");this.idx++}loc(e){return{begin:e,end:this.idx}}}class g{visitChildren(e){for(let t in e){let r=e[t];e.hasOwnProperty(t)&&(void 0!==r.type?this.visit(r):Array.isArray(r)&&r.forEach(e=>{this.visit(e)},this))}}visit(e){switch(e.type){case"Pattern":this.visitPattern(e);break;case"Flags":this.visitFlags(e);break;case"Disjunction":this.visitDisjunction(e);break;case"Alternative":this.visitAlternative(e);break;case"StartAnchor":this.visitStartAnchor(e);break;case"EndAnchor":this.visitEndAnchor(e);break;case"WordBoundary":this.visitWordBoundary(e);break;case"NonWordBoundary":this.visitNonWordBoundary(e);break;case"Lookahead":this.visitLookahead(e);break;case"NegativeLookahead":this.visitNegativeLookahead(e);break;case"Character":this.visitCharacter(e);break;case"Set":this.visitSet(e);break;case"Group":this.visitGroup(e);break;case"GroupBackReference":this.visitGroupBackReference(e);break;case"Quantifier":this.visitQuantifier(e)}this.visitChildren(e)}visitPattern(e){}visitFlags(e){}visitDisjunction(e){}visitAlternative(e){}visitStartAnchor(e){}visitEndAnchor(e){}visitWordBoundary(e){}visitNonWordBoundary(e){}visitLookahead(e){}visitNegativeLookahead(e){}visitCharacter(e){}visitSet(e){}visitGroup(e){}visitGroupBackReference(e){}visitQuantifier(e){}}},56196:function(e,t,r){r.d(t,{z:()=>u});var n=r(36902),i=r(62297),a=r(29266),s=r(92910),o=class extends n.T7{static{(0,n.eW)(this,"GitGraphTokenBuilder")}constructor(){super(["gitGraph"])}},l={parser:{TokenBuilder:(0,n.eW)(()=>new o,"TokenBuilder"),ValueConverter:(0,n.eW)(()=>new n.nr,"ValueConverter")}};function u(e=i.u){let t=(0,a.f3)((0,s.T)(e),n.GS),r=(0,a.f3)((0,s.Q)({shared:t}),n.vn,l);return t.ServiceRegistry.register(r),{shared:t,GitGraph:r}}(0,n.eW)(u,"createGitGraphServices")},54372:function(e,t,r){r.d(t,{T:()=>u});var n=r(36902),i=r(62297),a=r(29266),s=r(92910),o=class extends n.T7{static{(0,n.eW)(this,"RadarTokenBuilder")}constructor(){super(["radar-beta"])}},l={parser:{TokenBuilder:(0,n.eW)(()=>new o,"TokenBuilder"),ValueConverter:(0,n.eW)(()=>new n.nr,"ValueConverter")}};function u(e=i.u){let t=(0,a.f3)((0,s.T)(e),n.GS),r=(0,a.f3)((0,s.Q)({shared:t}),n.gB,l);return t.ServiceRegistry.register(r),{shared:t,Radar:r}}(0,n.eW)(u,"createRadarServices")},36902:function(e,t,r){r.d(t,{gB:()=>ed,F_:()=>es,nr:()=>ep,bb:()=>eo,eW:()=>E,kb:()=>ef,WH:()=>el,Qr:()=>eu,T7:()=>em,vn:()=>ec,GS:()=>ea});var n,i,a,s,o,l,u=r(89365),c=r(92910),d=r(29266),h=r(46117),f=r(62297),p=r(1862);let m={Grammar:()=>void 0,LanguageMetaData:()=>({caseInsensitive:!1,fileExtensions:[".langium"],languageId:"langium"})},g={AstReflection:()=>new h.SV};function y(e){var t;let r=function(){let e=(0,d.f3)((0,c.T)(f.u),g),t=(0,d.f3)((0,c.Q)({shared:e}),m);return e.ServiceRegistry.register(t),t}(),n=r.serializer.JsonSerializer.deserialize(e);return r.shared.workspace.LangiumDocumentFactory.fromModel(n,p.o.parse(`memory://${null!=(t=n.name)?t:"grammar"}.langium`)),n}var T=r(78249),v=r(93774),R=Object.defineProperty,E=(e,t)=>R(e,"name",{value:t,configurable:!0}),A="Statement",k="Architecture";E(function(e){return W.isInstance(e,k)},"isArchitecture");var $="Axis",x="Branch";E(function(e){return W.isInstance(e,x)},"isBranch");var I="Checkout",S="CherryPicking",C="Commit";E(function(e){return W.isInstance(e,C)},"isCommit");var N="Common";E(function(e){return W.isInstance(e,N)},"isCommon");var w="Curve",L="Edge",b="Entry",O="GitGraph";E(function(e){return W.isInstance(e,O)},"isGitGraph");var _="Group",P="Info";E(function(e){return W.isInstance(e,P)},"isInfo");var M="Junction",D="Merge";E(function(e){return W.isInstance(e,D)},"isMerge");var Z="Option",F="Packet";E(function(e){return W.isInstance(e,F)},"isPacket");var U="PacketBlock";E(function(e){return W.isInstance(e,U)},"isPacketBlock"),E(function(e){return W.isInstance(e,"Pie")},"isPie");var G="PieSection";E(function(e){return W.isInstance(e,G)},"isPieSection");var B="Radar",V="Service",K="Direction",j=class extends u.$v{static{E(this,"MermaidAstReflection")}getAllTypes(){return[k,$,x,I,S,C,N,w,K,L,b,O,_,P,M,D,Z,F,U,"Pie",G,B,V,A]}computeIsSubtype(e,t){switch(e){case x:case I:case S:case C:case D:return this.isSubtype(A,t);case K:return this.isSubtype(O,t);default:return!1}}getReferenceType(e){let t=`${e.container.$type}:${e.property}`;if("Entry:axis"===t)return $;throw Error(`${t} is not a valid reference id.`)}getTypeMetaData(e){switch(e){case k:return{name:k,properties:[{name:"accDescr"},{name:"accTitle"},{name:"edges",defaultValue:[]},{name:"groups",defaultValue:[]},{name:"junctions",defaultValue:[]},{name:"services",defaultValue:[]},{name:"title"}]};case $:return{name:$,properties:[{name:"label"},{name:"name"}]};case x:return{name:x,properties:[{name:"name"},{name:"order"}]};case I:return{name:I,properties:[{name:"branch"}]};case S:return{name:S,properties:[{name:"id"},{name:"parent"},{name:"tags",defaultValue:[]}]};case C:return{name:C,properties:[{name:"id"},{name:"message"},{name:"tags",defaultValue:[]},{name:"type"}]};case N:return{name:N,properties:[{name:"accDescr"},{name:"accTitle"},{name:"title"}]};case w:return{name:w,properties:[{name:"entries",defaultValue:[]},{name:"label"},{name:"name"}]};case L:return{name:L,properties:[{name:"lhsDir"},{name:"lhsGroup",defaultValue:!1},{name:"lhsId"},{name:"lhsInto",defaultValue:!1},{name:"rhsDir"},{name:"rhsGroup",defaultValue:!1},{name:"rhsId"},{name:"rhsInto",defaultValue:!1},{name:"title"}]};case b:return{name:b,properties:[{name:"axis"},{name:"value"}]};case O:return{name:O,properties:[{name:"accDescr"},{name:"accTitle"},{name:"statements",defaultValue:[]},{name:"title"}]};case _:return{name:_,properties:[{name:"icon"},{name:"id"},{name:"in"},{name:"title"}]};case P:return{name:P,properties:[{name:"accDescr"},{name:"accTitle"},{name:"title"}]};case M:return{name:M,properties:[{name:"id"},{name:"in"}]};case D:return{name:D,properties:[{name:"branch"},{name:"id"},{name:"tags",defaultValue:[]},{name:"type"}]};case Z:return{name:Z,properties:[{name:"name"},{name:"value",defaultValue:!1}]};case F:return{name:F,properties:[{name:"accDescr"},{name:"accTitle"},{name:"blocks",defaultValue:[]},{name:"title"}]};case U:return{name:U,properties:[{name:"end"},{name:"label"},{name:"start"}]};case"Pie":return{name:"Pie",properties:[{name:"accDescr"},{name:"accTitle"},{name:"sections",defaultValue:[]},{name:"showData",defaultValue:!1},{name:"title"}]};case G:return{name:G,properties:[{name:"label"},{name:"value"}]};case B:return{name:B,properties:[{name:"accDescr"},{name:"accTitle"},{name:"axes",defaultValue:[]},{name:"curves",defaultValue:[]},{name:"options",defaultValue:[]},{name:"title"}]};case V:return{name:V,properties:[{name:"icon"},{name:"iconText"},{name:"id"},{name:"in"},{name:"title"}]};case K:return{name:K,properties:[{name:"accDescr"},{name:"accTitle"},{name:"dir"},{name:"statements",defaultValue:[]},{name:"title"}]};default:return{name:e,properties:[]}}}},W=new j,H=E(()=>n??(n=y('{"$type":"Grammar","isDeclared":true,"name":"Info","imports":[],"rules":[{"$type":"ParserRule","entry":true,"name":"Info","definition":{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@3"},"arguments":[],"cardinality":"*"},{"$type":"Keyword","value":"info"},{"$type":"RuleCall","rule":{"$ref":"#/rules@3"},"arguments":[],"cardinality":"*"},{"$type":"Group","elements":[{"$type":"Keyword","value":"showInfo"},{"$type":"RuleCall","rule":{"$ref":"#/rules@3"},"arguments":[],"cardinality":"*"}],"cardinality":"?"},{"$type":"RuleCall","rule":{"$ref":"#/rules@1"},"arguments":[],"cardinality":"?"}]},"definesHiddenTokens":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"TitleAndAccessibilities","definition":{"$type":"Group","elements":[{"$type":"Alternatives","elements":[{"$type":"Assignment","feature":"accDescr","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@4"},"arguments":[]}},{"$type":"Assignment","feature":"accTitle","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@5"},"arguments":[]}},{"$type":"Assignment","feature":"title","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@6"},"arguments":[]}}]},{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[]}],"cardinality":"+"},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"EOL","dataType":"string","definition":{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@3"},"arguments":[],"cardinality":"+"},{"$type":"EndOfFile"}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"TerminalRule","name":"NEWLINE","definition":{"$type":"RegexToken","regex":"/\\\\r?\\\\n/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_DESCR","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accDescr(?:[\\\\t ]*:([^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)|\\\\s*{([^}]*)})/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accTitle[\\\\t ]*:(?:[^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*title(?:[\\\\t ][^\\\\n\\\\r]*?(?=%%)|[\\\\t ][^\\\\n\\\\r]*|)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","hidden":true,"name":"WHITESPACE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]+/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"YAML","definition":{"$type":"RegexToken","regex":"/---[\\\\t ]*\\\\r?\\\\n(?:[\\\\S\\\\s]*?\\\\r?\\\\n)?---(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"DIRECTIVE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%{[\\\\S\\\\s]*?}%%(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"SINGLE_LINE_COMMENT","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%[^\\\\n\\\\r]*/"},"fragment":false}],"definesHiddenTokens":false,"hiddenTokens":[],"interfaces":[{"$type":"Interface","name":"Common","attributes":[{"$type":"TypeAttribute","name":"accDescr","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}},{"$type":"TypeAttribute","name":"accTitle","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}},{"$type":"TypeAttribute","name":"title","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}}],"superTypes":[]}],"types":[],"usedGrammars":[]}')),"InfoGrammar"),z=E(()=>i??(i=y(`{"$type":"Grammar","isDeclared":true,"name":"Packet","imports":[],"rules":[{"$type":"ParserRule","entry":true,"name":"Packet","definition":{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@6"},"arguments":[],"cardinality":"*"},{"$type":"Keyword","value":"packet-beta"},{"$type":"Alternatives","elements":[{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@6"},"arguments":[],"cardinality":"*"},{"$type":"RuleCall","rule":{"$ref":"#/rules@4"},"arguments":[]},{"$type":"Assignment","feature":"blocks","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@1"},"arguments":[]},"cardinality":"*"}]},{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@6"},"arguments":[],"cardinality":"+"},{"$type":"Assignment","feature":"blocks","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@1"},"arguments":[]},"cardinality":"+"}]},{"$type":"RuleCall","rule":{"$ref":"#/rules@6"},"arguments":[],"cardinality":"*"}]}]},"definesHiddenTokens":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"PacketBlock","definition":{"$type":"Group","elements":[{"$type":"Assignment","feature":"start","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[]}},{"$type":"Group","elements":[{"$type":"Keyword","value":"-"},{"$type":"Assignment","feature":"end","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[]}}],"cardinality":"?"},{"$type":"Keyword","value":":"},{"$type":"Assignment","feature":"label","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@3"},"arguments":[]}},{"$type":"RuleCall","rule":{"$ref":"#/rules@5"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"TerminalRule","name":"INT","type":{"$type":"ReturnType","name":"number"},"definition":{"$type":"RegexToken","regex":"/0|[1-9][0-9]*/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"STRING","definition":{"$type":"RegexToken","regex":"/\\"[^\\"]*\\"|'[^']*'/"},"fragment":false,"hidden":false},{"$type":"ParserRule","fragment":true,"name":"TitleAndAccessibilities","definition":{"$type":"Group","elements":[{"$type":"Alternatives","elements":[{"$type":"Assignment","feature":"accDescr","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@7"},"arguments":[]}},{"$type":"Assignment","feature":"accTitle","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@8"},"arguments":[]}},{"$type":"Assignment","feature":"title","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@9"},"arguments":[]}}]},{"$type":"RuleCall","rule":{"$ref":"#/rules@5"},"arguments":[]}],"cardinality":"+"},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"EOL","dataType":"string","definition":{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@6"},"arguments":[],"cardinality":"+"},{"$type":"EndOfFile"}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"TerminalRule","name":"NEWLINE","definition":{"$type":"RegexToken","regex":"/\\\\r?\\\\n/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_DESCR","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accDescr(?:[\\\\t ]*:([^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)|\\\\s*{([^}]*)})/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accTitle[\\\\t ]*:(?:[^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*title(?:[\\\\t ][^\\\\n\\\\r]*?(?=%%)|[\\\\t ][^\\\\n\\\\r]*|)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","hidden":true,"name":"WHITESPACE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]+/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"YAML","definition":{"$type":"RegexToken","regex":"/---[\\\\t ]*\\\\r?\\\\n(?:[\\\\S\\\\s]*?\\\\r?\\\\n)?---(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"DIRECTIVE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%{[\\\\S\\\\s]*?}%%(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"SINGLE_LINE_COMMENT","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%[^\\\\n\\\\r]*/"},"fragment":false}],"definesHiddenTokens":false,"hiddenTokens":[],"interfaces":[{"$type":"Interface","name":"Common","attributes":[{"$type":"TypeAttribute","name":"accDescr","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}},{"$type":"TypeAttribute","name":"accTitle","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}},{"$type":"TypeAttribute","name":"title","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}}],"superTypes":[]}],"types":[],"usedGrammars":[]}`)),"PacketGrammar"),Y=E(()=>a??(a=y('{"$type":"Grammar","isDeclared":true,"name":"Pie","imports":[],"rules":[{"$type":"ParserRule","entry":true,"name":"Pie","definition":{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@6"},"arguments":[],"cardinality":"*"},{"$type":"Keyword","value":"pie"},{"$type":"Assignment","feature":"showData","operator":"?=","terminal":{"$type":"Keyword","value":"showData"},"cardinality":"?"},{"$type":"Alternatives","elements":[{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@6"},"arguments":[],"cardinality":"*"},{"$type":"RuleCall","rule":{"$ref":"#/rules@4"},"arguments":[]},{"$type":"Assignment","feature":"sections","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@1"},"arguments":[]},"cardinality":"*"}]},{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@6"},"arguments":[],"cardinality":"+"},{"$type":"Assignment","feature":"sections","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@1"},"arguments":[]},"cardinality":"+"}]},{"$type":"RuleCall","rule":{"$ref":"#/rules@6"},"arguments":[],"cardinality":"*"}]}]},"definesHiddenTokens":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"PieSection","definition":{"$type":"Group","elements":[{"$type":"Assignment","feature":"label","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[]}},{"$type":"Keyword","value":":"},{"$type":"Assignment","feature":"value","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@3"},"arguments":[]}},{"$type":"RuleCall","rule":{"$ref":"#/rules@5"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"TerminalRule","name":"PIE_SECTION_LABEL","definition":{"$type":"RegexToken","regex":"/\\"[^\\"]+\\"/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"PIE_SECTION_VALUE","type":{"$type":"ReturnType","name":"number"},"definition":{"$type":"RegexToken","regex":"/(0|[1-9][0-9]*)(\\\\.[0-9]+)?/"},"fragment":false,"hidden":false},{"$type":"ParserRule","fragment":true,"name":"TitleAndAccessibilities","definition":{"$type":"Group","elements":[{"$type":"Alternatives","elements":[{"$type":"Assignment","feature":"accDescr","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@7"},"arguments":[]}},{"$type":"Assignment","feature":"accTitle","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@8"},"arguments":[]}},{"$type":"Assignment","feature":"title","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@9"},"arguments":[]}}]},{"$type":"RuleCall","rule":{"$ref":"#/rules@5"},"arguments":[]}],"cardinality":"+"},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"EOL","dataType":"string","definition":{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@6"},"arguments":[],"cardinality":"+"},{"$type":"EndOfFile"}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"TerminalRule","name":"NEWLINE","definition":{"$type":"RegexToken","regex":"/\\\\r?\\\\n/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_DESCR","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accDescr(?:[\\\\t ]*:([^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)|\\\\s*{([^}]*)})/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accTitle[\\\\t ]*:(?:[^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*title(?:[\\\\t ][^\\\\n\\\\r]*?(?=%%)|[\\\\t ][^\\\\n\\\\r]*|)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","hidden":true,"name":"WHITESPACE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]+/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"YAML","definition":{"$type":"RegexToken","regex":"/---[\\\\t ]*\\\\r?\\\\n(?:[\\\\S\\\\s]*?\\\\r?\\\\n)?---(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"DIRECTIVE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%{[\\\\S\\\\s]*?}%%(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"SINGLE_LINE_COMMENT","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%[^\\\\n\\\\r]*/"},"fragment":false}],"definesHiddenTokens":false,"hiddenTokens":[],"interfaces":[{"$type":"Interface","name":"Common","attributes":[{"$type":"TypeAttribute","name":"accDescr","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}},{"$type":"TypeAttribute","name":"accTitle","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}},{"$type":"TypeAttribute","name":"title","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}}],"superTypes":[]}],"types":[],"usedGrammars":[]}')),"PieGrammar"),q=E(()=>s??(s=y('{"$type":"Grammar","isDeclared":true,"name":"Architecture","imports":[],"rules":[{"$type":"ParserRule","entry":true,"name":"Architecture","definition":{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@18"},"arguments":[],"cardinality":"*"},{"$type":"Keyword","value":"architecture-beta"},{"$type":"Alternatives","elements":[{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@18"},"arguments":[],"cardinality":"*"},{"$type":"RuleCall","rule":{"$ref":"#/rules@16"},"arguments":[]}]},{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@18"},"arguments":[],"cardinality":"*"},{"$type":"RuleCall","rule":{"$ref":"#/rules@1"},"arguments":[],"cardinality":"*"}]},{"$type":"RuleCall","rule":{"$ref":"#/rules@18"},"arguments":[],"cardinality":"*"}]}]},"definesHiddenTokens":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"Statement","definition":{"$type":"Alternatives","elements":[{"$type":"Assignment","feature":"groups","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@5"},"arguments":[]}},{"$type":"Assignment","feature":"services","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@6"},"arguments":[]}},{"$type":"Assignment","feature":"junctions","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@7"},"arguments":[]}},{"$type":"Assignment","feature":"edges","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@8"},"arguments":[]}}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"LeftPort","definition":{"$type":"Group","elements":[{"$type":"Keyword","value":":"},{"$type":"Assignment","feature":"lhsDir","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@9"},"arguments":[]}}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"RightPort","definition":{"$type":"Group","elements":[{"$type":"Assignment","feature":"rhsDir","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@9"},"arguments":[]}},{"$type":"Keyword","value":":"}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"Arrow","definition":{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[]},{"$type":"Assignment","feature":"lhsInto","operator":"?=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@15"},"arguments":[]},"cardinality":"?"},{"$type":"Alternatives","elements":[{"$type":"Keyword","value":"--"},{"$type":"Group","elements":[{"$type":"Keyword","value":"-"},{"$type":"Assignment","feature":"title","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@13"},"arguments":[]}},{"$type":"Keyword","value":"-"}]}]},{"$type":"Assignment","feature":"rhsInto","operator":"?=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@15"},"arguments":[]},"cardinality":"?"},{"$type":"RuleCall","rule":{"$ref":"#/rules@3"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Group","definition":{"$type":"Group","elements":[{"$type":"Keyword","value":"group"},{"$type":"Assignment","feature":"id","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@10"},"arguments":[]}},{"$type":"Assignment","feature":"icon","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@12"},"arguments":[]},"cardinality":"?"},{"$type":"Assignment","feature":"title","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@13"},"arguments":[]},"cardinality":"?"},{"$type":"Group","elements":[{"$type":"Keyword","value":"in"},{"$type":"Assignment","feature":"in","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@10"},"arguments":[]}}],"cardinality":"?"},{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Service","definition":{"$type":"Group","elements":[{"$type":"Keyword","value":"service"},{"$type":"Assignment","feature":"id","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@10"},"arguments":[]}},{"$type":"Alternatives","elements":[{"$type":"Assignment","feature":"iconText","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@11"},"arguments":[]}},{"$type":"Assignment","feature":"icon","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@12"},"arguments":[]}}],"cardinality":"?"},{"$type":"Assignment","feature":"title","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@13"},"arguments":[]},"cardinality":"?"},{"$type":"Group","elements":[{"$type":"Keyword","value":"in"},{"$type":"Assignment","feature":"in","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@10"},"arguments":[]}}],"cardinality":"?"},{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Junction","definition":{"$type":"Group","elements":[{"$type":"Keyword","value":"junction"},{"$type":"Assignment","feature":"id","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@10"},"arguments":[]}},{"$type":"Group","elements":[{"$type":"Keyword","value":"in"},{"$type":"Assignment","feature":"in","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@10"},"arguments":[]}}],"cardinality":"?"},{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Edge","definition":{"$type":"Group","elements":[{"$type":"Assignment","feature":"lhsId","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@10"},"arguments":[]}},{"$type":"Assignment","feature":"lhsGroup","operator":"?=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@14"},"arguments":[]},"cardinality":"?"},{"$type":"RuleCall","rule":{"$ref":"#/rules@4"},"arguments":[]},{"$type":"Assignment","feature":"rhsId","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@10"},"arguments":[]}},{"$type":"Assignment","feature":"rhsGroup","operator":"?=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@14"},"arguments":[]},"cardinality":"?"},{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"TerminalRule","name":"ARROW_DIRECTION","definition":{"$type":"TerminalAlternatives","elements":[{"$type":"TerminalAlternatives","elements":[{"$type":"TerminalAlternatives","elements":[{"$type":"CharacterRange","left":{"$type":"Keyword","value":"L"}},{"$type":"CharacterRange","left":{"$type":"Keyword","value":"R"}}]},{"$type":"CharacterRange","left":{"$type":"Keyword","value":"T"}}]},{"$type":"CharacterRange","left":{"$type":"Keyword","value":"B"}}]},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ARCH_ID","definition":{"$type":"RegexToken","regex":"/[\\\\w]+/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ARCH_TEXT_ICON","definition":{"$type":"RegexToken","regex":"/\\\\(\\"[^\\"]+\\"\\\\)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ARCH_ICON","definition":{"$type":"RegexToken","regex":"/\\\\([\\\\w-:]+\\\\)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ARCH_TITLE","definition":{"$type":"RegexToken","regex":"/\\\\[[\\\\w ]+\\\\]/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ARROW_GROUP","definition":{"$type":"RegexToken","regex":"/\\\\{group\\\\}/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ARROW_INTO","definition":{"$type":"RegexToken","regex":"/<|>/"},"fragment":false,"hidden":false},{"$type":"ParserRule","fragment":true,"name":"TitleAndAccessibilities","definition":{"$type":"Group","elements":[{"$type":"Alternatives","elements":[{"$type":"Assignment","feature":"accDescr","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@19"},"arguments":[]}},{"$type":"Assignment","feature":"accTitle","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[]}},{"$type":"Assignment","feature":"title","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@21"},"arguments":[]}}]},{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}],"cardinality":"+"},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"EOL","dataType":"string","definition":{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@18"},"arguments":[],"cardinality":"+"},{"$type":"EndOfFile"}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"TerminalRule","name":"NEWLINE","definition":{"$type":"RegexToken","regex":"/\\\\r?\\\\n/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_DESCR","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accDescr(?:[\\\\t ]*:([^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)|\\\\s*{([^}]*)})/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accTitle[\\\\t ]*:(?:[^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*title(?:[\\\\t ][^\\\\n\\\\r]*?(?=%%)|[\\\\t ][^\\\\n\\\\r]*|)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","hidden":true,"name":"WHITESPACE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]+/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"YAML","definition":{"$type":"RegexToken","regex":"/---[\\\\t ]*\\\\r?\\\\n(?:[\\\\S\\\\s]*?\\\\r?\\\\n)?---(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"DIRECTIVE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%{[\\\\S\\\\s]*?}%%(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"SINGLE_LINE_COMMENT","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%[^\\\\n\\\\r]*/"},"fragment":false}],"definesHiddenTokens":false,"hiddenTokens":[],"interfaces":[{"$type":"Interface","name":"Common","attributes":[{"$type":"TypeAttribute","name":"accDescr","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}},{"$type":"TypeAttribute","name":"accTitle","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}},{"$type":"TypeAttribute","name":"title","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}}],"superTypes":[]}],"types":[],"usedGrammars":[]}')),"ArchitectureGrammar"),X=E(()=>o??(o=y(`{"$type":"Grammar","isDeclared":true,"name":"GitGraph","interfaces":[{"$type":"Interface","name":"Common","attributes":[{"$type":"TypeAttribute","name":"accDescr","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}},{"$type":"TypeAttribute","name":"accTitle","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}},{"$type":"TypeAttribute","name":"title","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}}],"superTypes":[]}],"rules":[{"$type":"ParserRule","fragment":true,"name":"TitleAndAccessibilities","definition":{"$type":"Group","elements":[{"$type":"Alternatives","elements":[{"$type":"Assignment","feature":"accDescr","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@3"},"arguments":[]}},{"$type":"Assignment","feature":"accTitle","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@4"},"arguments":[]}},{"$type":"Assignment","feature":"title","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@5"},"arguments":[]}}]},{"$type":"RuleCall","rule":{"$ref":"#/rules@1"},"arguments":[]}],"cardinality":"+"},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"EOL","dataType":"string","definition":{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[],"cardinality":"+"},{"$type":"EndOfFile"}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"TerminalRule","name":"NEWLINE","definition":{"$type":"RegexToken","regex":"/\\\\r?\\\\n/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_DESCR","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accDescr(?:[\\\\t ]*:([^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)|\\\\s*{([^}]*)})/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accTitle[\\\\t ]*:(?:[^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*title(?:[\\\\t ][^\\\\n\\\\r]*?(?=%%)|[\\\\t ][^\\\\n\\\\r]*|)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","hidden":true,"name":"WHITESPACE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]+/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"YAML","definition":{"$type":"RegexToken","regex":"/---[\\\\t ]*\\\\r?\\\\n(?:[\\\\S\\\\s]*?\\\\r?\\\\n)?---(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"DIRECTIVE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%{[\\\\S\\\\s]*?}%%(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"SINGLE_LINE_COMMENT","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%[^\\\\n\\\\r]*/"},"fragment":false},{"$type":"ParserRule","entry":true,"name":"GitGraph","definition":{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[],"cardinality":"*"},{"$type":"Alternatives","elements":[{"$type":"Keyword","value":"gitGraph"},{"$type":"Group","elements":[{"$type":"Keyword","value":"gitGraph"},{"$type":"Keyword","value":":"}]},{"$type":"Keyword","value":"gitGraph:"},{"$type":"Group","elements":[{"$type":"Keyword","value":"gitGraph"},{"$type":"RuleCall","rule":{"$ref":"#/rules@12"},"arguments":[]},{"$type":"Keyword","value":":"}]}]},{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[],"cardinality":"*"},{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[],"cardinality":"*"},{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@0"},"arguments":[]},{"$type":"Assignment","feature":"statements","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@11"},"arguments":[]}},{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[]}],"cardinality":"*"}]}]},"definesHiddenTokens":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Statement","definition":{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@13"},"arguments":[]},{"$type":"RuleCall","rule":{"$ref":"#/rules@14"},"arguments":[]},{"$type":"RuleCall","rule":{"$ref":"#/rules@15"},"arguments":[]},{"$type":"RuleCall","rule":{"$ref":"#/rules@16"},"arguments":[]},{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Direction","definition":{"$type":"Assignment","feature":"dir","operator":"=","terminal":{"$type":"Alternatives","elements":[{"$type":"Keyword","value":"LR"},{"$type":"Keyword","value":"TB"},{"$type":"Keyword","value":"BT"}]}},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Commit","definition":{"$type":"Group","elements":[{"$type":"Keyword","value":"commit"},{"$type":"Alternatives","elements":[{"$type":"Group","elements":[{"$type":"Keyword","value":"id:"},{"$type":"Assignment","feature":"id","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[]}}]},{"$type":"Group","elements":[{"$type":"Keyword","value":"msg:","cardinality":"?"},{"$type":"Assignment","feature":"message","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[]}}]},{"$type":"Group","elements":[{"$type":"Keyword","value":"tag:"},{"$type":"Assignment","feature":"tags","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[]}}]},{"$type":"Group","elements":[{"$type":"Keyword","value":"type:"},{"$type":"Assignment","feature":"type","operator":"=","terminal":{"$type":"Alternatives","elements":[{"$type":"Keyword","value":"NORMAL"},{"$type":"Keyword","value":"REVERSE"},{"$type":"Keyword","value":"HIGHLIGHT"}]}}]}],"cardinality":"*"},{"$type":"RuleCall","rule":{"$ref":"#/rules@1"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Branch","definition":{"$type":"Group","elements":[{"$type":"Keyword","value":"branch"},{"$type":"Assignment","feature":"name","operator":"=","terminal":{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@19"},"arguments":[]},{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[]}]}},{"$type":"Group","elements":[{"$type":"Keyword","value":"order:"},{"$type":"Assignment","feature":"order","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@18"},"arguments":[]}}],"cardinality":"?"},{"$type":"RuleCall","rule":{"$ref":"#/rules@1"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Merge","definition":{"$type":"Group","elements":[{"$type":"Keyword","value":"merge"},{"$type":"Assignment","feature":"branch","operator":"=","terminal":{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@19"},"arguments":[]},{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[]}]}},{"$type":"Alternatives","elements":[{"$type":"Group","elements":[{"$type":"Keyword","value":"id:"},{"$type":"Assignment","feature":"id","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[]}}]},{"$type":"Group","elements":[{"$type":"Keyword","value":"tag:"},{"$type":"Assignment","feature":"tags","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[]}}]},{"$type":"Group","elements":[{"$type":"Keyword","value":"type:"},{"$type":"Assignment","feature":"type","operator":"=","terminal":{"$type":"Alternatives","elements":[{"$type":"Keyword","value":"NORMAL"},{"$type":"Keyword","value":"REVERSE"},{"$type":"Keyword","value":"HIGHLIGHT"}]}}]}],"cardinality":"*"},{"$type":"RuleCall","rule":{"$ref":"#/rules@1"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Checkout","definition":{"$type":"Group","elements":[{"$type":"Alternatives","elements":[{"$type":"Keyword","value":"checkout"},{"$type":"Keyword","value":"switch"}]},{"$type":"Assignment","feature":"branch","operator":"=","terminal":{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@19"},"arguments":[]},{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[]}]}},{"$type":"RuleCall","rule":{"$ref":"#/rules@1"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"CherryPicking","definition":{"$type":"Group","elements":[{"$type":"Keyword","value":"cherry-pick"},{"$type":"Alternatives","elements":[{"$type":"Group","elements":[{"$type":"Keyword","value":"id:"},{"$type":"Assignment","feature":"id","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[]}}]},{"$type":"Group","elements":[{"$type":"Keyword","value":"tag:"},{"$type":"Assignment","feature":"tags","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[]}}]},{"$type":"Group","elements":[{"$type":"Keyword","value":"parent:"},{"$type":"Assignment","feature":"parent","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[]}}]}],"cardinality":"*"},{"$type":"RuleCall","rule":{"$ref":"#/rules@1"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"TerminalRule","name":"INT","type":{"$type":"ReturnType","name":"number"},"definition":{"$type":"RegexToken","regex":"/[0-9]+(?=\\\\s)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ID","type":{"$type":"ReturnType","name":"string"},"definition":{"$type":"RegexToken","regex":"/\\\\w([-\\\\./\\\\w]*[-\\\\w])?/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"STRING","definition":{"$type":"RegexToken","regex":"/\\"[^\\"]*\\"|'[^']*'/"},"fragment":false,"hidden":false}],"definesHiddenTokens":false,"hiddenTokens":[],"imports":[],"types":[],"usedGrammars":[]}`)),"GitGraphGrammar"),Q=E(()=>l??(l=y(`{"$type":"Grammar","isDeclared":true,"name":"Radar","interfaces":[{"$type":"Interface","name":"Common","attributes":[{"$type":"TypeAttribute","name":"accDescr","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}},{"$type":"TypeAttribute","name":"accTitle","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}},{"$type":"TypeAttribute","name":"title","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}}],"superTypes":[]},{"$type":"Interface","name":"Entry","attributes":[{"$type":"TypeAttribute","name":"axis","isOptional":true,"type":{"$type":"ReferenceType","referenceType":{"$type":"SimpleType","typeRef":{"$ref":"#/rules@12"}}}},{"$type":"TypeAttribute","name":"value","type":{"$type":"SimpleType","primitiveType":"number"},"isOptional":false}],"superTypes":[]}],"rules":[{"$type":"ParserRule","fragment":true,"name":"TitleAndAccessibilities","definition":{"$type":"Group","elements":[{"$type":"Alternatives","elements":[{"$type":"Assignment","feature":"accDescr","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@3"},"arguments":[]}},{"$type":"Assignment","feature":"accTitle","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@4"},"arguments":[]}},{"$type":"Assignment","feature":"title","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@5"},"arguments":[]}}]},{"$type":"RuleCall","rule":{"$ref":"#/rules@1"},"arguments":[]}],"cardinality":"+"},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"EOL","dataType":"string","definition":{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[],"cardinality":"+"},{"$type":"EndOfFile"}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"TerminalRule","name":"NEWLINE","definition":{"$type":"RegexToken","regex":"/\\\\r?\\\\n/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_DESCR","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accDescr(?:[\\\\t ]*:([^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)|\\\\s*{([^}]*)})/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accTitle[\\\\t ]*:(?:[^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*title(?:[\\\\t ][^\\\\n\\\\r]*?(?=%%)|[\\\\t ][^\\\\n\\\\r]*|)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","hidden":true,"name":"WHITESPACE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]+/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"YAML","definition":{"$type":"RegexToken","regex":"/---[\\\\t ]*\\\\r?\\\\n(?:[\\\\S\\\\s]*?\\\\r?\\\\n)?---(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"DIRECTIVE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%{[\\\\S\\\\s]*?}%%(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"SINGLE_LINE_COMMENT","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%[^\\\\n\\\\r]*/"},"fragment":false},{"$type":"ParserRule","entry":true,"name":"Radar","definition":{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[],"cardinality":"*"},{"$type":"Alternatives","elements":[{"$type":"Keyword","value":"radar-beta"},{"$type":"Keyword","value":"radar-beta:"},{"$type":"Group","elements":[{"$type":"Keyword","value":"radar-beta"},{"$type":"Keyword","value":":"}]}]},{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[],"cardinality":"*"},{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@0"},"arguments":[]},{"$type":"Group","elements":[{"$type":"Keyword","value":"axis"},{"$type":"Assignment","feature":"axes","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@12"},"arguments":[]}},{"$type":"Group","elements":[{"$type":"Keyword","value":","},{"$type":"Assignment","feature":"axes","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@12"},"arguments":[]}}],"cardinality":"*"}]},{"$type":"Group","elements":[{"$type":"Keyword","value":"curve"},{"$type":"Assignment","feature":"curves","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@13"},"arguments":[]}},{"$type":"Group","elements":[{"$type":"Keyword","value":","},{"$type":"Assignment","feature":"curves","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@13"},"arguments":[]}}],"cardinality":"*"}]},{"$type":"Group","elements":[{"$type":"Assignment","feature":"options","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}},{"$type":"Group","elements":[{"$type":"Keyword","value":","},{"$type":"Assignment","feature":"options","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}}],"cardinality":"*"}]},{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[]}],"cardinality":"*"}]},"definesHiddenTokens":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"Label","definition":{"$type":"Group","elements":[{"$type":"Keyword","value":"["},{"$type":"Assignment","feature":"label","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@22"},"arguments":[]}},{"$type":"Keyword","value":"]"}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Axis","definition":{"$type":"Group","elements":[{"$type":"Assignment","feature":"name","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@21"},"arguments":[]}},{"$type":"RuleCall","rule":{"$ref":"#/rules@11"},"arguments":[],"cardinality":"?"}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Curve","definition":{"$type":"Group","elements":[{"$type":"Assignment","feature":"name","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@21"},"arguments":[]}},{"$type":"RuleCall","rule":{"$ref":"#/rules@11"},"arguments":[],"cardinality":"?"},{"$type":"Keyword","value":"{"},{"$type":"RuleCall","rule":{"$ref":"#/rules@14"},"arguments":[]},{"$type":"Keyword","value":"}"}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"Entries","definition":{"$type":"Alternatives","elements":[{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[],"cardinality":"*"},{"$type":"Assignment","feature":"entries","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@16"},"arguments":[]}},{"$type":"Group","elements":[{"$type":"Keyword","value":","},{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[],"cardinality":"*"},{"$type":"Assignment","feature":"entries","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@16"},"arguments":[]}}],"cardinality":"*"},{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[],"cardinality":"*"}]},{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[],"cardinality":"*"},{"$type":"Assignment","feature":"entries","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@15"},"arguments":[]}},{"$type":"Group","elements":[{"$type":"Keyword","value":","},{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[],"cardinality":"*"},{"$type":"Assignment","feature":"entries","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@15"},"arguments":[]}}],"cardinality":"*"},{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[],"cardinality":"*"}]}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"DetailedEntry","returnType":{"$ref":"#/interfaces@1"},"definition":{"$type":"Group","elements":[{"$type":"Assignment","feature":"axis","operator":"=","terminal":{"$type":"CrossReference","type":{"$ref":"#/rules@12"},"terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@21"},"arguments":[]},"deprecatedSyntax":false}},{"$type":"Keyword","value":":","cardinality":"?"},{"$type":"Assignment","feature":"value","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@18"},"arguments":[]}}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"NumberEntry","returnType":{"$ref":"#/interfaces@1"},"definition":{"$type":"Assignment","feature":"value","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@18"},"arguments":[]}},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Option","definition":{"$type":"Alternatives","elements":[{"$type":"Group","elements":[{"$type":"Assignment","feature":"name","operator":"=","terminal":{"$type":"Keyword","value":"showLegend"}},{"$type":"Assignment","feature":"value","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@19"},"arguments":[]}}]},{"$type":"Group","elements":[{"$type":"Assignment","feature":"name","operator":"=","terminal":{"$type":"Keyword","value":"ticks"}},{"$type":"Assignment","feature":"value","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@18"},"arguments":[]}}]},{"$type":"Group","elements":[{"$type":"Assignment","feature":"name","operator":"=","terminal":{"$type":"Keyword","value":"max"}},{"$type":"Assignment","feature":"value","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@18"},"arguments":[]}}]},{"$type":"Group","elements":[{"$type":"Assignment","feature":"name","operator":"=","terminal":{"$type":"Keyword","value":"min"}},{"$type":"Assignment","feature":"value","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@18"},"arguments":[]}}]},{"$type":"Group","elements":[{"$type":"Assignment","feature":"name","operator":"=","terminal":{"$type":"Keyword","value":"graticule"}},{"$type":"Assignment","feature":"value","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[]}}]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"TerminalRule","name":"NUMBER","type":{"$type":"ReturnType","name":"number"},"definition":{"$type":"RegexToken","regex":"/(0|[1-9][0-9]*)(\\\\.[0-9]+)?/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"BOOLEAN","type":{"$type":"ReturnType","name":"boolean"},"definition":{"$type":"TerminalAlternatives","elements":[{"$type":"CharacterRange","left":{"$type":"Keyword","value":"true"}},{"$type":"CharacterRange","left":{"$type":"Keyword","value":"false"}}]},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"GRATICULE","type":{"$type":"ReturnType","name":"string"},"definition":{"$type":"TerminalAlternatives","elements":[{"$type":"CharacterRange","left":{"$type":"Keyword","value":"circle"}},{"$type":"CharacterRange","left":{"$type":"Keyword","value":"polygon"}}]},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ID","type":{"$type":"ReturnType","name":"string"},"definition":{"$type":"RegexToken","regex":"/[a-zA-Z_][a-zA-Z0-9\\\\-_]*/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"STRING","definition":{"$type":"RegexToken","regex":"/\\"[^\\"]*\\"|'[^']*'/"},"fragment":false,"hidden":false}],"definesHiddenTokens":false,"hiddenTokens":[],"imports":[],"types":[],"usedGrammars":[]}`)),"RadarGrammar"),J={languageId:"info",fileExtensions:[".mmd",".mermaid"],caseInsensitive:!1,mode:"production"},ee={languageId:"packet",fileExtensions:[".mmd",".mermaid"],caseInsensitive:!1,mode:"production"},et={languageId:"pie",fileExtensions:[".mmd",".mermaid"],caseInsensitive:!1,mode:"production"},er={languageId:"architecture",fileExtensions:[".mmd",".mermaid"],caseInsensitive:!1,mode:"production"},en={languageId:"gitGraph",fileExtensions:[".mmd",".mermaid"],caseInsensitive:!1,mode:"production"},ei={languageId:"radar",fileExtensions:[".mmd",".mermaid"],caseInsensitive:!1,mode:"production"},ea={AstReflection:E(()=>new j,"AstReflection")},es={Grammar:E(()=>H(),"Grammar"),LanguageMetaData:E(()=>J,"LanguageMetaData"),parser:{}},eo={Grammar:E(()=>z(),"Grammar"),LanguageMetaData:E(()=>ee,"LanguageMetaData"),parser:{}},el={Grammar:E(()=>Y(),"Grammar"),LanguageMetaData:E(()=>et,"LanguageMetaData"),parser:{}},eu={Grammar:E(()=>q(),"Grammar"),LanguageMetaData:E(()=>er,"LanguageMetaData"),parser:{}},ec={Grammar:E(()=>X(),"Grammar"),LanguageMetaData:E(()=>en,"LanguageMetaData"),parser:{}},ed={Grammar:E(()=>Q(),"Grammar"),LanguageMetaData:E(()=>ei,"LanguageMetaData"),parser:{}},eh={ACC_DESCR:/accDescr(?:[\t ]*:([^\n\r]*)|\s*{([^}]*)})/,ACC_TITLE:/accTitle[\t ]*:([^\n\r]*)/,TITLE:/title([\t ][^\n\r]*|)/},ef=class extends T.t{static{E(this,"AbstractMermaidValueConverter")}runConverter(e,t,r){let n=this.runCommonConverter(e,t,r);return(void 0===n&&(n=this.runCustomConverter(e,t,r)),void 0===n)?super.runConverter(e,t,r):n}runCommonConverter(e,t,r){let n=eh[e.name];if(void 0===n)return;let i=n.exec(t);if(null!==i){if(void 0!==i[1])return i[1].trim().replace(/[\t ]{2,}/gm," ");if(void 0!==i[2])return i[2].replace(/^\s*/gm,"").replace(/\s+$/gm,"").replace(/[\t ]{2,}/gm," ").replace(/[\n\r]{2,}/gm,"\n")}}},ep=class extends ef{static{E(this,"CommonValueConverter")}runCustomConverter(e,t,r){}},em=class extends v.P{static{E(this,"AbstractMermaidTokenBuilder")}constructor(e){super(),this.keywords=new Set(e)}buildKeywordTokens(e,t,r){let n=super.buildKeywordTokens(e,t,r);return n.forEach(e=>{this.keywords.has(e.name)&&void 0!==e.PATTERN&&(e.PATTERN=RegExp(e.PATTERN.toString()+"(?:(?=%%)|(?!\\S))"))}),n}};(class extends null{static{E(this,"CommonTokenBuilder")}})},1041:function(e,t,r){r.d(t,{i:()=>c});var n=r(36902),i=r(62297),a=r(29266),s=r(92910),o=class extends n.T7{static{(0,n.eW)(this,"ArchitectureTokenBuilder")}constructor(){super(["architecture"])}},l=class extends n.kb{static{(0,n.eW)(this,"ArchitectureValueConverter")}runCustomConverter(e,t,r){return"ARCH_ICON"===e.name?t.replace(/[()]/g,"").trim():"ARCH_TEXT_ICON"===e.name?t.replace(/["()]/g,""):"ARCH_TITLE"===e.name?t.replace(/[[\]]/g,"").trim():void 0}},u={parser:{TokenBuilder:(0,n.eW)(()=>new o,"TokenBuilder"),ValueConverter:(0,n.eW)(()=>new l,"ValueConverter")}};function c(e=i.u){let t=(0,a.f3)((0,s.T)(e),n.GS),r=(0,a.f3)((0,s.Q)({shared:t}),n.Qr,u);return t.ServiceRegistry.register(r),{shared:t,Architecture:r}}(0,n.eW)(c,"createArchitectureServices")},64244:function(e,t,r){r.d(t,{M:()=>u});var n=r(36902),i=r(62297),a=r(29266),s=r(92910),o=class extends n.T7{static{(0,n.eW)(this,"InfoTokenBuilder")}constructor(){super(["info","showInfo"])}},l={parser:{TokenBuilder:(0,n.eW)(()=>new o,"TokenBuilder"),ValueConverter:(0,n.eW)(()=>new n.nr,"ValueConverter")}};function u(e=i.u){let t=(0,a.f3)((0,s.T)(e),n.GS),r=(0,a.f3)((0,s.Q)({shared:t}),n.F_,l);return t.ServiceRegistry.register(r),{shared:t,Info:r}}(0,n.eW)(u,"createInfoServices")},91432:function(e,t,r){r.d(t,{l:()=>c});var n=r(36902),i=r(62297),a=r(29266),s=r(92910),o=class extends n.T7{static{(0,n.eW)(this,"PieTokenBuilder")}constructor(){super(["pie","showData"])}},l=class extends n.kb{static{(0,n.eW)(this,"PieValueConverter")}runCustomConverter(e,t,r){if("PIE_SECTION_LABEL"===e.name)return t.replace(/"/g,"").trim()}},u={parser:{TokenBuilder:(0,n.eW)(()=>new o,"TokenBuilder"),ValueConverter:(0,n.eW)(()=>new l,"ValueConverter")}};function c(e=i.u){let t=(0,a.f3)((0,s.T)(e),n.GS),r=(0,a.f3)((0,s.Q)({shared:t}),n.WH,u);return t.ServiceRegistry.register(r),{shared:t,Pie:r}}(0,n.eW)(c,"createPieServices")},93919:function(e,t,r){r.d(t,{g:()=>u});var n=r(36902),i=r(62297),a=r(29266),s=r(92910),o=class extends n.T7{static{(0,n.eW)(this,"PacketTokenBuilder")}constructor(){super(["packet-beta"])}},l={parser:{TokenBuilder:(0,n.eW)(()=>new o,"TokenBuilder"),ValueConverter:(0,n.eW)(()=>new n.nr,"ValueConverter")}};function u(e=i.u){let t=(0,a.f3)((0,s.T)(e),n.GS),r=(0,a.f3)((0,s.Q)({shared:t}),n.bb,l);return t.ServiceRegistry.register(r),{shared:t,Packet:r}}(0,n.eW)(u,"createPacketServices")},85653:function(e,t,r){r.d(t,{Qc:()=>s}),r(56196),r(64244),r(93919),r(91432),r(1041),r(54372);var n=r(36902),i={},a={info:(0,n.eW)(async()=>{let{createInfoServices:e}=await r.e("751").then(r.bind(r,85728));i.info=e().Info.parser.LangiumParser},"info"),packet:(0,n.eW)(async()=>{let{createPacketServices:e}=await r.e("953").then(r.bind(r,35789));i.packet=e().Packet.parser.LangiumParser},"packet"),pie:(0,n.eW)(async()=>{let{createPieServices:e}=await r.e("957").then(r.bind(r,56006));i.pie=e().Pie.parser.LangiumParser},"pie"),architecture:(0,n.eW)(async()=>{let{createArchitectureServices:e}=await r.e("828").then(r.bind(r,45045));i.architecture=e().Architecture.parser.LangiumParser},"architecture"),gitGraph:(0,n.eW)(async()=>{let{createGitGraphServices:e}=await r.e("503").then(r.bind(r,66487));i.gitGraph=e().GitGraph.parser.LangiumParser},"gitGraph"),radar:(0,n.eW)(async()=>{let{createRadarServices:e}=await r.e("113").then(r.bind(r,86619));i.radar=e().Radar.parser.LangiumParser},"radar")};async function s(e,t){let r=a[e];if(!r)throw Error(`Unknown diagram type: ${e}`);i[e]||await r();let n=i[e].parse(t);if(n.lexerErrors.length>0||n.parserErrors.length>0)throw new o(n);return n.value}(0,n.eW)(s,"parse");var o=class extends Error{constructor(e){super(`Parsing failed: ${e.lexerErrors.map(e=>e.message).join("\n")} ${e.parserErrors.map(e=>e.message).join("\n")}`),this.result=e}static{(0,n.eW)(this,"MermaidParseError")}}},84376:function(e,t,r){r.d(t,{ol:()=>tD,ue:()=>X,sd:()=>tP,Wx:()=>W,l$:()=>t$,ej:()=>H,pT:()=>q,oC:()=>t3,nu:()=>rH,ZW:()=>tE,hI:()=>Y,Hs:()=>tZ,oI:()=>Q,hW:()=>tk,fK:()=>z,_o:()=>rj,dV:()=>rS,Sj:()=>V});var n,i,a,s,o,l,u,c,d,h=r(84458),f=r(80935),p=r(46471),m=r(47191),g=r(40290),y=r(1209);function T(e){function t(){}t.prototype=e;let r=new t;function n(){return typeof r.bar}return n(),n(),e}let v=function(e,t,r){var n=-1,i=e.length;t<0&&(t=-t>i?0:i+t),(r=r>i?i:r)<0&&(r+=i),i=t>r?0:r-t>>>0,t>>>=0;for(var a=Array(i);++n<i;)a[n]=e[n+t];return a};var R=r(47527);let E=function(e,t,r){var n=null==e?0:e.length;return n?v(e,(t=r||void 0===t?1:(0,R.Z)(t))<0?0:t,n):[]};var A=r(88514),k=r(26028),$=r(45169),x=r(48926),I=r(67737),S=r(98168),C=r(71257),N=Object.prototype.hasOwnProperty,w=(0,x.Z)(function(e,t){if((0,S.Z)(t)||(0,I.Z)(t))return void(0,$.Z)(t,(0,C.Z)(t),e);for(var r in t)N.call(t,r)&&(0,k.Z)(e,r,t[r])}),L=r(75952),b=r(36616),O=r(63519),_=r(80778);let P=function(e,t){if(null==e)return{};var r=(0,L.Z)((0,_.Z)(e),function(e){return[e]});return t=(0,b.Z)(t),(0,O.Z)(e,r,function(e,r){return t(e,r[0])})};var M=r(21452),D=r(32398),Z=r(94421),F=r(41394),U=F.Z&&F.Z.isRegExp,G=U?(0,Z.Z)(U):function(e){return(0,D.Z)(e)&&"[object RegExp]"==(0,M.Z)(e)};class B{get definition(){return this._definition}set definition(e){this._definition=e}constructor(e){this._definition=e}accept(e){e.visit(this),(0,h.Z)(this.definition,t=>{t.accept(e)})}}class V extends B{constructor(e){super([]),this.idx=1,w(this,P(e,e=>void 0!==e))}set definition(e){}get definition(){return void 0!==this.referencedRule?this.referencedRule.definition:[]}accept(e){e.visit(this)}}class K extends B{constructor(e){super(e.definition),this.orgText="",w(this,P(e,e=>void 0!==e))}}class j extends B{constructor(e){super(e.definition),this.ignoreAmbiguities=!1,w(this,P(e,e=>void 0!==e))}}class W extends B{constructor(e){super(e.definition),this.idx=1,w(this,P(e,e=>void 0!==e))}}class H extends B{constructor(e){super(e.definition),this.idx=1,w(this,P(e,e=>void 0!==e))}}class z extends B{constructor(e){super(e.definition),this.idx=1,w(this,P(e,e=>void 0!==e))}}class Y extends B{constructor(e){super(e.definition),this.idx=1,w(this,P(e,e=>void 0!==e))}}class q extends B{constructor(e){super(e.definition),this.idx=1,w(this,P(e,e=>void 0!==e))}}class X extends B{get definition(){return this._definition}set definition(e){this._definition=e}constructor(e){super(e.definition),this.idx=1,this.ignoreAmbiguities=!1,this.hasPredicates=!1,w(this,P(e,e=>void 0!==e))}}class Q{constructor(e){this.idx=1,w(this,P(e,e=>void 0!==e))}accept(e){e.visit(this)}}class J{visit(e){switch(e.constructor){case V:return this.visitNonTerminal(e);case j:return this.visitAlternative(e);case W:return this.visitOption(e);case H:return this.visitRepetitionMandatory(e);case z:return this.visitRepetitionMandatoryWithSeparator(e);case q:return this.visitRepetitionWithSeparator(e);case Y:return this.visitRepetition(e);case X:return this.visitAlternation(e);case Q:return this.visitTerminal(e);case K:return this.visitRule(e);default:throw Error("non exhaustive match")}}visitNonTerminal(e){}visitAlternative(e){}visitOption(e){}visitRepetition(e){}visitRepetitionMandatory(e){}visitRepetitionMandatoryWithSeparator(e){}visitRepetitionWithSeparator(e){}visitAlternation(e){}visitTerminal(e){}visitRule(e){}}var ee=r(35477),et=r(61411);let er=function(e,t){var r;return(0,et.Z)(e,function(e,n,i){return!(r=t(e,n,i))}),!!r};var en=r(3073),ei=r(99302);let ea=function(e,t,r){var n=(0,en.Z)(e)?ee.Z:er;return r&&(0,ei.Z)(e,t,r)&&(t=void 0),n(e,(0,b.Z)(t,3))};var es=r(10239),eo=Math.max;let el=function(e,t,r,n){e=(0,I.Z)(e)?e:(0,f.Z)(e),r=r&&!n?(0,R.Z)(r):0;var i=e.length;return r<0&&(r=eo(i+r,0)),(0,A.Z)(e)?r<=i&&e.indexOf(t,r)>-1:!!i&&(0,es.Z)(e,t,r)>-1},eu=function(e,t){for(var r=-1,n=null==e?0:e.length;++r<n;)if(!t(e[r],r,e))return!1;return!0},ec=function(e,t){var r=!0;return(0,et.Z)(e,function(e,n,i){return r=!!t(e,n,i)}),r},ed=function(e,t,r){var n=(0,en.Z)(e)?eu:ec;return r&&(0,ei.Z)(e,t,r)&&(t=void 0),n(e,(0,b.Z)(t,3))};function eh(e,t=[]){return e instanceof W||e instanceof Y||e instanceof q||(e instanceof X?ea(e.definition,e=>eh(e,t)):!(e instanceof V&&el(t,e))&&e instanceof B&&(e instanceof V&&t.push(e),ed(e.definition,e=>eh(e,t))))}function ef(e){if(e instanceof V)return"SUBRULE";if(e instanceof W)return"OPTION";if(e instanceof X)return"OR";if(e instanceof H)return"AT_LEAST_ONE";if(e instanceof z)return"AT_LEAST_ONE_SEP";else if(e instanceof q)return"MANY_SEP";else if(e instanceof Y)return"MANY";else if(e instanceof Q)return"CONSUME";else throw Error("non exhaustive match")}class ep{walk(e,t=[]){(0,h.Z)(e.definition,(r,n)=>{let i=E(e.definition,n+1);if(r instanceof V)this.walkProdRef(r,i,t);else if(r instanceof Q)this.walkTerminal(r,i,t);else if(r instanceof j)this.walkFlat(r,i,t);else if(r instanceof W)this.walkOption(r,i,t);else if(r instanceof H)this.walkAtLeastOne(r,i,t);else if(r instanceof z)this.walkAtLeastOneSep(r,i,t);else if(r instanceof q)this.walkManySep(r,i,t);else if(r instanceof Y)this.walkMany(r,i,t);else if(r instanceof X)this.walkOr(r,i,t);else throw Error("non exhaustive match")})}walkTerminal(e,t,r){}walkProdRef(e,t,r){}walkFlat(e,t,r){let n=t.concat(r);this.walk(e,n)}walkOption(e,t,r){let n=t.concat(r);this.walk(e,n)}walkAtLeastOne(e,t,r){let n=[new W({definition:e.definition})].concat(t,r);this.walk(e,n)}walkAtLeastOneSep(e,t,r){let n=em(e,t,r);this.walk(e,n)}walkMany(e,t,r){let n=[new W({definition:e.definition})].concat(t,r);this.walk(e,n)}walkManySep(e,t,r){let n=em(e,t,r);this.walk(e,n)}walkOr(e,t,r){let n=t.concat(r);(0,h.Z)(e.definition,e=>{let t=new j({definition:[e]});this.walk(t,n)})}}function em(e,t,r){return[new W({definition:[new Q({terminalType:e.separator})].concat(e.definition)})].concat(t,r)}var eg=r(18020);let ey=function(e){return e&&e.length?(0,eg.Z)(e):[]};var eT=r(74590);function ev(e){if(e instanceof V)return ev(e.referencedRule);if(e instanceof Q)return[e.terminalType];if(e instanceof j||e instanceof W||e instanceof Y||e instanceof H||e instanceof z||e instanceof q||e instanceof Q||e instanceof K)return function(e){let t,r=[],n=e.definition,i=0,a=n.length>i,s=!0;for(;a&&s;)s=eh(t=n[i]),r=r.concat(ev(t)),i+=1,a=n.length>i;return ey(r)}(e);if(e instanceof X){var t=e;let r=(0,m.Z)(t.definition,e=>ev(e));return ey((0,eT.Z)(r))}throw Error("non exhaustive match")}let eR="_~IN~_";class eE extends ep{constructor(e){super(),this.topProd=e,this.follows={}}startWalking(){return this.walk(this.topProd),this.follows}walkTerminal(e,t,r){}walkProdRef(e,t,r){var n,i;let a=(n=e.referencedRule,i=e.idx,n.name+i+eR+this.topProd.name),s=ev(new j({definition:t.concat(r)}));this.follows[a]=s}}var eA=r(8321),ek=r(51026),e$=r(27272),ex=r(46418),eI=r(34963);let eS=function(e){if("function"!=typeof e)throw TypeError("Expected a function");return function(){var t=arguments;switch(t.length){case 0:return!e.call(this);case 1:return!e.call(this,t[0]);case 2:return!e.call(this,t[0],t[1]);case 3:return!e.call(this,t[0],t[1],t[2])}return!e.apply(this,t)}},eC=function(e,t){return((0,en.Z)(e)?ex.Z:eI.Z)(e,eS((0,b.Z)(t,3)))};var eN=r(84015),ew=Math.max;let eL=function(e,t,r){var n=null==e?0:e.length;if(!n)return -1;var i=null==r?0:(0,R.Z)(r);return i<0&&(i=ew(n+i,0)),(0,es.Z)(e,t,i)};var eb=r(65457),eO=r(67998),e_=r(6623),eP=r(43387),eM=r(76973),eD=r(43137);let eZ=function(e,t,r,n){var i=-1,a=eP.Z,s=!0,o=e.length,l=[],u=t.length;if(!o)return l;r&&(t=(0,L.Z)(t,(0,Z.Z)(r))),n?(a=eM.Z,s=!1):t.length>=200&&(a=eD.Z,s=!1,t=new e_.Z(t));e:for(;++i<o;){var c=e[i],d=null==r?c:r(c);if(c=n||0!==c?c:0,s&&d==d){for(var h=u;h--;)if(t[h]===d)continue e;l.push(c)}else a(t,d,n)||l.push(c)}return l};var eF=r(27796),eU=r(11021),eG=r(91631),eB=(0,eU.Z)(function(e,t){return(0,eG.Z)(e)?eZ(e,(0,eF.Z)(t,1,eG.Z,!0)):[]});let eV=function(e){for(var t=-1,r=null==e?0:e.length,n=0,i=[];++t<r;){var a=e[t];a&&(i[n++]=a)}return i},eK=function(e){return e&&e.length?e[0]:void 0};var ej=r(90083);function eW(e){console&&console.error&&console.error(`Error: ${e}`)}function eH(e){console&&console.warn&&console.warn(`Warning: ${e}`)}let ez={},eY=new ek.O;function eq(e){let t=e.toString();if(ez.hasOwnProperty(t))return ez[t];{let e=eY.pattern(t);return ez[t]=e,e}}let eX="Complement Sets are not supported for first char optimization",eQ='Unable to use "first char" lexer optimizations:\n';function eJ(e,t,r){let n=tl(e);t[n]=n,!0===r&&function(e,t){let r=String.fromCharCode(e),n=r.toUpperCase();if(n!==r){let e=tl(n.charCodeAt(0));t[e]=e}else{let e=r.toLowerCase();if(e!==r){let r=tl(e.charCodeAt(0));t[r]=r}}}(e,t)}function e0(e,t){return(0,ej.Z)(e.value,e=>"number"==typeof e?el(t,e):void 0!==(0,ej.Z)(t,t=>e.from<=t&&t<=e.to))}class e1 extends ek.e{constructor(e){super(),this.targetCharCodes=e,this.found=!1}visitChildren(e){if(!0!==this.found){switch(e.type){case"Lookahead":this.visitLookahead(e);return;case"NegativeLookahead":this.visitNegativeLookahead(e);return}super.visitChildren(e)}}visitCharacter(e){el(this.targetCharCodes,e.value)&&(this.found=!0)}visitSet(e){e.complement?void 0===e0(e,this.targetCharCodes)&&(this.found=!0):void 0!==e0(e,this.targetCharCodes)&&(this.found=!0)}}function e2(e,t){if(!(t instanceof RegExp))return void 0!==(0,ej.Z)(t,t=>el(e,t.charCodeAt(0)));{let r=eq(t),n=new e1(e);return n.visit(r),n.found}}let e6="PATTERN",e9="defaultMode",e3="modes",e7="boolean"==typeof RegExp("(?:)").sticky,e4=/[^\\][$]/,e5=/[^\\[][\^]|^\^/;function e8(e){let t=e.ignoreCase?"i":"";return RegExp(`^(?:${e.source})`,t)}function te(e){let t=e.ignoreCase?"iy":"y";return RegExp(`${e.source}`,t)}function tt(e){let t=e.PATTERN;if(G(t))return!1;if((0,eN.Z)(t))return!0;if((0,g.Z)(t,"exec"))return!0;if((0,A.Z)(t))return!1;throw Error("non exhaustive match")}function tr(e){return!!(0,A.Z)(e)&&1===e.length&&e.charCodeAt(0)}let tn={test:function(e){let t=e.length;for(let r=this.lastIndex;r<t;r++){let t=e.charCodeAt(r);if(10===t)return this.lastIndex=r+1,!0;if(13===t)return 10===e.charCodeAt(r+1)?this.lastIndex=r+2:this.lastIndex=r+1,!0}return!1},lastIndex:0};function ti(e,t){if((0,g.Z)(e,"LINE_BREAKS"))return!1;if(G(e.PATTERN)){try{e2(t,e.PATTERN)}catch(e){return{issue:l.IDENTIFY_TERMINATOR,errMsg:e.message}}return!1}if((0,A.Z)(e.PATTERN))return!1;if(tt(e))return{issue:l.CUSTOM_LINE_BREAK};throw Error("non exhaustive match")}function ta(e){return(0,m.Z)(e,e=>(0,A.Z)(e)?e.charCodeAt(0):e)}function ts(e,t,r){void 0===e[t]?e[t]=[r]:e[t].push(r)}let to=[];function tl(e){return e<256?e:to[e]}var tu=r(85627),tc=r(20997),td=r(90437);function th(e){let t=new Date().getTime(),r=e();return{time:new Date().getTime()-t,value:r}}function tf(e,t){let r=e.tokenTypeIdx;return r===t.tokenTypeIdx||!0===t.isParent&&!0===t.categoryMatchesMap[r]}function tp(e,t){return e.tokenTypeIdx===t.tokenTypeIdx}let tm=1,tg={};function ty(e){var t,r,n;let i=function(e){let t=(0,y.Z)(e),r=e,n=!0;for(;n;){let e=eB(r=eV((0,eT.Z)((0,m.Z)(r,e=>e.CATEGORIES))),t);t=t.concat(e),(0,p.Z)(e)?n=!1:r=e}return t}(e);t=i,(0,h.Z)(t,e=>{var t,r;tT(e)||(tg[tm]=e,e.tokenTypeIdx=tm++),tv(e)&&!(0,en.Z)(e.CATEGORIES)&&(e.CATEGORIES=[e.CATEGORIES]),tv(e)||(e.CATEGORIES=[]),t=e,(0,g.Z)(t,"categoryMatches")||(e.categoryMatches=[]),r=e,(0,g.Z)(r,"categoryMatchesMap")||(e.categoryMatchesMap={})}),r=i,(0,h.Z)(r,e=>{!function e(t,r){(0,h.Z)(t,e=>{r.categoryMatchesMap[e.tokenTypeIdx]=!0}),(0,h.Z)(r.CATEGORIES,n=>{let i=t.concat(r);el(i,n)||e(i,n)})}([],e)}),n=i,(0,h.Z)(n,e=>{e.categoryMatches=[],(0,h.Z)(e.categoryMatchesMap,(t,r)=>{e.categoryMatches.push(tg[r].tokenTypeIdx)})}),(0,h.Z)(i,e=>{e.isParent=e.categoryMatches.length>0})}function tT(e){return(0,g.Z)(e,"tokenTypeIdx")}function tv(e){return(0,g.Z)(e,"CATEGORIES")}function tR(e){return(0,g.Z)(e,"tokenTypeIdx")}let tE={buildUnableToPopLexerModeMessage:e=>`Unable to pop Lexer Mode after encountering Token ->${e.image}<- The Mode Stack is empty`,buildUnexpectedCharactersMessage:(e,t,r,n,i)=>`unexpected character: ->${e.charAt(t)}<- at offset: ${t}, skipped ${r} characters.`};(n=l||(l={}))[n.MISSING_PATTERN=0]="MISSING_PATTERN",n[n.INVALID_PATTERN=1]="INVALID_PATTERN",n[n.EOI_ANCHOR_FOUND=2]="EOI_ANCHOR_FOUND",n[n.UNSUPPORTED_FLAGS_FOUND=3]="UNSUPPORTED_FLAGS_FOUND",n[n.DUPLICATE_PATTERNS_FOUND=4]="DUPLICATE_PATTERNS_FOUND",n[n.INVALID_GROUP_TYPE_FOUND=5]="INVALID_GROUP_TYPE_FOUND",n[n.PUSH_MODE_DOES_NOT_EXIST=6]="PUSH_MODE_DOES_NOT_EXIST",n[n.MULTI_MODE_LEXER_WITHOUT_DEFAULT_MODE=7]="MULTI_MODE_LEXER_WITHOUT_DEFAULT_MODE",n[n.MULTI_MODE_LEXER_WITHOUT_MODES_PROPERTY=8]="MULTI_MODE_LEXER_WITHOUT_MODES_PROPERTY",n[n.MULTI_MODE_LEXER_DEFAULT_MODE_VALUE_DOES_NOT_EXIST=9]="MULTI_MODE_LEXER_DEFAULT_MODE_VALUE_DOES_NOT_EXIST",n[n.LEXER_DEFINITION_CANNOT_CONTAIN_UNDEFINED=10]="LEXER_DEFINITION_CANNOT_CONTAIN_UNDEFINED",n[n.SOI_ANCHOR_FOUND=11]="SOI_ANCHOR_FOUND",n[n.EMPTY_MATCH_PATTERN=12]="EMPTY_MATCH_PATTERN",n[n.NO_LINE_BREAKS_FLAGS=13]="NO_LINE_BREAKS_FLAGS",n[n.UNREACHABLE_PATTERN=14]="UNREACHABLE_PATTERN",n[n.IDENTIFY_TERMINATOR=15]="IDENTIFY_TERMINATOR",n[n.CUSTOM_LINE_BREAK=16]="CUSTOM_LINE_BREAK",n[n.MULTI_MODE_LEXER_LONGER_ALT_NOT_IN_CURRENT_MODE=17]="MULTI_MODE_LEXER_LONGER_ALT_NOT_IN_CURRENT_MODE";let tA={deferDefinitionErrorsHandling:!1,positionTracking:"full",lineTerminatorsPattern:/\n|\r\n?/g,lineTerminatorCharacters:["\n","\r"],ensureOptimizations:!1,safeMode:!1,errorMessageProvider:tE,traceInitPerf:!1,skipValidations:!1,recoveryEnabled:!0};Object.freeze(tA);class tk{constructor(e,t=tA){if(this.lexerDefinition=e,this.lexerDefinitionErrors=[],this.lexerDefinitionWarning=[],this.patternIdxToConfig={},this.charCodeToPatternIdxToConfig={},this.modes=[],this.emptyGroups={},this.trackStartLines=!0,this.trackEndLines=!0,this.hasCustom=!1,this.canModeBeOptimized={},this.TRACE_INIT=(e,t)=>{if(!0!==this.traceInitPerf)return t();{this.traceInitIndent++;let r=Array(this.traceInitIndent+1).join("	");this.traceInitIndent<this.traceInitMaxIdent&&console.log(`${r}--> <${e}>`);let{time:n,value:i}=th(t),a=n>10?console.warn:console.log;return this.traceInitIndent<this.traceInitMaxIdent&&a(`${r}<-- <${e}> time: ${n}ms`),this.traceInitIndent--,i}},"boolean"==typeof t)throw Error("The second argument to the Lexer constructor is now an ILexerConfig Object.\na boolean 2nd argument is no longer supported");this.config=w({},tA,t);let r=this.config.traceInitPerf;!0===r?(this.traceInitMaxIdent=1/0,this.traceInitPerf=!0):"number"==typeof r&&(this.traceInitMaxIdent=r,this.traceInitPerf=!0),this.traceInitIndent=-1,this.TRACE_INIT("Lexer Constructor",()=>{let r,n=!0;this.TRACE_INIT("Lexer Config handling",()=>{if(this.config.lineTerminatorsPattern===tA.lineTerminatorsPattern)this.config.lineTerminatorsPattern=tn;else if(this.config.lineTerminatorCharacters===tA.lineTerminatorCharacters)throw Error("Error: Missing <lineTerminatorCharacters> property on the Lexer config.\n	For details See: https://chevrotain.io/docs/guide/resolving_lexer_errors.html#MISSING_LINE_TERM_CHARS");if(t.safeMode&&t.ensureOptimizations)throw Error('"safeMode" and "ensureOptimizations" flags are mutually exclusive.');this.trackStartLines=/full|onlyStart/i.test(this.config.positionTracking),this.trackEndLines=/full/i.test(this.config.positionTracking),(0,en.Z)(e)?r={modes:{defaultMode:(0,y.Z)(e)},defaultMode:e9}:(n=!1,r=(0,y.Z)(e))}),!1===this.config.skipValidations&&(this.TRACE_INIT("performRuntimeChecks",()=>{this.lexerDefinitionErrors=this.lexerDefinitionErrors.concat(function(e,t,r){let n=[];return(0,g.Z)(e,e9)||n.push({message:"A MultiMode Lexer cannot be initialized without a <"+e9+"> property in its definition\n",type:l.MULTI_MODE_LEXER_WITHOUT_DEFAULT_MODE}),(0,g.Z)(e,e3)||n.push({message:"A MultiMode Lexer cannot be initialized without a <"+e3+"> property in its definition\n",type:l.MULTI_MODE_LEXER_WITHOUT_MODES_PROPERTY}),(0,g.Z)(e,e3)&&(0,g.Z)(e,e9)&&!(0,g.Z)(e.modes,e.defaultMode)&&n.push({message:`A MultiMode Lexer cannot be initialized with a ${e9}: <${e.defaultMode}>which does not exist
`,type:l.MULTI_MODE_LEXER_DEFAULT_MODE_VALUE_DOES_NOT_EXIST}),(0,g.Z)(e,e3)&&(0,h.Z)(e.modes,(e,t)=>{(0,h.Z)(e,(r,i)=>{if((0,eA.Z)(r))n.push({message:`A Lexer cannot be initialized using an undefined Token Type. Mode:<${t}> at index: <${i}>
`,type:l.LEXER_DEFINITION_CANNOT_CONTAIN_UNDEFINED});else if((0,g.Z)(r,"LONGER_ALT")){let i=(0,en.Z)(r.LONGER_ALT)?r.LONGER_ALT:[r.LONGER_ALT];(0,h.Z)(i,i=>{(0,eA.Z)(i)||el(e,i)||n.push({message:`A MultiMode Lexer cannot be initialized with a longer_alt <${i.name}> on token <${r.name}> outside of mode <${t}>
`,type:l.MULTI_MODE_LEXER_LONGER_ALT_NOT_IN_CURRENT_MODE})})}})}),n}(r,this.trackStartLines,this.config.lineTerminatorCharacters))}),this.TRACE_INIT("performWarningRuntimeChecks",()=>{this.lexerDefinitionWarning=this.lexerDefinitionWarning.concat(function(e,t,r){let n=[],i=!1,a=eC(eV((0,eT.Z)((0,f.Z)(e.modes))),e=>e[e6]===tk.NA),s=ta(r);return t&&(0,h.Z)(a,e=>{let t=ti(e,s);if(!1!==t){let r={message:function(e,t){if(t.issue===l.IDENTIFY_TERMINATOR)return`Warning: unable to identify line terminator usage in pattern.
	The problem is in the <${e.name}> Token Type
	 Root cause: ${t.errMsg}.
	For details See: https://chevrotain.io/docs/guide/resolving_lexer_errors.html#IDENTIFY_TERMINATOR`;if(t.issue===l.CUSTOM_LINE_BREAK)return`Warning: A Custom Token Pattern should specify the <line_breaks> option.
	The problem is in the <${e.name}> Token Type
	For details See: https://chevrotain.io/docs/guide/resolving_lexer_errors.html#CUSTOM_LINE_BREAK`;throw Error("non exhaustive match")}(e,t),type:t.issue,tokenType:e};n.push(r)}else(0,g.Z)(e,"LINE_BREAKS")?!0===e.LINE_BREAKS&&(i=!0):e2(s,e.PATTERN)&&(i=!0)}),t&&!i&&n.push({message:"Warning: No LINE_BREAKS Found.\n	This Lexer has been defined to track line and column information,\n	But none of the Token Types can be identified as matching a line terminator.\n	See https://chevrotain.io/docs/guide/resolving_lexer_errors.html#LINE_BREAKS \n	for details.",type:l.NO_LINE_BREAKS_FLAGS}),n}(r,this.trackStartLines,this.config.lineTerminatorCharacters))})),r.modes=r.modes?r.modes:{},(0,h.Z)(r.modes,(e,t)=>{r.modes[t]=eC(e,e=>(0,eA.Z)(e))});let i=(0,C.Z)(r.modes);if((0,h.Z)(r.modes,(e,r)=>{this.TRACE_INIT(`Mode: <${r}> processing`,()=>{if(this.modes.push(r),!1===this.config.skipValidations&&this.TRACE_INIT("validatePatterns",()=>{this.lexerDefinitionErrors=this.lexerDefinitionErrors.concat(function(e,t){let r=[],n=function(e){let t=(0,eO.Z)(e,e=>!(0,g.Z)(e,e6));return{errors:(0,m.Z)(t,e=>({message:"Token Type: ->"+e.name+"<- missing static 'PATTERN' property",type:l.MISSING_PATTERN,tokenTypes:[e]})),valid:eB(e,t)}}(e);r=r.concat(n.errors);let i=function(e){let t=(0,eO.Z)(e,e=>{let t=e[e6];return!G(t)&&!(0,eN.Z)(t)&&!(0,g.Z)(t,"exec")&&!(0,A.Z)(t)});return{errors:(0,m.Z)(t,e=>({message:"Token Type: ->"+e.name+"<- static 'PATTERN' can only be a RegExp, a Function matching the {CustomPatternMatcherFunc} type or an Object matching the {ICustomPattern} interface.",type:l.INVALID_PATTERN,tokenTypes:[e]})),valid:eB(e,t)}}(n.valid),a=i.valid;return(r=(r=(r=(r=r.concat(i.errors)).concat(function(e){let t=[],r=(0,eO.Z)(e,e=>G(e[e6]));return(t=(t=(t=(t=t.concat(function(e){class t extends ek.e{constructor(){super(...arguments),this.found=!1}visitEndAnchor(e){this.found=!0}}let r=(0,eO.Z)(e,e=>{let r=e.PATTERN;try{let e=eq(r),n=new t;return n.visit(e),n.found}catch(e){return e4.test(r.source)}});return(0,m.Z)(r,e=>({message:"Unexpected RegExp Anchor Error:\n	Token Type: ->"+e.name+"<- static 'PATTERN' cannot contain end of input anchor '$'\n	See chevrotain.io/docs/guide/resolving_lexer_errors.html#ANCHORS	for details.",type:l.EOI_ANCHOR_FOUND,tokenTypes:[e]}))}(r))).concat(function(e){class t extends ek.e{constructor(){super(...arguments),this.found=!1}visitStartAnchor(e){this.found=!0}}let r=(0,eO.Z)(e,e=>{let r=e.PATTERN;try{let e=eq(r),n=new t;return n.visit(e),n.found}catch(e){return e5.test(r.source)}});return(0,m.Z)(r,e=>({message:"Unexpected RegExp Anchor Error:\n	Token Type: ->"+e.name+"<- static 'PATTERN' cannot contain start of input anchor '^'\n	See https://chevrotain.io/docs/guide/resolving_lexer_errors.html#ANCHORS	for details.",type:l.SOI_ANCHOR_FOUND,tokenTypes:[e]}))}(r))).concat(function(e){let t=(0,eO.Z)(e,e=>{let t=e[e6];return t instanceof RegExp&&(t.multiline||t.global)});return(0,m.Z)(t,e=>({message:"Token Type: ->"+e.name+"<- static 'PATTERN' may NOT contain global('g') or multiline('m')",type:l.UNSUPPORTED_FLAGS_FOUND,tokenTypes:[e]}))}(r))).concat(function(e){let t=[],r=(0,m.Z)(e,r=>(0,eb.Z)(e,(e,n)=>(r.PATTERN.source!==n.PATTERN.source||el(t,n)||n.PATTERN===tk.NA||(t.push(n),e.push(n)),e),[]));r=eV(r);let n=(0,eO.Z)(r,e=>e.length>1);return(0,m.Z)(n,e=>{let t=(0,m.Z)(e,e=>e.name),r=eK(e).PATTERN;return{message:`The same RegExp pattern ->${r}<-has been used in all of the following Token Types: ${t.join(", ")} <-`,type:l.DUPLICATE_PATTERNS_FOUND,tokenTypes:e}})}(r))).concat(function(e){let t=(0,eO.Z)(e,e=>e.PATTERN.test(""));return(0,m.Z)(t,e=>({message:"Token Type: ->"+e.name+"<- static 'PATTERN' must not match an empty string",type:l.EMPTY_MATCH_PATTERN,tokenTypes:[e]}))}(r))}(a))).concat(function(e){let t=(0,eO.Z)(e,e=>{if(!(0,g.Z)(e,"GROUP"))return!1;let t=e.GROUP;return t!==tk.SKIPPED&&t!==tk.NA&&!(0,A.Z)(t)});return(0,m.Z)(t,e=>({message:"Token Type: ->"+e.name+"<- static 'GROUP' can only be Lexer.SKIPPED/Lexer.NA/A String",type:l.INVALID_GROUP_TYPE_FOUND,tokenTypes:[e]}))}(a))).concat(function(e,t){let r=(0,eO.Z)(e,e=>void 0!==e.PUSH_MODE&&!el(t,e.PUSH_MODE));return(0,m.Z)(r,e=>({message:`Token Type: ->${e.name}<- static 'PUSH_MODE' value cannot refer to a Lexer Mode ->${e.PUSH_MODE}<-which does not exist`,type:l.PUSH_MODE_DOES_NOT_EXIST,tokenTypes:[e]}))}(a,t))).concat(function(e){let t=[],r=(0,eb.Z)(e,(e,t,r)=>{var n;let i=t.PATTERN;return i===tk.NA||((0,A.Z)(i)?e.push({str:i,idx:r,tokenType:t}):G(i)&&(n=i,void 0===(0,ej.Z)([".","\\","[","]","|","^","$","(",")","?","*","+","{"],e=>-1!==n.source.indexOf(e)))&&e.push({str:i.source,idx:r,tokenType:t})),e},[]);return(0,h.Z)(e,(e,n)=>{(0,h.Z)(r,({str:r,idx:i,tokenType:a})=>{if(n<i&&function(e,t){if(G(t)){let r=t.exec(e);return null!==r&&0===r.index}if((0,eN.Z)(t))return t(e,0,[],{});if((0,g.Z)(t,"exec"))return t.exec(e,0,[],{});if("string"==typeof t)return t===e;throw Error("non exhaustive match")}(r,e.PATTERN)){let r=`Token: ->${a.name}<- can never be matched.
Because it appears AFTER the Token Type ->${e.name}<-in the lexer's definition.
See https://chevrotain.io/docs/guide/resolving_lexer_errors.html#UNREACHABLE`;t.push({message:r,type:l.UNREACHABLE_PATTERN,tokenTypes:[e,a]})}})}),t}(a))}(e,i))}),(0,p.Z)(this.lexerDefinitionErrors)){let n;ty(e),this.TRACE_INIT("analyzeTokenTypes",()=>{n=function(e,t){let r,n,i,a,s,o,l,u,c,d,y,T,v=(t=(0,e$.Z)(t,{useSticky:e7,debug:!1,safeMode:!1,positionTracking:"full",lineTerminatorCharacters:["\r","\n"],tracer:(e,t)=>t()})).tracer;v("initCharCodeToOptimizedIndexMap",()=>{if((0,p.Z)(to)){to=Array(65536);for(let e=0;e<65536;e++)to[e]=e>255?255+~~(e/255):e}}),v("Reject Lexer.NA",()=>{r=eC(e,e=>e[e6]===tk.NA)});let R=!1;v("Transform Patterns",()=>{R=!1,n=(0,m.Z)(r,e=>{let r=e[e6];if(G(r)){let e=r.source;return 1!==e.length||"^"===e||"$"===e||"."===e||r.ignoreCase?2!==e.length||"\\"!==e[0]||el(["d","D","s","S","t","r","n","t","0","c","b","B","f","v","w","W"],e[1])?t.useSticky?te(r):e8(r):e[1]:e}if((0,eN.Z)(r))return R=!0,{exec:r};if("object"==typeof r)return R=!0,r;if("string"==typeof r)if(1===r.length)return r;else{let e=new RegExp(r.replace(/[\\^$.*+?()[\]{}|]/g,"\\$&"));return t.useSticky?te(e):e8(e)}throw Error("non exhaustive match")})}),v("misc mapping",()=>{i=(0,m.Z)(r,e=>e.tokenTypeIdx),a=(0,m.Z)(r,e=>{let t=e.GROUP;if(t!==tk.SKIPPED){if((0,A.Z)(t))return t;if((0,eA.Z)(t))return!1;throw Error("non exhaustive match")}}),s=(0,m.Z)(r,e=>{let t=e.LONGER_ALT;if(t)return(0,en.Z)(t)?(0,m.Z)(t,e=>eL(r,e)):[eL(r,t)]}),o=(0,m.Z)(r,e=>e.PUSH_MODE),l=(0,m.Z)(r,e=>(0,g.Z)(e,"POP_MODE"))}),v("Line Terminator Handling",()=>{let e=ta(t.lineTerminatorCharacters);u=(0,m.Z)(r,e=>!1),"onlyOffset"!==t.positionTracking&&(u=(0,m.Z)(r,t=>(0,g.Z)(t,"LINE_BREAKS")?!!t.LINE_BREAKS:!1===ti(t,e)&&e2(e,t.PATTERN)))}),v("Misc Mapping #2",()=>{c=(0,m.Z)(r,tt),d=(0,m.Z)(n,tr),y=(0,eb.Z)(r,(e,t)=>{let r=t.GROUP;return(0,A.Z)(r)&&r!==tk.SKIPPED&&(e[r]=[]),e},{}),T=(0,m.Z)(n,(e,t)=>({pattern:n[t],longerAlt:s[t],canLineTerminator:u[t],isCustom:c[t],short:d[t],group:a[t],push:o[t],pop:l[t],tokenTypeIdx:i[t],tokenType:r[t]}))});let E=!0,k=[];return t.safeMode||v("First Char Optimization",()=>{k=(0,eb.Z)(r,(e,r,n)=>{if("string"==typeof r.PATTERN)ts(e,tl(r.PATTERN.charCodeAt(0)),T[n]);else if((0,en.Z)(r.START_CHARS_HINT)){let t;(0,h.Z)(r.START_CHARS_HINT,r=>{let i=tl("string"==typeof r?r.charCodeAt(0):r);t!==i&&(t=i,ts(e,i,T[n]))})}else if(G(r.PATTERN))if(r.PATTERN.unicode)E=!1,t.ensureOptimizations&&eW(`${eQ}	Unable to analyze < ${r.PATTERN.toString()} > pattern.
	The regexp unicode flag is not currently supported by the regexp-to-ast library.
	This will disable the lexer's first char optimizations.
	For details See: https://chevrotain.io/docs/guide/resolving_lexer_errors.html#UNICODE_OPTIMIZE`);else{let i=function(e,t=!1){try{let t=eq(e);return function e(t,r,n){switch(t.type){case"Disjunction":for(let i=0;i<t.value.length;i++)e(t.value[i],r,n);break;case"Alternative":let i=t.value;for(let t=0;t<i.length;t++){let a=i[t];switch(a.type){case"EndAnchor":case"GroupBackReference":case"Lookahead":case"NegativeLookahead":case"StartAnchor":case"WordBoundary":case"NonWordBoundary":continue}switch(a.type){case"Character":eJ(a.value,r,n);break;case"Set":if(!0===a.complement)throw Error(eX);(0,h.Z)(a.value,e=>{if("number"==typeof e)eJ(e,r,n);else if(!0===n)for(let t=e.from;t<=e.to;t++)eJ(t,r,n);else{for(let t=e.from;t<=e.to&&t<256;t++)eJ(t,r,n);if(e.to>=256){let t=e.from>=256?e.from:256,n=e.to,i=tl(t),a=tl(n);for(let e=i;e<=a;e++)r[e]=e}}});break;case"Group":e(a.value,r,n);break;default:throw Error("Non Exhaustive Match")}let s=void 0!==a.quantifier&&0===a.quantifier.atLeast;if("Group"===a.type&&!1===function e(t){let r=t.quantifier;return!!r&&0===r.atLeast||!!t.value&&((0,en.Z)(t.value)?ed(t.value,e):e(t.value))}(a)||"Group"!==a.type&&!1===s)break}break;default:throw Error("non exhaustive match!")}return(0,f.Z)(r)}(t.value,{},t.flags.ignoreCase)}catch(r){if(r.message===eX)t&&eH(`${eQ}	Unable to optimize: < ${e.toString()} >
	Complement Sets cannot be automatically optimized.
	This will disable the lexer's first char optimizations.
	See: https://chevrotain.io/docs/guide/resolving_lexer_errors.html#COMPLEMENT for details.`);else{let r="";t&&(r="\n	This will disable the lexer's first char optimizations.\n	See: https://chevrotain.io/docs/guide/resolving_lexer_errors.html#REGEXP_PARSING for details."),eW(`${eQ}
	Failed parsing: < ${e.toString()} >
	Using the @chevrotain/regexp-to-ast library
	Please open an issue at: https://github.com/chevrotain/chevrotain/issues`+r)}}return[]}(r.PATTERN,t.ensureOptimizations);(0,p.Z)(i)&&(E=!1),(0,h.Z)(i,t=>{ts(e,t,T[n])})}else t.ensureOptimizations&&eW(`${eQ}	TokenType: <${r.name}> is using a custom token pattern without providing <start_chars_hint> parameter.
	This will disable the lexer's first char optimizations.
	For details See: https://chevrotain.io/docs/guide/resolving_lexer_errors.html#CUSTOM_OPTIMIZE`),E=!1;return e},[])}),{emptyGroups:y,patternIdxToConfig:T,charCodeToPatternIdxToConfig:k,hasCustom:R,canBeOptimized:E}}(e,{lineTerminatorCharacters:this.config.lineTerminatorCharacters,positionTracking:t.positionTracking,ensureOptimizations:t.ensureOptimizations,safeMode:t.safeMode,tracer:this.TRACE_INIT})}),this.patternIdxToConfig[r]=n.patternIdxToConfig,this.charCodeToPatternIdxToConfig[r]=n.charCodeToPatternIdxToConfig,this.emptyGroups=w({},this.emptyGroups,n.emptyGroups),this.hasCustom=n.hasCustom||this.hasCustom,this.canModeBeOptimized[r]=n.canBeOptimized}})}),this.defaultMode=r.defaultMode,!(0,p.Z)(this.lexerDefinitionErrors)&&!this.config.deferDefinitionErrorsHandling)throw Error("Errors detected in definition of Lexer:\n"+(0,m.Z)(this.lexerDefinitionErrors,e=>e.message).join("-----------------------\n"));(0,h.Z)(this.lexerDefinitionWarning,e=>{eH(e.message)}),this.TRACE_INIT("Choosing sub-methods implementations",()=>{if(e7?(this.chopInput=tu.Z,this.match=this.matchWithTest):(this.updateLastIndex=tc.Z,this.match=this.matchWithExec),n&&(this.handleModes=tc.Z),!1===this.trackStartLines&&(this.computeNewColumn=tu.Z),!1===this.trackEndLines&&(this.updateTokenEndLineColumnLocation=tc.Z),/full/i.test(this.config.positionTracking))this.createTokenInstance=this.createFullToken;else if(/onlyStart/i.test(this.config.positionTracking))this.createTokenInstance=this.createStartOnlyToken;else if(/onlyOffset/i.test(this.config.positionTracking))this.createTokenInstance=this.createOffsetOnlyToken;else throw Error(`Invalid <positionTracking> config option: "${this.config.positionTracking}"`);this.hasCustom?(this.addToken=this.addTokenUsingPush,this.handlePayload=this.handlePayloadWithCustom):(this.addToken=this.addTokenUsingMemberAccess,this.handlePayload=this.handlePayloadNoCustom)}),this.TRACE_INIT("Failed Optimization Warnings",()=>{let e=(0,eb.Z)(this.canModeBeOptimized,(e,t,r)=>(!1===t&&e.push(r),e),[]);if(t.ensureOptimizations&&!(0,p.Z)(e))throw Error(`Lexer Modes: < ${e.join(", ")} > cannot be optimized.
	 Disable the "ensureOptimizations" lexer config flag to silently ignore this and run the lexer in an un-optimized mode.
	 Or inspect the console log for details on how to resolve these issues.`)}),this.TRACE_INIT("clearRegExpParserCache",()=>{ez={}}),this.TRACE_INIT("toFastProperties",()=>{T(this)})})}tokenize(e,t=this.defaultMode){if(!(0,p.Z)(this.lexerDefinitionErrors))throw Error("Unable to Tokenize because Errors detected in definition of Lexer:\n"+(0,m.Z)(this.lexerDefinitionErrors,e=>e.message).join("-----------------------\n"));return this.tokenizeInternal(e,t)}tokenizeInternal(e,t){let r,n,i,a,s,o,l,u,c,d,f,p,m,g,y,T,v,R=e,E=R.length,A=0,k=0,$=Array(this.hasCustom?0:Math.floor(e.length/10)),x=[],I=this.trackStartLines?1:void 0,S=this.trackStartLines?1:void 0,N=function(e){let t={},r=(0,C.Z)(e);return(0,h.Z)(r,r=>{let n=e[r];if((0,en.Z)(n))t[r]=[];else throw Error("non exhaustive match")}),t}(this.emptyGroups),w=this.trackStartLines,L=this.config.lineTerminatorsPattern,b=0,O=[],_=[],P=[],M=[];function D(){return O}function Z(e){let t=_[tl(e)];return void 0===t?M:t}Object.freeze(M);let F=e=>{if(1===P.length&&void 0===e.tokenType.PUSH_MODE){let t=this.config.errorMessageProvider.buildUnableToPopLexerModeMessage(e);x.push({offset:e.startOffset,line:e.startLine,column:e.startColumn,length:e.image.length,message:t})}else{P.pop();let e=(0,td.Z)(P);O=this.patternIdxToConfig[e],_=this.charCodeToPatternIdxToConfig[e],b=O.length;let t=this.canModeBeOptimized[e]&&!1===this.config.safeMode;T=_&&t?Z:D}};function U(e){P.push(e),_=this.charCodeToPatternIdxToConfig[e],b=(O=this.patternIdxToConfig[e]).length,b=O.length;let t=this.canModeBeOptimized[e]&&!1===this.config.safeMode;T=_&&t?Z:D}U.call(this,t);let G=this.config.recoveryEnabled;for(;A<E;){o=null;let t=R.charCodeAt(A),h=T(t),C=h.length;for(r=0;r<C;r++){let n=(v=h[r]).pattern;l=null;let c=v.short;if(!1!==c?t===c&&(o=n):!0===v.isCustom?null!==(y=n.exec(R,A,$,N))?(o=y[0],void 0!==y.payload&&(l=y.payload)):o=null:(this.updateLastIndex(n,A),o=this.match(n,e,A)),null!==o){if(void 0!==(s=v.longerAlt)){let t=s.length;for(i=0;i<t;i++){let t=O[s[i]],r=t.pattern;if(u=null,!0===t.isCustom?null!==(y=r.exec(R,A,$,N))?(a=y[0],void 0!==y.payload&&(u=y.payload)):a=null:(this.updateLastIndex(r,A),a=this.match(r,e,A)),a&&a.length>o.length){o=a,l=u,v=t;break}}}break}}if(null!==o){if(c=o.length,void 0!==(d=v.group)&&(f=v.tokenTypeIdx,p=this.createTokenInstance(o,A,f,v.tokenType,I,S,c),this.handlePayload(p,l),!1===d?k=this.addToken($,k,p):N[d].push(p)),e=this.chopInput(e,c),A+=c,S=this.computeNewColumn(S,c),!0===w&&!0===v.canLineTerminator){let e,t,r=0;L.lastIndex=0;do!0===(e=L.test(o))&&(t=L.lastIndex-1,r++);while(!0===e);0!==r&&(I+=r,S=c-t,this.updateTokenEndLineColumnLocation(p,d,t,r,I,S,c))}this.handleModes(v,F,U,p)}else{let t=A,r=I,i=S,a=!1===G;for(;!1===a&&A<E;)for(e=this.chopInput(e,1),A++,n=0;n<b;n++){let t=O[n],r=t.pattern,i=t.short;if(!1!==i?R.charCodeAt(A)===i&&(a=!0):!0===t.isCustom?a=null!==r.exec(R,A,$,N):(this.updateLastIndex(r,A),a=null!==r.exec(e)),!0===a)break}if(m=A-t,S=this.computeNewColumn(S,m),g=this.config.errorMessageProvider.buildUnexpectedCharactersMessage(R,t,m,r,i),x.push({offset:t,line:r,column:i,length:m,message:g}),!1===G)break}}return this.hasCustom||($.length=k),{tokens:$,groups:N,errors:x}}handleModes(e,t,r,n){if(!0===e.pop){let i=e.push;t(n),void 0!==i&&r.call(this,i)}else void 0!==e.push&&r.call(this,e.push)}chopInput(e,t){return e.substring(t)}updateLastIndex(e,t){e.lastIndex=t}updateTokenEndLineColumnLocation(e,t,r,n,i,a,s){let o,l;void 0!==t&&(l=(o=r===s-1)?-1:0,(1!==n||!0!==o)&&(e.endLine=i+l,e.endColumn=a-1+-l))}computeNewColumn(e,t){return e+t}createOffsetOnlyToken(e,t,r,n){return{image:e,startOffset:t,tokenTypeIdx:r,tokenType:n}}createStartOnlyToken(e,t,r,n,i,a){return{image:e,startOffset:t,startLine:i,startColumn:a,tokenTypeIdx:r,tokenType:n}}createFullToken(e,t,r,n,i,a,s){return{image:e,startOffset:t,endOffset:t+s-1,startLine:i,endLine:i,startColumn:a,endColumn:a+s-1,tokenTypeIdx:r,tokenType:n}}addTokenUsingPush(e,t,r){return e.push(r),t}addTokenUsingMemberAccess(e,t,r){return e[t]=r,++t}handlePayloadNoCustom(e,t){}handlePayloadWithCustom(e,t){null!==t&&(e.payload=t)}matchWithTest(e,t,r){return!0===e.test(t)?t.substring(r,e.lastIndex):null}matchWithExec(e,t){let r=e.exec(t);return null!==r?r[0]:null}}function t$(e){return tx(e)?e.LABEL:e.name}function tx(e){return(0,A.Z)(e.LABEL)&&""!==e.LABEL}tk.SKIPPED="This marks a skipped Token pattern, this means each token identified by it willbe consumed and then thrown into oblivion, this can be used to for example to completely ignore whitespace.",tk.NA=/NOT_APPLICABLE/;let tI="categories",tS="label",tC="group",tN="push_mode",tw="pop_mode",tL="longer_alt",tb="line_breaks",tO="start_chars_hint";function t_(e){let t=e.pattern,r={};if(r.name=e.name,(0,eA.Z)(t)||(r.PATTERN=t),(0,g.Z)(e,"parent"))throw"The parent property is no longer supported.\nSee: https://github.com/chevrotain/chevrotain/issues/564#issuecomment-349062346 for details.";return(0,g.Z)(e,tI)&&(r.CATEGORIES=e[tI]),ty([r]),(0,g.Z)(e,tS)&&(r.LABEL=e[tS]),(0,g.Z)(e,tC)&&(r.GROUP=e[tC]),(0,g.Z)(e,tw)&&(r.POP_MODE=e[tw]),(0,g.Z)(e,tN)&&(r.PUSH_MODE=e[tN]),(0,g.Z)(e,tL)&&(r.LONGER_ALT=e[tL]),(0,g.Z)(e,tb)&&(r.LINE_BREAKS=e[tb]),(0,g.Z)(e,tO)&&(r.START_CHARS_HINT=e[tO]),r}let tP=t_({name:"EOF",pattern:tk.NA});function tM(e,t,r,n,i,a,s,o){return{image:t,startOffset:r,endOffset:n,startLine:i,endLine:a,startColumn:s,endColumn:o,tokenTypeIdx:e.tokenTypeIdx,tokenType:e}}function tD(e,t){return tf(e,t)}ty([tP]);let tZ={buildMismatchTokenMessage({expected:e,actual:t,previous:r,ruleName:n}){let i=tx(e)?`--> ${t$(e)} <--`:`token of type --> ${e.name} <--`;return`Expecting ${i} but found --> '${t.image}' <--`},buildNotAllInputParsedMessage:({firstRedundant:e,ruleName:t})=>"Redundant input, expecting EOF but found: "+e.image,buildNoViableAltMessage({expectedPathsPerAlt:e,actual:t,previous:r,customUserDescription:n,ruleName:i}){let a="Expecting: ",s="\nbut found: '"+eK(t).image+"'";if(n)return a+n+s;{let t=(0,eb.Z)(e,(e,t)=>e.concat(t),[]),r=(0,m.Z)(t,e=>`[${(0,m.Z)(e,e=>t$(e)).join(", ")}]`),n=(0,m.Z)(r,(e,t)=>`  ${t+1}. ${e}`);return a+`one of these possible Token sequences:
${n.join("\n")}`+s}},buildEarlyExitMessage({expectedIterationPaths:e,actual:t,customUserDescription:r,ruleName:n}){let i="Expecting: ",a="\nbut found: '"+eK(t).image+"'";if(r)return i+r+a;{let t=(0,m.Z)(e,e=>`[${(0,m.Z)(e,e=>t$(e)).join(",")}]`);return i+`expecting at least one iteration which starts with one of these possible Token sequences::
  <${t.join(" ,")}>`+a}}};Object.freeze(tZ);let tF={buildRuleNotFoundError:(e,t)=>"Invalid grammar, reference to a rule which is not defined: ->"+t.nonTerminalName+"<-\ninside top level rule: ->"+e.name+"<-"},tU={buildDuplicateFoundError(e,t){let r=e.name,n=eK(t),i=n.idx,a=ef(n),s=n instanceof Q?n.terminalType.name:n instanceof V?n.nonTerminalName:"",o=`->${a}${i>0?i:""}<- ${s?`with argument: ->${s}<-`:""}
                  appears more than once (${t.length} times) in the top level rule: ->${r}<-.                  
                  For further details see: https://chevrotain.io/docs/FAQ.html#NUMERICAL_SUFFIXES 
                  `;return(o=o.replace(/[ \t]+/g," ")).replace(/\s\s+/g,"\n")},buildNamespaceConflictError:e=>`Namespace conflict found in grammar.
The grammar has both a Terminal(Token) and a Non-Terminal(Rule) named: <${e.name}>.
To resolve this make sure each Terminal and Non-Terminal names are unique
This is easy to accomplish by using the convention that Terminal names start with an uppercase letter
and Non-Terminal names start with a lower case letter.`,buildAlternationPrefixAmbiguityError(e){let t=(0,m.Z)(e.prefixPath,e=>t$(e)).join(", "),r=0===e.alternation.idx?"":e.alternation.idx;return`Ambiguous alternatives: <${e.ambiguityIndices.join(" ,")}> due to common lookahead prefix
in <OR${r}> inside <${e.topLevelRule.name}> Rule,
<${t}> may appears as a prefix path in all these alternatives.
See: https://chevrotain.io/docs/guide/resolving_grammar_errors.html#COMMON_PREFIX
For Further details.`},buildAlternationAmbiguityError(e){let t=(0,m.Z)(e.prefixPath,e=>t$(e)).join(", "),r=0===e.alternation.idx?"":e.alternation.idx,n=`Ambiguous Alternatives Detected: <${e.ambiguityIndices.join(" ,")}> in <OR${r}> inside <${e.topLevelRule.name}> Rule,
<${t}> may appears as a prefix path in all these alternatives.
`;return n+`See: https://chevrotain.io/docs/guide/resolving_grammar_errors.html#AMBIGUOUS_ALTERNATIVES
`+"For Further details."},buildEmptyRepetitionError(e){let t=ef(e.repetition);return 0!==e.repetition.idx&&(t+=e.repetition.idx),`The repetition <${t}> within Rule <${e.topLevelRule.name}> can never consume any tokens.
This could lead to an infinite loop.`},buildTokenNameError:e=>"deprecated",buildEmptyAlternationError:e=>`Ambiguous empty alternative: <${e.emptyChoiceIdx+1}> in <OR${e.alternation.idx}> inside <${e.topLevelRule.name}> Rule.
Only the last alternative may be an empty alternative.`,buildTooManyAlternativesError:e=>`An Alternation cannot have more than 256 alternatives:
<OR${e.alternation.idx}> inside <${e.topLevelRule.name}> Rule.
 has ${e.alternation.definition.length+1} alternatives.`,buildLeftRecursionError(e){let t=e.topLevelRule.name,r=(0,m.Z)(e.leftRecursionPath,e=>e.name),n=`${t} --> ${r.concat([t]).join(" --\x3e ")}`;return`Left Recursion found in grammar.
rule: <${t}> can be invoked from itself (directly or indirectly)
without consuming any Tokens. The grammar path that causes this is: 
 ${n}
 To fix this refactor your grammar to remove the left recursion.
see: https://en.wikipedia.org/wiki/LL_parser#Left_factoring.`},buildInvalidRuleNameError:e=>"deprecated",buildDuplicateRuleNameError(e){let t;return t=e.topLevelRule instanceof K?e.topLevelRule.name:e.topLevelRule,`Duplicate definition, rule: ->${t}<- is already defined in the grammar: ->${e.grammarName}<-`}};class tG extends J{constructor(e,t){super(),this.nameToTopRule=e,this.errMsgProvider=t,this.errors=[]}resolveRefs(){(0,h.Z)((0,f.Z)(this.nameToTopRule),e=>{this.currTopLevel=e,e.accept(this)})}visitNonTerminal(e){let t=this.nameToTopRule[e.nonTerminalName];if(t)e.referencedRule=t;else{let t=this.errMsgProvider.buildRuleNotFoundError(this.currTopLevel,e);this.errors.push({message:t,type:d.UNRESOLVED_SUBRULE_REF,ruleName:this.currTopLevel.name,unresolvedRefName:e.nonTerminalName})}}}var tB=r(24945),tV=r(23454);let tK=function(e,t,r,n){for(var i=-1,a=null==e?0:e.length;++i<a;){var s=e[i];t(n,s,r(s),e)}return n},tj=function(e,t,r,n){return(0,et.Z)(e,function(e,i,a){t(n,e,r(e),a)}),n};var tW=Object.prototype.hasOwnProperty,tH=(i=function(e,t,r){tW.call(e,r)?e[r].push(t):(0,tV.Z)(e,r,[t])},function(e,t){return((0,en.Z)(e)?tK:tj)(e,i,(0,b.Z)(t,2),{})});let tz=function(e,t,r){var n=null==e?0:e.length;return n?v(e,0,(t=n-(t=r||void 0===t?1:(0,R.Z)(t)))<0?0:t):[]};class tY extends ep{constructor(e,t){super(),this.topProd=e,this.path=t,this.possibleTokTypes=[],this.nextProductionName="",this.nextProductionOccurrence=0,this.found=!1,this.isAtEndOfPath=!1}startWalking(){if(this.found=!1,this.path.ruleStack[0]!==this.topProd.name)throw Error("The path does not start with the walker's top Rule!");return this.ruleStack=(0,y.Z)(this.path.ruleStack).reverse(),this.occurrenceStack=(0,y.Z)(this.path.occurrenceStack).reverse(),this.ruleStack.pop(),this.occurrenceStack.pop(),this.updateExpectedNext(),this.walk(this.topProd),this.possibleTokTypes}walk(e,t=[]){this.found||super.walk(e,t)}walkProdRef(e,t,r){if(e.referencedRule.name===this.nextProductionName&&e.idx===this.nextProductionOccurrence){let n=t.concat(r);this.updateExpectedNext(),this.walk(e.referencedRule,n)}}updateExpectedNext(){(0,p.Z)(this.ruleStack)?(this.nextProductionName="",this.nextProductionOccurrence=0,this.isAtEndOfPath=!0):(this.nextProductionName=this.ruleStack.pop(),this.nextProductionOccurrence=this.occurrenceStack.pop())}}class tq extends tY{constructor(e,t){super(e,t),this.path=t,this.nextTerminalName="",this.nextTerminalOccurrence=0,this.nextTerminalName=this.path.lastTok.name,this.nextTerminalOccurrence=this.path.lastTokOccurrence}walkTerminal(e,t,r){if(this.isAtEndOfPath&&e.terminalType.name===this.nextTerminalName&&e.idx===this.nextTerminalOccurrence&&!this.found){let e=new j({definition:t.concat(r)});this.possibleTokTypes=ev(e),this.found=!0}}}class tX extends ep{constructor(e,t){super(),this.topRule=e,this.occurrence=t,this.result={token:void 0,occurrence:void 0,isEndOfRule:void 0}}startWalking(){return this.walk(this.topRule),this.result}}class tQ extends tX{walkMany(e,t,r){if(e.idx===this.occurrence){let e=eK(t.concat(r));this.result.isEndOfRule=void 0===e,e instanceof Q&&(this.result.token=e.terminalType,this.result.occurrence=e.idx)}else super.walkMany(e,t,r)}}class tJ extends tX{walkManySep(e,t,r){if(e.idx===this.occurrence){let e=eK(t.concat(r));this.result.isEndOfRule=void 0===e,e instanceof Q&&(this.result.token=e.terminalType,this.result.occurrence=e.idx)}else super.walkManySep(e,t,r)}}class t0 extends tX{walkAtLeastOne(e,t,r){if(e.idx===this.occurrence){let e=eK(t.concat(r));this.result.isEndOfRule=void 0===e,e instanceof Q&&(this.result.token=e.terminalType,this.result.occurrence=e.idx)}else super.walkAtLeastOne(e,t,r)}}class t1 extends tX{walkAtLeastOneSep(e,t,r){if(e.idx===this.occurrence){let e=eK(t.concat(r));this.result.isEndOfRule=void 0===e,e instanceof Q&&(this.result.token=e.terminalType,this.result.occurrence=e.idx)}else super.walkAtLeastOneSep(e,t,r)}}function t2(e,t,r=[]){r=(0,y.Z)(r);let n=[],i=0;function a(a){let s=t2(a.concat(E(e,i+1)),t,r);return n.concat(s)}for(;r.length<t&&i<e.length;){let t=e[i];if(t instanceof j)return a(t.definition);if(t instanceof V)return a(t.definition);if(t instanceof W)n=a(t.definition);else if(t instanceof H)return a(t.definition.concat([new Y({definition:t.definition})]));else if(t instanceof z)return a([new j({definition:t.definition}),new Y({definition:[new Q({terminalType:t.separator})].concat(t.definition)})]);else if(t instanceof q)n=a(t.definition.concat([new Y({definition:[new Q({terminalType:t.separator})].concat(t.definition)})]));else if(t instanceof Y)n=a(t.definition.concat([new Y({definition:t.definition})]));else if(t instanceof X)return(0,h.Z)(t.definition,e=>{!1===(0,p.Z)(e.definition)&&(n=a(e.definition))}),n;else if(t instanceof Q)r.push(t.terminalType);else throw Error("non exhaustive match");i++}return n.push({partialPath:r,suffixDef:E(e,i)}),n}function t6(e,t,r,n){let i="EXIT_NONE_TERMINAL",a=[i],s="EXIT_ALTERNATIVE",o=!1,l=t.length,u=l-n-1,c=[],d=[];for(d.push({idx:-1,def:e,ruleStack:[],occurrenceStack:[]});!(0,p.Z)(d);){let e=d.pop();if(e===s){o&&(0,td.Z)(d).idx<=u&&d.pop();continue}let n=e.def,h=e.idx,f=e.ruleStack,m=e.occurrenceStack;if((0,p.Z)(n))continue;let g=n[0];if(g===i){let e={idx:h,def:E(n),ruleStack:tz(f),occurrenceStack:tz(m)};d.push(e)}else if(g instanceof Q)if(h<l-1){let e=h+1;if(r(t[e],g.terminalType)){let t={idx:e,def:E(n),ruleStack:f,occurrenceStack:m};d.push(t)}}else if(h===l-1)c.push({nextTokenType:g.terminalType,nextTokenOccurrence:g.idx,ruleStack:f,occurrenceStack:m}),o=!0;else throw Error("non exhaustive match");else if(g instanceof V){let e=(0,y.Z)(f);e.push(g.nonTerminalName);let t=(0,y.Z)(m);t.push(g.idx);let r={idx:h,def:g.definition.concat(a,E(n)),ruleStack:e,occurrenceStack:t};d.push(r)}else if(g instanceof W){let e={idx:h,def:E(n),ruleStack:f,occurrenceStack:m};d.push(e),d.push(s);let t={idx:h,def:g.definition.concat(E(n)),ruleStack:f,occurrenceStack:m};d.push(t)}else if(g instanceof H){let e=new Y({definition:g.definition,idx:g.idx}),t={idx:h,def:g.definition.concat([e],E(n)),ruleStack:f,occurrenceStack:m};d.push(t)}else if(g instanceof z){let e=new Y({definition:[new Q({terminalType:g.separator})].concat(g.definition),idx:g.idx}),t={idx:h,def:g.definition.concat([e],E(n)),ruleStack:f,occurrenceStack:m};d.push(t)}else if(g instanceof q){let e={idx:h,def:E(n),ruleStack:f,occurrenceStack:m};d.push(e),d.push(s);let t=new Y({definition:[new Q({terminalType:g.separator})].concat(g.definition),idx:g.idx}),r={idx:h,def:g.definition.concat([t],E(n)),ruleStack:f,occurrenceStack:m};d.push(r)}else if(g instanceof Y){let e={idx:h,def:E(n),ruleStack:f,occurrenceStack:m};d.push(e),d.push(s);let t=new Y({definition:g.definition,idx:g.idx}),r={idx:h,def:g.definition.concat([t],E(n)),ruleStack:f,occurrenceStack:m};d.push(r)}else if(g instanceof X)for(let e=g.definition.length-1;e>=0;e--){let t={idx:h,def:g.definition[e].definition.concat(E(n)),ruleStack:f,occurrenceStack:m};d.push(t),d.push(s)}else if(g instanceof j)d.push({idx:h,def:g.definition.concat(E(n)),ruleStack:f,occurrenceStack:m});else if(g instanceof K)d.push(function(e,t,r,n){let i=(0,y.Z)(r);i.push(e.name);let a=(0,y.Z)(n);return a.push(1),{idx:t,def:e.definition,ruleStack:i,occurrenceStack:a}}(g,h,f,m));else throw Error("non exhaustive match")}return c}function t9(e){if(e instanceof W||"Option"===e)return u.OPTION;if(e instanceof Y||"Repetition"===e)return u.REPETITION;if(e instanceof H||"RepetitionMandatory"===e)return u.REPETITION_MANDATORY;if(e instanceof z||"RepetitionMandatoryWithSeparator"===e)return u.REPETITION_MANDATORY_WITH_SEPARATOR;if(e instanceof q||"RepetitionWithSeparator"===e)return u.REPETITION_WITH_SEPARATOR;else if(e instanceof X||"Alternation"===e)return u.ALTERNATION;else throw Error("non exhaustive match")}function t3(e){let{occurrence:t,rule:r,prodType:n,maxLookahead:i}=e,a=t9(n);return a===u.ALTERNATION?rn(t,r,i):ri(t,r,a,i)}function t7(e,t,r,n){let i=e.length,a=ed(e,e=>ed(e,e=>1===e.length));if(t)return function(t){let n=(0,m.Z)(t,e=>e.GATE);for(let t=0;t<i;t++){let i=e[t],a=i.length,s=n[t];if(void 0===s||!1!==s.call(this))t:for(let e=0;e<a;e++){let n=i[e],a=n.length;for(let e=0;e<a;e++)if(!1===r(this.LA(e+1),n[e]))continue t;return t}}};if(!a||n)return function(){for(let t=0;t<i;t++){let n=e[t],i=n.length;t:for(let e=0;e<i;e++){let i=n[e],a=i.length;for(let e=0;e<a;e++)if(!1===r(this.LA(e+1),i[e]))continue t;return t}}};{let t=(0,m.Z)(e,e=>(0,eT.Z)(e)),r=(0,eb.Z)(t,(e,t,r)=>((0,h.Z)(t,t=>{(0,g.Z)(e,t.tokenTypeIdx)||(e[t.tokenTypeIdx]=r),(0,h.Z)(t.categoryMatches,t=>{(0,g.Z)(e,t)||(e[t]=r)})}),e),{});return function(){return r[this.LA(1).tokenTypeIdx]}}}function t4(e,t,r){let n=ed(e,e=>1===e.length),i=e.length;if(!n||r)return function(){t:for(let r=0;r<i;r++){let n=e[r],i=n.length;for(let e=0;e<i;e++)if(!1===t(this.LA(e+1),n[e]))continue t;return!0}return!1};{let t=(0,eT.Z)(e);if(1===t.length&&(0,p.Z)(t[0].categoryMatches)){let e=t[0].tokenTypeIdx;return function(){return this.LA(1).tokenTypeIdx===e}}{let e=(0,eb.Z)(t,(e,t,r)=>(e[t.tokenTypeIdx]=!0,(0,h.Z)(t.categoryMatches,t=>{e[t]=!0}),e),[]);return function(){return!0===e[this.LA(1).tokenTypeIdx]}}}}(a=u||(u={}))[a.OPTION=0]="OPTION",a[a.REPETITION=1]="REPETITION",a[a.REPETITION_MANDATORY=2]="REPETITION_MANDATORY",a[a.REPETITION_MANDATORY_WITH_SEPARATOR=3]="REPETITION_MANDATORY_WITH_SEPARATOR",a[a.REPETITION_WITH_SEPARATOR=4]="REPETITION_WITH_SEPARATOR",a[a.ALTERNATION=5]="ALTERNATION";class t5 extends ep{constructor(e,t,r){super(),this.topProd=e,this.targetOccurrence=t,this.targetProdType=r}startWalking(){return this.walk(this.topProd),this.restDef}checkIsTarget(e,t,r,n){return e.idx===this.targetOccurrence&&this.targetProdType===t&&(this.restDef=r.concat(n),!0)}walkOption(e,t,r){this.checkIsTarget(e,u.OPTION,t,r)||super.walkOption(e,t,r)}walkAtLeastOne(e,t,r){this.checkIsTarget(e,u.REPETITION_MANDATORY,t,r)||super.walkOption(e,t,r)}walkAtLeastOneSep(e,t,r){this.checkIsTarget(e,u.REPETITION_MANDATORY_WITH_SEPARATOR,t,r)||super.walkOption(e,t,r)}walkMany(e,t,r){this.checkIsTarget(e,u.REPETITION,t,r)||super.walkOption(e,t,r)}walkManySep(e,t,r){this.checkIsTarget(e,u.REPETITION_WITH_SEPARATOR,t,r)||super.walkOption(e,t,r)}}class t8 extends J{constructor(e,t,r){super(),this.targetOccurrence=e,this.targetProdType=t,this.targetRef=r,this.result=[]}checkIsTarget(e,t){e.idx===this.targetOccurrence&&this.targetProdType===t&&(void 0===this.targetRef||e===this.targetRef)&&(this.result=e.definition)}visitOption(e){this.checkIsTarget(e,u.OPTION)}visitRepetition(e){this.checkIsTarget(e,u.REPETITION)}visitRepetitionMandatory(e){this.checkIsTarget(e,u.REPETITION_MANDATORY)}visitRepetitionMandatoryWithSeparator(e){this.checkIsTarget(e,u.REPETITION_MANDATORY_WITH_SEPARATOR)}visitRepetitionWithSeparator(e){this.checkIsTarget(e,u.REPETITION_WITH_SEPARATOR)}visitAlternation(e){this.checkIsTarget(e,u.ALTERNATION)}}function re(e){let t=Array(e);for(let r=0;r<e;r++)t[r]=[];return t}function rt(e){let t=[""];for(let r=0;r<e.length;r++){let n=e[r],i=[];for(let e=0;e<t.length;e++){let r=t[e];i.push(r+"_"+n.tokenTypeIdx);for(let e=0;e<n.categoryMatches.length;e++){let t="_"+n.categoryMatches[e];i.push(r+t)}}t=i}return t}function rr(e,t){let r=(0,m.Z)(e,e=>t2([e],1)),n=re(r.length),i=(0,m.Z)(r,e=>{let t={};return(0,h.Z)(e,e=>{let r=rt(e.partialPath);(0,h.Z)(r,e=>{t[e]=!0})}),t}),a=r;for(let e=1;e<=t;e++){let r=a;a=re(r.length);for(let s=0;s<r.length;s++){let o=r[s];for(let r=0;r<o.length;r++){let l=o[r].partialPath,u=o[r].suffixDef,c=rt(l);if(function(e,t,r){for(let n=0;n<e.length;n++){if(n===r)continue;let i=e[n];for(let e=0;e<t.length;e++)if(!0===i[t[e]])return!1}return!0}(i,c,s)||(0,p.Z)(u)||l.length===t){let e=n[s];if(!1===ra(e,l)){e.push(l);for(let e=0;e<c.length;e++){let t=c[e];i[s][t]=!0}}}else{let t=t2(u,e+1,l);a[s]=a[s].concat(t),(0,h.Z)(t,e=>{let t=rt(e.partialPath);(0,h.Z)(t,e=>{i[s][e]=!0})})}}}}return n}function rn(e,t,r,n){let i=new t8(e,u.ALTERNATION,n);return t.accept(i),rr(i.result,r)}function ri(e,t,r,n){let i=new t8(e,r);t.accept(i);let a=i.result,s=new t5(t,e,r).startWalking();return rr([new j({definition:a}),new j({definition:s})],n)}function ra(e,t){r:for(let r=0;r<e.length;r++){let n=e[r];if(n.length===t.length){for(let e=0;e<n.length;e++){let r=t[e],i=n[e];if(!1==(r===i||void 0!==i.categoryMatchesMap[r.tokenTypeIdx]))continue r}return!0}}return!1}function rs(e){return ed(e,e=>ed(e,e=>ed(e,e=>(0,p.Z)(e.categoryMatches))))}function ro(e){return`${ef(e)}_#_${e.idx}_#_${rl(e)}`}function rl(e){return e instanceof Q?e.terminalType.name:e instanceof V?e.nonTerminalName:""}class ru extends J{constructor(){super(...arguments),this.allProductions=[]}visitNonTerminal(e){this.allProductions.push(e)}visitOption(e){this.allProductions.push(e)}visitRepetitionWithSeparator(e){this.allProductions.push(e)}visitRepetitionMandatory(e){this.allProductions.push(e)}visitRepetitionMandatoryWithSeparator(e){this.allProductions.push(e)}visitRepetition(e){this.allProductions.push(e)}visitAlternation(e){this.allProductions.push(e)}visitTerminal(e){this.allProductions.push(e)}}class rc extends J{constructor(){super(...arguments),this.alternations=[]}visitAlternation(e){this.alternations.push(e)}}class rd extends J{constructor(){super(...arguments),this.allProductions=[]}visitRepetitionWithSeparator(e){this.allProductions.push(e)}visitRepetitionMandatory(e){this.allProductions.push(e)}visitRepetitionMandatoryWithSeparator(e){this.allProductions.push(e)}visitRepetition(e){this.allProductions.push(e)}}let rh="MismatchedTokenException",rf="NoViableAltException",rp="EarlyExitException",rm="NotAllInputParsedException",rg=[rh,rf,rp,rm];function ry(e){return el(rg,e.name)}Object.freeze(rg);class rT extends Error{constructor(e,t){super(e),this.token=t,this.resyncedTokens=[],Object.setPrototypeOf(this,new.target.prototype),Error.captureStackTrace&&Error.captureStackTrace(this,this.constructor)}}class rv extends rT{constructor(e,t,r){super(e,t),this.previousToken=r,this.name=rh}}class rR extends rT{constructor(e,t,r){super(e,t),this.previousToken=r,this.name=rf}}class rE extends rT{constructor(e,t){super(e,t),this.name=rm}}class rA extends rT{constructor(e,t,r){super(e,t),this.previousToken=r,this.name=rp}}let rk={},r$="InRuleRecoveryException";class rx extends Error{constructor(e){super(e),this.name=r$}}function rI(e,t,r,n,i,a,s){let o=this.getKeyForAutomaticLookahead(n,i),l=this.firstAfterRepMap[o];if(void 0===l){let e=this.getCurrRuleFullName();l=new a(this.getGAstProductions()[e],i).startWalking(),this.firstAfterRepMap[o]=l}let u=l.token,c=l.occurrence,d=l.isEndOfRule;1===this.RULE_STACK.length&&d&&void 0===u&&(u=tP,c=1),void 0!==u&&void 0!==c&&this.shouldInRepetitionRecoveryBeTried(u,c,s)&&this.tryInRepetitionRecovery(e,t,r,u)}class rS{constructor(e){var t;this.maxLookahead=null!=(t=null==e?void 0:e.maxLookahead)?t:rV.maxLookahead}validate(e){let t=this.validateNoLeftRecursion(e.rules);if((0,p.Z)(t)){let r=this.validateEmptyOrAlternatives(e.rules);return[...t,...r,...this.validateAmbiguousAlternationAlternatives(e.rules,this.maxLookahead),...this.validateSomeNonEmptyLookaheadPath(e.rules,this.maxLookahead)]}return t}validateNoLeftRecursion(e){return(0,tB.Z)(e,e=>(function e(t,r,n,i=[]){let a=[],s=function e(t){let r=[];if((0,p.Z)(t))return r;let n=eK(t);if(n instanceof V)r.push(n.referencedRule);else if(n instanceof j||n instanceof W||n instanceof H||n instanceof z||n instanceof q||n instanceof Y)r=r.concat(e(n.definition));else if(n instanceof X)r=(0,eT.Z)((0,m.Z)(n.definition,t=>e(t.definition)));else if(n instanceof Q);else throw Error("non exhaustive match");let i=eh(n),a=t.length>1;if(!i||!a)return r;{let n=E(t);return r.concat(e(n))}}(r.definition);if((0,p.Z)(s))return[];{let r=t.name;el(s,t)&&a.push({message:n.buildLeftRecursionError({topLevelRule:t,leftRecursionPath:i}),type:d.LEFT_RECURSION,ruleName:r});let o=eB(s,i.concat([t])),l=(0,tB.Z)(o,r=>{let a=(0,y.Z)(i);return a.push(r),e(t,r,n,a)});return a.concat(l)}})(e,e,tU))}validateEmptyOrAlternatives(e){return(0,tB.Z)(e,e=>(function(e,t){let r=new rc;e.accept(r);let n=r.alternations;return(0,tB.Z)(n,r=>{let n=tz(r.definition);return(0,tB.Z)(n,(n,i)=>{let a=t6([n],[],tf,1);return(0,p.Z)(a)?[{message:t.buildEmptyAlternationError({topLevelRule:e,alternation:r,emptyChoiceIdx:i}),type:d.NONE_LAST_EMPTY_ALT,ruleName:e.name,occurrence:r.idx,alternative:i+1}]:[]})})})(e,tU))}validateAmbiguousAlternationAlternatives(e,t){return(0,tB.Z)(e,e=>(function(e,t,r){let n=new rc;e.accept(n);let i=n.alternations;return i=eC(i,e=>!0===e.ignoreAmbiguities),(0,tB.Z)(i,n=>{let i=rn(n.idx,e,n.maxLookahead||t,n),a=function(e,t,r,n){let i=[],a=(0,eb.Z)(e,(r,n,a)=>(!0===t.definition[a].ignoreAmbiguities||(0,h.Z)(n,n=>{let s=[a];(0,h.Z)(e,(e,r)=>{a!==r&&ra(e,n)&&!0!==t.definition[r].ignoreAmbiguities&&s.push(r)}),s.length>1&&!ra(i,n)&&(i.push(n),r.push({alts:s,path:n}))}),r),[]);return(0,m.Z)(a,e=>{let i=(0,m.Z)(e.alts,e=>e+1);return{message:n.buildAlternationAmbiguityError({topLevelRule:r,alternation:t,ambiguityIndices:i,prefixPath:e.path}),type:d.AMBIGUOUS_ALTS,ruleName:r.name,occurrence:t.idx,alternatives:e.alts}})}(i,n,e,r),s=function(e,t,r,n){let i=(0,eb.Z)(e,(e,t,r)=>{let n=(0,m.Z)(t,e=>({idx:r,path:e}));return e.concat(n)},[]);return eV((0,tB.Z)(i,e=>{if(!0===t.definition[e.idx].ignoreAmbiguities)return[];let a=e.idx,s=e.path,o=(0,eO.Z)(i,e=>{var r;return!0!==t.definition[e.idx].ignoreAmbiguities&&e.idx<a&&(r=e.path,r.length<s.length&&ed(r,(e,t)=>{let r=s[t];return e===r||r.categoryMatchesMap[e.tokenTypeIdx]}))});return(0,m.Z)(o,e=>{let i=[e.idx+1,a+1],s=0===t.idx?"":t.idx;return{message:n.buildAlternationPrefixAmbiguityError({topLevelRule:r,alternation:t,ambiguityIndices:i,prefixPath:e.path}),type:d.AMBIGUOUS_PREFIX_ALTS,ruleName:r.name,occurrence:s,alternatives:i}})}))}(i,n,e,r);return a.concat(s)})})(e,t,tU))}validateSomeNonEmptyLookaheadPath(e,t){let r=[];return(0,h.Z)(e,e=>{let n=new rd;e.accept(n);let i=n.allProductions;(0,h.Z)(i,n=>{let i=t9(n),a=n.maxLookahead||t,s=ri(n.idx,e,i,a)[0];if((0,p.Z)((0,eT.Z)(s))){let t=tU.buildEmptyRepetitionError({topLevelRule:e,repetition:n});r.push({message:t,type:d.NO_NON_EMPTY_LOOKAHEAD,ruleName:e.name})}})}),r}buildLookaheadForAlternation(e){return function(e,t,r,n,i,a){let s=rn(e,t,r),o=rs(s)?tp:tf;return a(s,n,o,i)}(e.prodOccurrence,e.rule,e.maxLookahead,e.hasPredicates,e.dynamicTokensEnabled,t7)}buildLookaheadForOptional(e){return function(e,t,r,n,i,a){let s=ri(e,t,i,r),o=rs(s)?tp:tf;return a(s[0],o,n)}(e.prodOccurrence,e.rule,e.maxLookahead,e.dynamicTokensEnabled,t9(e.prodType),t4)}}let rC=new class extends J{constructor(){super(...arguments),this.dslMethods={option:[],alternation:[],repetition:[],repetitionWithSeparator:[],repetitionMandatory:[],repetitionMandatoryWithSeparator:[]}}reset(){this.dslMethods={option:[],alternation:[],repetition:[],repetitionWithSeparator:[],repetitionMandatory:[],repetitionMandatoryWithSeparator:[]}}visitOption(e){this.dslMethods.option.push(e)}visitRepetitionWithSeparator(e){this.dslMethods.repetitionWithSeparator.push(e)}visitRepetitionMandatory(e){this.dslMethods.repetitionMandatory.push(e)}visitRepetitionMandatoryWithSeparator(e){this.dslMethods.repetitionMandatoryWithSeparator.push(e)}visitRepetition(e){this.dslMethods.repetition.push(e)}visitAlternation(e){this.dslMethods.alternation.push(e)}};function rN(e,t){!0===isNaN(e.startOffset)?(e.startOffset=t.startOffset,e.endOffset=t.endOffset):e.endOffset<t.endOffset==!0&&(e.endOffset=t.endOffset)}function rw(e,t){!0===isNaN(e.startOffset)?(e.startOffset=t.startOffset,e.startColumn=t.startColumn,e.startLine=t.startLine,e.endOffset=t.endOffset,e.endColumn=t.endColumn,e.endLine=t.endLine):e.endOffset<t.endOffset==!0&&(e.endOffset=t.endOffset,e.endColumn=t.endColumn,e.endLine=t.endLine)}function rL(e,t){Object.defineProperty(e,"name",{enumerable:!1,configurable:!0,writable:!1,value:t})}function rb(e,t){let r=(0,C.Z)(e),n=r.length;for(let i=0;i<n;i++){let n=e[r[i]],a=n.length;for(let e=0;e<a;e++){let r=n[e];void 0===r.tokenTypeIdx&&this[r.name](r.children,t)}}}(s=c||(c={}))[s.REDUNDANT_METHOD=0]="REDUNDANT_METHOD",s[s.MISSING_METHOD=1]="MISSING_METHOD";var rO=r(26129);let r_={description:"This Object indicates the Parser is during Recording Phase"};Object.freeze(r_);let rP=t_({name:"RECORDING_PHASE_TOKEN",pattern:tk.NA});ty([rP]);let rM=tM(rP,"This IToken indicates the Parser is in Recording Phase\n	See: https://chevrotain.io/docs/guide/internals.html#grammar-recording for details",-1,-1,-1,-1,-1,-1);Object.freeze(rM);let rD={name:"This CSTNode indicates the Parser is in Recording Phase\n	See: https://chevrotain.io/docs/guide/internals.html#grammar-recording for details",children:{}};function rZ(e,t,r,n=!1){rG(r);let i=(0,td.Z)(this.recordingProdStack),a=(0,eN.Z)(t)?t:t.DEF,s=new e({definition:[],idx:r});return n&&(s.separator=t.SEP),(0,g.Z)(t,"MAX_LOOKAHEAD")&&(s.maxLookahead=t.MAX_LOOKAHEAD),this.recordingProdStack.push(s),a.call(this),i.definition.push(s),this.recordingProdStack.pop(),r_}function rF(e,t){rG(t);let r=(0,td.Z)(this.recordingProdStack),n=!1===(0,en.Z)(e),i=!1===n?e:e.DEF,a=new X({definition:[],idx:t,ignoreAmbiguities:n&&!0===e.IGNORE_AMBIGUITIES});return(0,g.Z)(e,"MAX_LOOKAHEAD")&&(a.maxLookahead=e.MAX_LOOKAHEAD),a.hasPredicates=ea(i,e=>(0,eN.Z)(e.GATE)),r.definition.push(a),(0,h.Z)(i,e=>{let t=new j({definition:[]});a.definition.push(t),(0,g.Z)(e,"IGNORE_AMBIGUITIES")?t.ignoreAmbiguities=e.IGNORE_AMBIGUITIES:(0,g.Z)(e,"GATE")&&(t.ignoreAmbiguities=!0),this.recordingProdStack.push(t),e.ALT.call(this),this.recordingProdStack.pop()}),r_}function rU(e){return 0===e?"":`${e}`}function rG(e){if(e<0||e>255){let t=Error(`Invalid DSL Method idx value: <${e}>
	Idx value must be a none negative value smaller than ${256}`);throw t.KNOWN_RECORDER_ERROR=!0,t}}let rB=tM(tP,"",NaN,NaN,NaN,NaN,NaN,NaN);Object.freeze(rB);let rV=Object.freeze({recoveryEnabled:!1,maxLookahead:3,dynamicTokensEnabled:!1,outputCst:!0,errorMessageProvider:tZ,nodeLocationTracking:"none",traceInitPerf:!1,skipValidations:!1}),rK=Object.freeze({recoveryValueFunc:()=>void 0,resyncEnabled:!0});function rj(e){return function(){return e}}(o=d||(d={}))[o.INVALID_RULE_NAME=0]="INVALID_RULE_NAME",o[o.DUPLICATE_RULE_NAME=1]="DUPLICATE_RULE_NAME",o[o.INVALID_RULE_OVERRIDE=2]="INVALID_RULE_OVERRIDE",o[o.DUPLICATE_PRODUCTIONS=3]="DUPLICATE_PRODUCTIONS",o[o.UNRESOLVED_SUBRULE_REF=4]="UNRESOLVED_SUBRULE_REF",o[o.LEFT_RECURSION=5]="LEFT_RECURSION",o[o.NONE_LAST_EMPTY_ALT=6]="NONE_LAST_EMPTY_ALT",o[o.AMBIGUOUS_ALTS=7]="AMBIGUOUS_ALTS",o[o.CONFLICT_TOKENS_RULES_NAMESPACE=8]="CONFLICT_TOKENS_RULES_NAMESPACE",o[o.INVALID_TOKEN_NAME=9]="INVALID_TOKEN_NAME",o[o.NO_NON_EMPTY_LOOKAHEAD=10]="NO_NON_EMPTY_LOOKAHEAD",o[o.AMBIGUOUS_PREFIX_ALTS=11]="AMBIGUOUS_PREFIX_ALTS",o[o.TOO_MANY_ALTS=12]="TOO_MANY_ALTS",o[o.CUSTOM_LOOKAHEAD_VALIDATION=13]="CUSTOM_LOOKAHEAD_VALIDATION";class rW{static performSelfAnalysis(e){throw Error("The **static** `performSelfAnalysis` method has been deprecated.	\nUse the **instance** method with the same name instead.")}performSelfAnalysis(){this.TRACE_INIT("performSelfAnalysis",()=>{let e;this.selfAnalysisDone=!0;let t=this.className;this.TRACE_INIT("toFastProps",()=>{T(this)}),this.TRACE_INIT("Grammar Recording",()=>{try{this.enableRecording(),(0,h.Z)(this.definedRulesNames,e=>{let t,r=this[e].originalGrammarAction;this.TRACE_INIT(`${e} Rule`,()=>{t=this.topLevelRuleRecord(e,r)}),this.gastProductionsCache[e]=t})}finally{this.disableRecording()}});let r=[];if(this.TRACE_INIT("Grammar Resolving",()=>{r=function(e){let t=(0,e$.Z)(e,{errMsgProvider:tF}),r={};(0,h.Z)(e.rules,e=>{r[e.name]=e});let n=new tG(r,t.errMsgProvider);return n.resolveRefs(),n.errors}({rules:(0,f.Z)(this.gastProductionsCache)}),this.definitionErrors=this.definitionErrors.concat(r)}),this.TRACE_INIT("Grammar Validations",()=>{if((0,p.Z)(r)&&!1===this.skipValidations){var e;let r=(e={rules:(0,f.Z)(this.gastProductionsCache),tokenTypes:(0,f.Z)(this.tokensMap),errMsgProvider:tU,grammarName:t},function(e,t,r,n){let i=(0,tB.Z)(e,e=>(function(e,t){let r=new ru;e.accept(r);let n=P(tH(r.allProductions,ro),e=>e.length>1);return(0,m.Z)((0,f.Z)(n),r=>{let n=eK(r),i=t.buildDuplicateFoundError(e,r),a=ef(n),s={message:i,type:d.DUPLICATE_PRODUCTIONS,ruleName:e.name,dslName:a,occurrence:n.idx},o=rl(n);return o&&(s.parameter=o),s})})(e,r)),a=function(e,t,r){let n=[],i=(0,m.Z)(t,e=>e.name);return(0,h.Z)(e,e=>{let t=e.name;if(el(i,t)){let i=r.buildNamespaceConflictError(e);n.push({message:i,type:d.CONFLICT_TOKENS_RULES_NAMESPACE,ruleName:t})}}),n}(e,t,r),s=(0,tB.Z)(e,e=>(function(e,t){let r=new rc;e.accept(r);let n=r.alternations;return(0,tB.Z)(n,r=>r.definition.length>255?[{message:t.buildTooManyAlternativesError({topLevelRule:e,alternation:r}),type:d.TOO_MANY_ALTS,ruleName:e.name,occurrence:r.idx}]:[])})(e,r)),o=(0,tB.Z)(e,t=>(function(e,t,r,n){let i=[];if((0,eb.Z)(t,(t,r)=>r.name===e.name?t+1:t,0)>1){let t=n.buildDuplicateRuleNameError({topLevelRule:e,grammarName:r});i.push({message:t,type:d.DUPLICATE_RULE_NAME,ruleName:e.name})}return i})(t,e,n,r));return i.concat(a,s,o)}((e=(0,e$.Z)(e,{errMsgProvider:tU})).rules,e.tokenTypes,e.errMsgProvider,e.grammarName)),n=function(e){let t=e.lookaheadStrategy.validate({rules:e.rules,tokenTypes:e.tokenTypes,grammarName:e.grammarName});return(0,m.Z)(t,e=>Object.assign({type:d.CUSTOM_LOOKAHEAD_VALIDATION},e))}({lookaheadStrategy:this.lookaheadStrategy,rules:(0,f.Z)(this.gastProductionsCache),tokenTypes:(0,f.Z)(this.tokensMap),grammarName:t});this.definitionErrors=this.definitionErrors.concat(r,n)}}),(0,p.Z)(this.definitionErrors)&&(this.recoveryEnabled&&this.TRACE_INIT("computeAllProdsFollows",()=>{let e=function(e){let t={};return(0,h.Z)(e,e=>{w(t,new eE(e).startWalking())}),t}((0,f.Z)(this.gastProductionsCache));this.resyncFollows=e}),this.TRACE_INIT("ComputeLookaheadFunctions",()=>{var e,t;null==(t=(e=this.lookaheadStrategy).initialize)||t.call(e,{rules:(0,f.Z)(this.gastProductionsCache)}),this.preComputeLookaheadFunctions((0,f.Z)(this.gastProductionsCache))})),!rW.DEFER_DEFINITION_ERRORS_HANDLING&&!(0,p.Z)(this.definitionErrors))throw e=(0,m.Z)(this.definitionErrors,e=>e.message),Error(`Parser Definition Errors detected:
 ${e.join("\n-------------------------------\n")}`)})}constructor(e,t){if(this.definitionErrors=[],this.selfAnalysisDone=!1,this.initErrorHandler(t),this.initLexerAdapter(),this.initLooksAhead(t),this.initRecognizerEngine(e,t),this.initRecoverable(t),this.initTreeBuilder(t),this.initContentAssist(),this.initGastRecorder(t),this.initPerformanceTracer(t),(0,g.Z)(t,"ignoredIssues"))throw Error("The <ignoredIssues> IParserConfig property has been deprecated.\n	Please use the <IGNORE_AMBIGUITIES> flag on the relevant DSL method instead.\n	See: https://chevrotain.io/docs/guide/resolving_grammar_errors.html#IGNORING_AMBIGUITIES\n	For further details.");this.skipValidations=(0,g.Z)(t,"skipValidations")?t.skipValidations:rV.skipValidations}}rW.DEFER_DEFINITION_ERRORS_HANDLING=!1,[class{initRecoverable(e){this.firstAfterRepMap={},this.resyncFollows={},this.recoveryEnabled=(0,g.Z)(e,"recoveryEnabled")?e.recoveryEnabled:rV.recoveryEnabled,this.recoveryEnabled&&(this.attemptInRepetitionRecovery=rI)}getTokenToInsert(e){let t=tM(e,"",NaN,NaN,NaN,NaN,NaN,NaN);return t.isInsertedInRecovery=!0,t}canTokenTypeBeInsertedInRecovery(e){return!0}canTokenTypeBeDeletedInRecovery(e){return!0}tryInRepetitionRecovery(e,t,r,n){let i=this.findReSyncTokenType(),a=this.exportLexerState(),s=[],o=!1,l=this.LA(1),u=this.LA(1),c=()=>{let e=this.LA(0),t=new rv(this.errorMessageProvider.buildMismatchTokenMessage({expected:n,actual:l,previous:e,ruleName:this.getCurrRuleFullName()}),l,this.LA(0));t.resyncedTokens=tz(s),this.SAVE_ERROR(t)};for(;!o;)if(this.tokenMatcher(u,n))return void c();else if(r.call(this)){c(),e.apply(this,t);return}else this.tokenMatcher(u,i)?o=!0:(u=this.SKIP_TOKEN(),this.addToResyncTokens(u,s));this.importLexerState(a)}shouldInRepetitionRecoveryBeTried(e,t,r){return!(!1===r||this.tokenMatcher(this.LA(1),e)||this.isBackTracking()||this.canPerformInRuleRecovery(e,this.getFollowsForInRuleRecovery(e,t)))}getFollowsForInRuleRecovery(e,t){let r=this.getCurrentGrammarPath(e,t);return this.getNextPossibleTokenTypes(r)}tryInRuleRecovery(e,t){if(this.canRecoverWithSingleTokenInsertion(e,t))return this.getTokenToInsert(e);if(this.canRecoverWithSingleTokenDeletion(e)){let e=this.SKIP_TOKEN();return this.consumeToken(),e}throw new rx("sad sad panda")}canPerformInRuleRecovery(e,t){return this.canRecoverWithSingleTokenInsertion(e,t)||this.canRecoverWithSingleTokenDeletion(e)}canRecoverWithSingleTokenInsertion(e,t){if(!this.canTokenTypeBeInsertedInRecovery(e)||(0,p.Z)(t))return!1;let r=this.LA(1);return void 0!==(0,ej.Z)(t,e=>this.tokenMatcher(r,e))}canRecoverWithSingleTokenDeletion(e){return!!this.canTokenTypeBeDeletedInRecovery(e)&&this.tokenMatcher(this.LA(2),e)}isInCurrentRuleReSyncSet(e){let t=this.getCurrFollowKey();return el(this.getFollowSetFromFollowKey(t),e)}findReSyncTokenType(){let e=this.flattenFollowSet(),t=this.LA(1),r=2;for(;;){let n=(0,ej.Z)(e,e=>tf(t,e));if(void 0!==n)return n;t=this.LA(r),r++}}getCurrFollowKey(){if(1===this.RULE_STACK.length)return rk;let e=this.getLastExplicitRuleShortName(),t=this.getLastExplicitRuleOccurrenceIndex(),r=this.getPreviousExplicitRuleShortName();return{ruleName:this.shortRuleNameToFullName(e),idxInCallingRule:t,inRule:this.shortRuleNameToFullName(r)}}buildFullFollowKeyStack(){let e=this.RULE_STACK,t=this.RULE_OCCURRENCE_STACK;return(0,m.Z)(e,(r,n)=>0===n?rk:{ruleName:this.shortRuleNameToFullName(r),idxInCallingRule:t[n],inRule:this.shortRuleNameToFullName(e[n-1])})}flattenFollowSet(){let e=(0,m.Z)(this.buildFullFollowKeyStack(),e=>this.getFollowSetFromFollowKey(e));return(0,eT.Z)(e)}getFollowSetFromFollowKey(e){if(e===rk)return[tP];let t=e.ruleName+e.idxInCallingRule+eR+e.inRule;return this.resyncFollows[t]}addToResyncTokens(e,t){return this.tokenMatcher(e,tP)||t.push(e),t}reSyncTo(e){let t=[],r=this.LA(1);for(;!1===this.tokenMatcher(r,e);)r=this.SKIP_TOKEN(),this.addToResyncTokens(r,t);return tz(t)}attemptInRepetitionRecovery(e,t,r,n,i,a,s){}getCurrentGrammarPath(e,t){let r=this.getHumanReadableRuleStack();return{ruleStack:r,occurrenceStack:(0,y.Z)(this.RULE_OCCURRENCE_STACK),lastTok:e,lastTokOccurrence:t}}getHumanReadableRuleStack(){return(0,m.Z)(this.RULE_STACK,e=>this.shortRuleNameToFullName(e))}},class{initLooksAhead(e){this.dynamicTokensEnabled=(0,g.Z)(e,"dynamicTokensEnabled")?e.dynamicTokensEnabled:rV.dynamicTokensEnabled,this.maxLookahead=(0,g.Z)(e,"maxLookahead")?e.maxLookahead:rV.maxLookahead,this.lookaheadStrategy=(0,g.Z)(e,"lookaheadStrategy")?e.lookaheadStrategy:new rS({maxLookahead:this.maxLookahead}),this.lookAheadFuncsCache=new Map}preComputeLookaheadFunctions(e){(0,h.Z)(e,e=>{this.TRACE_INIT(`${e.name} Rule Lookahead`,()=>{let{alternation:t,repetition:r,option:n,repetitionMandatory:i,repetitionMandatoryWithSeparator:a,repetitionWithSeparator:s}=function(e){rC.reset(),e.accept(rC);let t=rC.dslMethods;return rC.reset(),t}(e);(0,h.Z)(t,t=>{let r=0===t.idx?"":t.idx;this.TRACE_INIT(`${ef(t)}${r}`,()=>{var r;let n=this.lookaheadStrategy.buildLookaheadForAlternation({prodOccurrence:t.idx,rule:e,maxLookahead:t.maxLookahead||this.maxLookahead,hasPredicates:t.hasPredicates,dynamicTokensEnabled:this.dynamicTokensEnabled}),i=(r=this.fullRuleNameToShort[e.name],256|t.idx|r);this.setLaFuncCache(i,n)})}),(0,h.Z)(r,t=>{this.computeLookaheadFunc(e,t.idx,768,"Repetition",t.maxLookahead,ef(t))}),(0,h.Z)(n,t=>{this.computeLookaheadFunc(e,t.idx,512,"Option",t.maxLookahead,ef(t))}),(0,h.Z)(i,t=>{this.computeLookaheadFunc(e,t.idx,1024,"RepetitionMandatory",t.maxLookahead,ef(t))}),(0,h.Z)(a,t=>{this.computeLookaheadFunc(e,t.idx,1536,"RepetitionMandatoryWithSeparator",t.maxLookahead,ef(t))}),(0,h.Z)(s,t=>{this.computeLookaheadFunc(e,t.idx,1280,"RepetitionWithSeparator",t.maxLookahead,ef(t))})})})}computeLookaheadFunc(e,t,r,n,i,a){this.TRACE_INIT(`${a}${0===t?"":t}`,()=>{let a=this.lookaheadStrategy.buildLookaheadForOptional({prodOccurrence:t,rule:e,maxLookahead:i||this.maxLookahead,dynamicTokensEnabled:this.dynamicTokensEnabled,prodType:n}),s=t|r|this.fullRuleNameToShort[e.name];this.setLaFuncCache(s,a)})}getKeyForAutomaticLookahead(e,t){return t|e|this.getLastExplicitRuleShortName()}getLaFuncFromCache(e){return this.lookAheadFuncsCache.get(e)}setLaFuncCache(e,t){this.lookAheadFuncsCache.set(e,t)}},class{initTreeBuilder(e){if(this.CST_STACK=[],this.outputCst=e.outputCst,this.nodeLocationTracking=(0,g.Z)(e,"nodeLocationTracking")?e.nodeLocationTracking:rV.nodeLocationTracking,this.outputCst)if(/full/i.test(this.nodeLocationTracking))this.recoveryEnabled?(this.setNodeLocationFromToken=rw,this.setNodeLocationFromNode=rw,this.cstPostRule=tc.Z,this.setInitialNodeLocation=this.setInitialNodeLocationFullRecovery):(this.setNodeLocationFromToken=tc.Z,this.setNodeLocationFromNode=tc.Z,this.cstPostRule=this.cstPostRuleFull,this.setInitialNodeLocation=this.setInitialNodeLocationFullRegular);else if(/onlyOffset/i.test(this.nodeLocationTracking))this.recoveryEnabled?(this.setNodeLocationFromToken=rN,this.setNodeLocationFromNode=rN,this.cstPostRule=tc.Z,this.setInitialNodeLocation=this.setInitialNodeLocationOnlyOffsetRecovery):(this.setNodeLocationFromToken=tc.Z,this.setNodeLocationFromNode=tc.Z,this.cstPostRule=this.cstPostRuleOnlyOffset,this.setInitialNodeLocation=this.setInitialNodeLocationOnlyOffsetRegular);else if(/none/i.test(this.nodeLocationTracking))this.setNodeLocationFromToken=tc.Z,this.setNodeLocationFromNode=tc.Z,this.cstPostRule=tc.Z,this.setInitialNodeLocation=tc.Z;else throw Error(`Invalid <nodeLocationTracking> config option: "${e.nodeLocationTracking}"`);else this.cstInvocationStateUpdate=tc.Z,this.cstFinallyStateUpdate=tc.Z,this.cstPostTerminal=tc.Z,this.cstPostNonTerminal=tc.Z,this.cstPostRule=tc.Z}setInitialNodeLocationOnlyOffsetRecovery(e){e.location={startOffset:NaN,endOffset:NaN}}setInitialNodeLocationOnlyOffsetRegular(e){e.location={startOffset:this.LA(1).startOffset,endOffset:NaN}}setInitialNodeLocationFullRecovery(e){e.location={startOffset:NaN,startLine:NaN,startColumn:NaN,endOffset:NaN,endLine:NaN,endColumn:NaN}}setInitialNodeLocationFullRegular(e){let t=this.LA(1);e.location={startOffset:t.startOffset,startLine:t.startLine,startColumn:t.startColumn,endOffset:NaN,endLine:NaN,endColumn:NaN}}cstInvocationStateUpdate(e){let t={name:e,children:Object.create(null)};this.setInitialNodeLocation(t),this.CST_STACK.push(t)}cstFinallyStateUpdate(){this.CST_STACK.pop()}cstPostRuleFull(e){let t=this.LA(0),r=e.location;r.startOffset<=t.startOffset==!0?(r.endOffset=t.endOffset,r.endLine=t.endLine,r.endColumn=t.endColumn):(r.startOffset=NaN,r.startLine=NaN,r.startColumn=NaN)}cstPostRuleOnlyOffset(e){let t=this.LA(0),r=e.location;r.startOffset<=t.startOffset==!0?r.endOffset=t.endOffset:r.startOffset=NaN}cstPostTerminal(e,t){let r=this.CST_STACK[this.CST_STACK.length-1];void 0===r.children[e]?r.children[e]=[t]:r.children[e].push(t),this.setNodeLocationFromToken(r.location,t)}cstPostNonTerminal(e,t){let r=this.CST_STACK[this.CST_STACK.length-1];void 0===r.children[t]?r.children[t]=[e]:r.children[t].push(e),this.setNodeLocationFromNode(r.location,e.location)}getBaseCstVisitorConstructor(){if((0,eA.Z)(this.baseCstVisitorConstructor)){let e=function(e,t){let r=function(){};return rL(r,e+"BaseSemantics"),r.prototype={visit:function(e,t){if((0,en.Z)(e)&&(e=e[0]),!(0,eA.Z)(e))return this[e.name](e.children,t)},validateVisitor:function(){let e=function(e,t){var r=e,n=t;let i=(0,eO.Z)(n,e=>!1===(0,eN.Z)(r[e]));return eV((0,m.Z)(i,e=>({msg:`Missing visitor method: <${e}> on ${r.constructor.name} CST Visitor.`,type:c.MISSING_METHOD,methodName:e})))}(this,t);if(!(0,p.Z)(e)){let t=(0,m.Z)(e,e=>e.msg);throw Error(`Errors Detected in CST Visitor <${this.constructor.name}>:
	${t.join("\n\n").replace(/\n/g,"\n	")}`)}}},r.prototype.constructor=r,r._RULE_NAMES=t,r}(this.className,(0,C.Z)(this.gastProductionsCache));return this.baseCstVisitorConstructor=e,e}return this.baseCstVisitorConstructor}getBaseCstVisitorConstructorWithDefaults(){if((0,eA.Z)(this.baseCstVisitorWithDefaultsConstructor)){let e=function(e,t,r){let n=function(){};rL(n,e+"BaseSemanticsWithDefaults");let i=Object.create(r.prototype);return(0,h.Z)(t,e=>{i[e]=rb}),n.prototype=i,n.prototype.constructor=n,n}(this.className,(0,C.Z)(this.gastProductionsCache),this.getBaseCstVisitorConstructor());return this.baseCstVisitorWithDefaultsConstructor=e,e}return this.baseCstVisitorWithDefaultsConstructor}getLastExplicitRuleShortName(){let e=this.RULE_STACK;return e[e.length-1]}getPreviousExplicitRuleShortName(){let e=this.RULE_STACK;return e[e.length-2]}getLastExplicitRuleOccurrenceIndex(){let e=this.RULE_OCCURRENCE_STACK;return e[e.length-1]}},class{initLexerAdapter(){this.tokVector=[],this.tokVectorLength=0,this.currIdx=-1}set input(e){if(!0!==this.selfAnalysisDone)throw Error("Missing <performSelfAnalysis> invocation at the end of the Parser's constructor.");this.reset(),this.tokVector=e,this.tokVectorLength=e.length}get input(){return this.tokVector}SKIP_TOKEN(){return this.currIdx<=this.tokVector.length-2?(this.consumeToken(),this.LA(1)):rB}LA(e){let t=this.currIdx+e;return t<0||this.tokVectorLength<=t?rB:this.tokVector[t]}consumeToken(){this.currIdx++}exportLexerState(){return this.currIdx}importLexerState(e){this.currIdx=e}resetLexerState(){this.currIdx=-1}moveToTerminatedState(){this.currIdx=this.tokVector.length-1}getLexerPosition(){return this.exportLexerState()}},class{initRecognizerEngine(e,t){if(this.className=this.constructor.name,this.shortRuleNameToFull={},this.fullRuleNameToShort={},this.ruleShortNameIdx=256,this.tokenMatcher=tp,this.subruleIdx=0,this.definedRulesNames=[],this.tokensMap={},this.isBackTrackingStack=[],this.RULE_STACK=[],this.RULE_OCCURRENCE_STACK=[],this.gastProductionsCache={},(0,g.Z)(t,"serializedGrammar"))throw Error("The Parser's configuration can no longer contain a <serializedGrammar> property.\n	See: https://chevrotain.io/docs/changes/BREAKING_CHANGES.html#_6-0-0\n	For Further details.");if((0,en.Z)(e)){if((0,p.Z)(e))throw Error("A Token Vocabulary cannot be empty.\n	Note that the first argument for the parser constructor\n	is no longer a Token vector (since v4.0).");if("number"==typeof e[0].startOffset)throw Error("The Parser constructor no longer accepts a token vector as the first argument.\n	See: https://chevrotain.io/docs/changes/BREAKING_CHANGES.html#_4-0-0\n	For Further details.")}if((0,en.Z)(e))this.tokensMap=(0,eb.Z)(e,(e,t)=>(e[t.name]=t,e),{});else if((0,g.Z)(e,"modes")&&ed((0,eT.Z)((0,f.Z)(e.modes)),tR)){let t=ey((0,eT.Z)((0,f.Z)(e.modes)));this.tokensMap=(0,eb.Z)(t,(e,t)=>(e[t.name]=t,e),{})}else if((0,rO.Z)(e))this.tokensMap=(0,y.Z)(e);else throw Error("<tokensDictionary> argument must be An Array of Token constructors, A dictionary of Token constructors or an IMultiModeLexerDefinition");this.tokensMap.EOF=tP;let r=ed((0,g.Z)(e,"modes")?(0,eT.Z)((0,f.Z)(e.modes)):(0,f.Z)(e),e=>(0,p.Z)(e.categoryMatches));this.tokenMatcher=r?tp:tf,ty((0,f.Z)(this.tokensMap))}defineRule(e,t,r){let n;if(this.selfAnalysisDone)throw Error(`Grammar rule <${e}> may not be defined after the 'performSelfAnalysis' method has been called'
Make sure that all grammar rule definitions are done before 'performSelfAnalysis' is called.`);let i=(0,g.Z)(r,"resyncEnabled")?r.resyncEnabled:rK.resyncEnabled,a=(0,g.Z)(r,"recoveryValueFunc")?r.recoveryValueFunc:rK.recoveryValueFunc,s=this.ruleShortNameIdx<<12;return this.ruleShortNameIdx++,this.shortRuleNameToFull[s]=e,this.fullRuleNameToShort[e]=s,Object.assign(!0===this.outputCst?function(...r){try{this.ruleInvocationStateUpdate(s,e,this.subruleIdx),t.apply(this,r);let n=this.CST_STACK[this.CST_STACK.length-1];return this.cstPostRule(n),n}catch(e){return this.invokeRuleCatch(e,i,a)}finally{this.ruleFinallyStateUpdate()}}:function(...r){try{return this.ruleInvocationStateUpdate(s,e,this.subruleIdx),t.apply(this,r)}catch(e){return this.invokeRuleCatch(e,i,a)}finally{this.ruleFinallyStateUpdate()}},{ruleName:e,originalGrammarAction:t})}invokeRuleCatch(e,t,r){let n=1===this.RULE_STACK.length,i=t&&!this.isBackTracking()&&this.recoveryEnabled;if(ry(e)){if(i){let t=this.findReSyncTokenType();if(this.isInCurrentRuleReSyncSet(t)){if(e.resyncedTokens=this.reSyncTo(t),!this.outputCst)return r(e);{let e=this.CST_STACK[this.CST_STACK.length-1];return e.recoveredNode=!0,e}}if(this.outputCst){let t=this.CST_STACK[this.CST_STACK.length-1];t.recoveredNode=!0,e.partialCstResult=t}throw e}if(n)return this.moveToTerminatedState(),r(e)}throw e}optionInternal(e,t){let r=this.getKeyForAutomaticLookahead(512,t);return this.optionInternalLogic(e,t,r)}optionInternalLogic(e,t,r){let n,i=this.getLaFuncFromCache(r);if("function"!=typeof e){n=e.DEF;let t=e.GATE;if(void 0!==t){let e=i;i=()=>t.call(this)&&e.call(this)}}else n=e;if(!0===i.call(this))return n.call(this)}atLeastOneInternal(e,t){let r=this.getKeyForAutomaticLookahead(1024,e);return this.atLeastOneInternalLogic(e,t,r)}atLeastOneInternalLogic(e,t,r){let n,i=this.getLaFuncFromCache(r);if("function"!=typeof t){n=t.DEF;let e=t.GATE;if(void 0!==e){let t=i;i=()=>e.call(this)&&t.call(this)}}else n=t;if(!0===i.call(this)){let e=this.doSingleRepetition(n);for(;!0===i.call(this)&&!0===e;)e=this.doSingleRepetition(n)}else throw this.raiseEarlyExitException(e,u.REPETITION_MANDATORY,t.ERR_MSG);this.attemptInRepetitionRecovery(this.atLeastOneInternal,[e,t],i,1024,e,t0)}atLeastOneSepFirstInternal(e,t){let r=this.getKeyForAutomaticLookahead(1536,e);this.atLeastOneSepFirstInternalLogic(e,t,r)}atLeastOneSepFirstInternalLogic(e,t,r){let n=t.DEF,i=t.SEP;if(!0===this.getLaFuncFromCache(r).call(this)){n.call(this);let t=()=>this.tokenMatcher(this.LA(1),i);for(;!0===this.tokenMatcher(this.LA(1),i);)this.CONSUME(i),n.call(this);this.attemptInRepetitionRecovery(this.repetitionSepSecondInternal,[e,i,t,n,t1],t,1536,e,t1)}else throw this.raiseEarlyExitException(e,u.REPETITION_MANDATORY_WITH_SEPARATOR,t.ERR_MSG)}manyInternal(e,t){let r=this.getKeyForAutomaticLookahead(768,e);return this.manyInternalLogic(e,t,r)}manyInternalLogic(e,t,r){let n,i=this.getLaFuncFromCache(r);if("function"!=typeof t){n=t.DEF;let e=t.GATE;if(void 0!==e){let t=i;i=()=>e.call(this)&&t.call(this)}}else n=t;let a=!0;for(;!0===i.call(this)&&!0===a;)a=this.doSingleRepetition(n);this.attemptInRepetitionRecovery(this.manyInternal,[e,t],i,768,e,tQ,a)}manySepFirstInternal(e,t){let r=this.getKeyForAutomaticLookahead(1280,e);this.manySepFirstInternalLogic(e,t,r)}manySepFirstInternalLogic(e,t,r){let n=t.DEF,i=t.SEP;if(!0===this.getLaFuncFromCache(r).call(this)){n.call(this);let t=()=>this.tokenMatcher(this.LA(1),i);for(;!0===this.tokenMatcher(this.LA(1),i);)this.CONSUME(i),n.call(this);this.attemptInRepetitionRecovery(this.repetitionSepSecondInternal,[e,i,t,n,tJ],t,1280,e,tJ)}}repetitionSepSecondInternal(e,t,r,n,i){for(;r();)this.CONSUME(t),n.call(this);this.attemptInRepetitionRecovery(this.repetitionSepSecondInternal,[e,t,r,n,i],r,1536,e,i)}doSingleRepetition(e){let t=this.getLexerPosition();return e.call(this),this.getLexerPosition()>t}orInternal(e,t){let r=this.getKeyForAutomaticLookahead(256,t),n=(0,en.Z)(e)?e:e.DEF,i=this.getLaFuncFromCache(r).call(this,n);if(void 0!==i)return n[i].ALT.call(this);this.raiseNoAltException(t,e.ERR_MSG)}ruleFinallyStateUpdate(){if(this.RULE_STACK.pop(),this.RULE_OCCURRENCE_STACK.pop(),this.cstFinallyStateUpdate(),0===this.RULE_STACK.length&&!1===this.isAtEndOfInput()){let e=this.LA(1),t=this.errorMessageProvider.buildNotAllInputParsedMessage({firstRedundant:e,ruleName:this.getCurrRuleFullName()});this.SAVE_ERROR(new rE(t,e))}}subruleInternal(e,t,r){let n;try{let i=void 0!==r?r.ARGS:void 0;return this.subruleIdx=t,n=e.apply(this,i),this.cstPostNonTerminal(n,void 0!==r&&void 0!==r.LABEL?r.LABEL:e.ruleName),n}catch(t){throw this.subruleInternalError(t,r,e.ruleName)}}subruleInternalError(e,t,r){throw ry(e)&&void 0!==e.partialCstResult&&(this.cstPostNonTerminal(e.partialCstResult,void 0!==t&&void 0!==t.LABEL?t.LABEL:r),delete e.partialCstResult),e}consumeInternal(e,t,r){let n;try{let t=this.LA(1);!0===this.tokenMatcher(t,e)?(this.consumeToken(),n=t):this.consumeInternalError(e,t,r)}catch(r){n=this.consumeInternalRecovery(e,t,r)}return this.cstPostTerminal(void 0!==r&&void 0!==r.LABEL?r.LABEL:e.name,n),n}consumeInternalError(e,t,r){let n,i=this.LA(0);throw n=void 0!==r&&r.ERR_MSG?r.ERR_MSG:this.errorMessageProvider.buildMismatchTokenMessage({expected:e,actual:t,previous:i,ruleName:this.getCurrRuleFullName()}),this.SAVE_ERROR(new rv(n,t,i))}consumeInternalRecovery(e,t,r){if(this.recoveryEnabled&&"MismatchedTokenException"===r.name&&!this.isBackTracking()){let n=this.getFollowsForInRuleRecovery(e,t);try{return this.tryInRuleRecovery(e,n)}catch(e){if(e.name===r$)throw r;throw e}}throw r}saveRecogState(){let e=this.errors,t=(0,y.Z)(this.RULE_STACK);return{errors:e,lexerState:this.exportLexerState(),RULE_STACK:t,CST_STACK:this.CST_STACK}}reloadRecogState(e){this.errors=e.errors,this.importLexerState(e.lexerState),this.RULE_STACK=e.RULE_STACK}ruleInvocationStateUpdate(e,t,r){this.RULE_OCCURRENCE_STACK.push(r),this.RULE_STACK.push(e),this.cstInvocationStateUpdate(t)}isBackTracking(){return 0!==this.isBackTrackingStack.length}getCurrRuleFullName(){let e=this.getLastExplicitRuleShortName();return this.shortRuleNameToFull[e]}shortRuleNameToFullName(e){return this.shortRuleNameToFull[e]}isAtEndOfInput(){return this.tokenMatcher(this.LA(1),tP)}reset(){this.resetLexerState(),this.subruleIdx=0,this.isBackTrackingStack=[],this.errors=[],this.RULE_STACK=[],this.CST_STACK=[],this.RULE_OCCURRENCE_STACK=[]}},class{ACTION(e){return e.call(this)}consume(e,t,r){return this.consumeInternal(t,e,r)}subrule(e,t,r){return this.subruleInternal(t,e,r)}option(e,t){return this.optionInternal(t,e)}or(e,t){return this.orInternal(t,e)}many(e,t){return this.manyInternal(e,t)}atLeastOne(e,t){return this.atLeastOneInternal(e,t)}CONSUME(e,t){return this.consumeInternal(e,0,t)}CONSUME1(e,t){return this.consumeInternal(e,1,t)}CONSUME2(e,t){return this.consumeInternal(e,2,t)}CONSUME3(e,t){return this.consumeInternal(e,3,t)}CONSUME4(e,t){return this.consumeInternal(e,4,t)}CONSUME5(e,t){return this.consumeInternal(e,5,t)}CONSUME6(e,t){return this.consumeInternal(e,6,t)}CONSUME7(e,t){return this.consumeInternal(e,7,t)}CONSUME8(e,t){return this.consumeInternal(e,8,t)}CONSUME9(e,t){return this.consumeInternal(e,9,t)}SUBRULE(e,t){return this.subruleInternal(e,0,t)}SUBRULE1(e,t){return this.subruleInternal(e,1,t)}SUBRULE2(e,t){return this.subruleInternal(e,2,t)}SUBRULE3(e,t){return this.subruleInternal(e,3,t)}SUBRULE4(e,t){return this.subruleInternal(e,4,t)}SUBRULE5(e,t){return this.subruleInternal(e,5,t)}SUBRULE6(e,t){return this.subruleInternal(e,6,t)}SUBRULE7(e,t){return this.subruleInternal(e,7,t)}SUBRULE8(e,t){return this.subruleInternal(e,8,t)}SUBRULE9(e,t){return this.subruleInternal(e,9,t)}OPTION(e){return this.optionInternal(e,0)}OPTION1(e){return this.optionInternal(e,1)}OPTION2(e){return this.optionInternal(e,2)}OPTION3(e){return this.optionInternal(e,3)}OPTION4(e){return this.optionInternal(e,4)}OPTION5(e){return this.optionInternal(e,5)}OPTION6(e){return this.optionInternal(e,6)}OPTION7(e){return this.optionInternal(e,7)}OPTION8(e){return this.optionInternal(e,8)}OPTION9(e){return this.optionInternal(e,9)}OR(e){return this.orInternal(e,0)}OR1(e){return this.orInternal(e,1)}OR2(e){return this.orInternal(e,2)}OR3(e){return this.orInternal(e,3)}OR4(e){return this.orInternal(e,4)}OR5(e){return this.orInternal(e,5)}OR6(e){return this.orInternal(e,6)}OR7(e){return this.orInternal(e,7)}OR8(e){return this.orInternal(e,8)}OR9(e){return this.orInternal(e,9)}MANY(e){this.manyInternal(0,e)}MANY1(e){this.manyInternal(1,e)}MANY2(e){this.manyInternal(2,e)}MANY3(e){this.manyInternal(3,e)}MANY4(e){this.manyInternal(4,e)}MANY5(e){this.manyInternal(5,e)}MANY6(e){this.manyInternal(6,e)}MANY7(e){this.manyInternal(7,e)}MANY8(e){this.manyInternal(8,e)}MANY9(e){this.manyInternal(9,e)}MANY_SEP(e){this.manySepFirstInternal(0,e)}MANY_SEP1(e){this.manySepFirstInternal(1,e)}MANY_SEP2(e){this.manySepFirstInternal(2,e)}MANY_SEP3(e){this.manySepFirstInternal(3,e)}MANY_SEP4(e){this.manySepFirstInternal(4,e)}MANY_SEP5(e){this.manySepFirstInternal(5,e)}MANY_SEP6(e){this.manySepFirstInternal(6,e)}MANY_SEP7(e){this.manySepFirstInternal(7,e)}MANY_SEP8(e){this.manySepFirstInternal(8,e)}MANY_SEP9(e){this.manySepFirstInternal(9,e)}AT_LEAST_ONE(e){this.atLeastOneInternal(0,e)}AT_LEAST_ONE1(e){return this.atLeastOneInternal(1,e)}AT_LEAST_ONE2(e){this.atLeastOneInternal(2,e)}AT_LEAST_ONE3(e){this.atLeastOneInternal(3,e)}AT_LEAST_ONE4(e){this.atLeastOneInternal(4,e)}AT_LEAST_ONE5(e){this.atLeastOneInternal(5,e)}AT_LEAST_ONE6(e){this.atLeastOneInternal(6,e)}AT_LEAST_ONE7(e){this.atLeastOneInternal(7,e)}AT_LEAST_ONE8(e){this.atLeastOneInternal(8,e)}AT_LEAST_ONE9(e){this.atLeastOneInternal(9,e)}AT_LEAST_ONE_SEP(e){this.atLeastOneSepFirstInternal(0,e)}AT_LEAST_ONE_SEP1(e){this.atLeastOneSepFirstInternal(1,e)}AT_LEAST_ONE_SEP2(e){this.atLeastOneSepFirstInternal(2,e)}AT_LEAST_ONE_SEP3(e){this.atLeastOneSepFirstInternal(3,e)}AT_LEAST_ONE_SEP4(e){this.atLeastOneSepFirstInternal(4,e)}AT_LEAST_ONE_SEP5(e){this.atLeastOneSepFirstInternal(5,e)}AT_LEAST_ONE_SEP6(e){this.atLeastOneSepFirstInternal(6,e)}AT_LEAST_ONE_SEP7(e){this.atLeastOneSepFirstInternal(7,e)}AT_LEAST_ONE_SEP8(e){this.atLeastOneSepFirstInternal(8,e)}AT_LEAST_ONE_SEP9(e){this.atLeastOneSepFirstInternal(9,e)}RULE(e,t,r=rK){if(el(this.definedRulesNames,e)){let t={message:tU.buildDuplicateRuleNameError({topLevelRule:e,grammarName:this.className}),type:d.DUPLICATE_RULE_NAME,ruleName:e};this.definitionErrors.push(t)}this.definedRulesNames.push(e);let n=this.defineRule(e,t,r);return this[e]=n,n}OVERRIDE_RULE(e,t,r=rK){let n=function(e,t,r){let n=[];return el(t,e)||n.push({message:`Invalid rule override, rule: ->${e}<- cannot be overridden in the grammar: ->${r}<-as it is not defined in any of the super grammars `,type:d.INVALID_RULE_OVERRIDE,ruleName:e}),n}(e,this.definedRulesNames,this.className);this.definitionErrors=this.definitionErrors.concat(n);let i=this.defineRule(e,t,r);return this[e]=i,i}BACKTRACK(e,t){return function(){this.isBackTrackingStack.push(1);let r=this.saveRecogState();try{return e.apply(this,t),!0}catch(e){if(ry(e))return!1;throw e}finally{this.reloadRecogState(r),this.isBackTrackingStack.pop()}}}getGAstProductions(){return this.gastProductionsCache}getSerializedGastProductions(){var e;return e=(0,f.Z)(this.gastProductionsCache),(0,m.Z)(e,function e(t){function r(t){return(0,m.Z)(t,e)}if(t instanceof V){let e={type:"NonTerminal",name:t.nonTerminalName,idx:t.idx};return(0,A.Z)(t.label)&&(e.label=t.label),e}if(t instanceof j)return{type:"Alternative",definition:r(t.definition)};if(t instanceof W)return{type:"Option",idx:t.idx,definition:r(t.definition)};if(t instanceof H)return{type:"RepetitionMandatory",idx:t.idx,definition:r(t.definition)};if(t instanceof z)return{type:"RepetitionMandatoryWithSeparator",idx:t.idx,separator:e(new Q({terminalType:t.separator})),definition:r(t.definition)};else if(t instanceof q)return{type:"RepetitionWithSeparator",idx:t.idx,separator:e(new Q({terminalType:t.separator})),definition:r(t.definition)};else if(t instanceof Y)return{type:"Repetition",idx:t.idx,definition:r(t.definition)};else if(t instanceof X)return{type:"Alternation",idx:t.idx,definition:r(t.definition)};else if(t instanceof Q){var n,i;let e={type:"Terminal",name:t.terminalType.name,label:(i=n=t.terminalType,(0,A.Z)(i.LABEL)&&""!==i.LABEL)?n.LABEL:n.name,idx:t.idx};(0,A.Z)(t.label)&&(e.terminalLabel=t.label);let r=t.terminalType.PATTERN;return t.terminalType.PATTERN&&(e.pattern=G(r)?r.source:r),e}else if(t instanceof K)return{type:"Rule",name:t.name,orgText:t.orgText,definition:r(t.definition)};else throw Error("non exhaustive match")})}},class{initErrorHandler(e){this._errors=[],this.errorMessageProvider=(0,g.Z)(e,"errorMessageProvider")?e.errorMessageProvider:rV.errorMessageProvider}SAVE_ERROR(e){if(ry(e))return e.context={ruleStack:this.getHumanReadableRuleStack(),ruleOccurrenceStack:(0,y.Z)(this.RULE_OCCURRENCE_STACK)},this._errors.push(e),e;throw Error("Trying to save an Error which is not a RecognitionException")}get errors(){return(0,y.Z)(this._errors)}set errors(e){this._errors=e}raiseEarlyExitException(e,t,r){let n=this.getCurrRuleFullName(),i=ri(e,this.getGAstProductions()[n],t,this.maxLookahead)[0],a=[];for(let e=1;e<=this.maxLookahead;e++)a.push(this.LA(e));let s=this.errorMessageProvider.buildEarlyExitMessage({expectedIterationPaths:i,actual:a,previous:this.LA(0),customUserDescription:r,ruleName:n});throw this.SAVE_ERROR(new rA(s,this.LA(1),this.LA(0)))}raiseNoAltException(e,t){let r=this.getCurrRuleFullName(),n=rn(e,this.getGAstProductions()[r],this.maxLookahead),i=[];for(let e=1;e<=this.maxLookahead;e++)i.push(this.LA(e));let a=this.LA(0),s=this.errorMessageProvider.buildNoViableAltMessage({expectedPathsPerAlt:n,actual:i,previous:a,customUserDescription:t,ruleName:this.getCurrRuleFullName()});throw this.SAVE_ERROR(new rR(s,this.LA(1),a))}},class{initContentAssist(){}computeContentAssist(e,t){let r=this.gastProductionsCache[e];if((0,eA.Z)(r))throw Error(`Rule ->${e}<- does not exist in this grammar.`);return t6([r],t,this.tokenMatcher,this.maxLookahead)}getNextPossibleTokenTypes(e){let t=eK(e.ruleStack);return new tq(this.getGAstProductions()[t],e).startWalking()}},class{initGastRecorder(e){this.recordingProdStack=[],this.RECORDING_PHASE=!1}enableRecording(){this.RECORDING_PHASE=!0,this.TRACE_INIT("Enable Recording",()=>{for(let e=0;e<10;e++){let t=e>0?e:"";this[`CONSUME${t}`]=function(t,r){return this.consumeInternalRecord(t,e,r)},this[`SUBRULE${t}`]=function(t,r){return this.subruleInternalRecord(t,e,r)},this[`OPTION${t}`]=function(t){return this.optionInternalRecord(t,e)},this[`OR${t}`]=function(t){return this.orInternalRecord(t,e)},this[`MANY${t}`]=function(t){this.manyInternalRecord(e,t)},this[`MANY_SEP${t}`]=function(t){this.manySepFirstInternalRecord(e,t)},this[`AT_LEAST_ONE${t}`]=function(t){this.atLeastOneInternalRecord(e,t)},this[`AT_LEAST_ONE_SEP${t}`]=function(t){this.atLeastOneSepFirstInternalRecord(e,t)}}this.consume=function(e,t,r){return this.consumeInternalRecord(t,e,r)},this.subrule=function(e,t,r){return this.subruleInternalRecord(t,e,r)},this.option=function(e,t){return this.optionInternalRecord(t,e)},this.or=function(e,t){return this.orInternalRecord(t,e)},this.many=function(e,t){this.manyInternalRecord(e,t)},this.atLeastOne=function(e,t){this.atLeastOneInternalRecord(e,t)},this.ACTION=this.ACTION_RECORD,this.BACKTRACK=this.BACKTRACK_RECORD,this.LA=this.LA_RECORD})}disableRecording(){this.RECORDING_PHASE=!1,this.TRACE_INIT("Deleting Recording methods",()=>{for(let e=0;e<10;e++){let t=e>0?e:"";delete this[`CONSUME${t}`],delete this[`SUBRULE${t}`],delete this[`OPTION${t}`],delete this[`OR${t}`],delete this[`MANY${t}`],delete this[`MANY_SEP${t}`],delete this[`AT_LEAST_ONE${t}`],delete this[`AT_LEAST_ONE_SEP${t}`]}delete this.consume,delete this.subrule,delete this.option,delete this.or,delete this.many,delete this.atLeastOne,delete this.ACTION,delete this.BACKTRACK,delete this.LA})}ACTION_RECORD(e){}BACKTRACK_RECORD(e,t){return()=>!0}LA_RECORD(e){return rB}topLevelRuleRecord(e,t){try{let r=new K({definition:[],name:e});return r.name=e,this.recordingProdStack.push(r),t.call(this),this.recordingProdStack.pop(),r}catch(e){if(!0!==e.KNOWN_RECORDER_ERROR)try{e.message=e.message+'\n	 This error was thrown during the "grammar recording phase" For more info see:\n	https://chevrotain.io/docs/guide/internals.html#grammar-recording'}catch(e){}throw e}}optionInternalRecord(e,t){return rZ.call(this,W,e,t)}atLeastOneInternalRecord(e,t){rZ.call(this,H,t,e)}atLeastOneSepFirstInternalRecord(e,t){rZ.call(this,z,t,e,!0)}manyInternalRecord(e,t){rZ.call(this,Y,t,e)}manySepFirstInternalRecord(e,t){rZ.call(this,q,t,e,!0)}orInternalRecord(e,t){return rF.call(this,e,t)}subruleInternalRecord(e,t,r){if(rG(t),!e||!1===(0,g.Z)(e,"ruleName")){let r=Error(`<SUBRULE${rU(t)}> argument is invalid expecting a Parser method reference but got: <${JSON.stringify(e)}>
 inside top level rule: <${this.recordingProdStack[0].name}>`);throw r.KNOWN_RECORDER_ERROR=!0,r}let n=(0,td.Z)(this.recordingProdStack),i=new V({idx:t,nonTerminalName:e.ruleName,label:null==r?void 0:r.LABEL,referencedRule:void 0});return n.definition.push(i),this.outputCst?rD:r_}consumeInternalRecord(e,t,r){if(rG(t),!tT(e)){let r=Error(`<CONSUME${rU(t)}> argument is invalid expecting a TokenType reference but got: <${JSON.stringify(e)}>
 inside top level rule: <${this.recordingProdStack[0].name}>`);throw r.KNOWN_RECORDER_ERROR=!0,r}let n=(0,td.Z)(this.recordingProdStack),i=new Q({idx:t,terminalType:e,label:null==r?void 0:r.LABEL});return n.definition.push(i),rM}},class{initPerformanceTracer(e){if((0,g.Z)(e,"traceInitPerf")){let t=e.traceInitPerf,r="number"==typeof t;this.traceInitMaxIdent=r?t:1/0,this.traceInitPerf=r?t>0:t}else this.traceInitMaxIdent=0,this.traceInitPerf=rV.traceInitPerf;this.traceInitIndent=-1}TRACE_INIT(e,t){if(!0!==this.traceInitPerf)return t();{this.traceInitIndent++;let r=Array(this.traceInitIndent+1).join("	");this.traceInitIndent<this.traceInitMaxIdent&&console.log(`${r}--> <${e}>`);let{time:n,value:i}=th(t),a=n>10?console.warn:console.log;return this.traceInitIndent<this.traceInitMaxIdent&&a(`${r}<-- <${e}> time: ${n}ms`),this.traceInitIndent--,i}}}].forEach(e=>{let t=e.prototype;Object.getOwnPropertyNames(t).forEach(r=>{if("constructor"===r)return;let n=Object.getOwnPropertyDescriptor(t,r);n&&(n.get||n.set)?Object.defineProperty(rW.prototype,r,n):rW.prototype[r]=e.prototype[r]})});class rH extends rW{constructor(e,t=rV){let r=(0,y.Z)(t);r.outputCst=!1,super(e,r)}}},92910:function(e,t,r){r.d(t,{Q:()=>nK,T:()=>nj});var n,i,a,s,o,l,u,c,d,h,f,p,m,g,y,T,v,R,E,A,k,$,x,I,S,C,N,w,L,b,O,_,P,M,D,Z,F,U,G,B,V,K,j,W,H,z,Y,q,X,Q,J,ee,et,er,en,ei,ea,es,eo,el,eu,ec,ed,eh,ef,ep,em,eg,ey,eT,ev,eR,eE,eA,ek,e$,ex,eI,eS,eC,eN,ew,eL,eb,eO,e_,eP,eM,eD,eZ,eF,eU,eG,eB,eV,eK,ej,eW,eH,ez,eY,eq,eX,eQ,eJ,e0,e1,e2,e6,e9,e3,e7,e4,e5,e8,te,tt,tr,tn,ti,ta,ts,to,tl,tu,tc,td,th,tf,tp,tm,tg,ty,tT,tv,tR,tE,tA,tk,t$,tx,tI,tS,tC,tN,tw=r(10769),tL=r(84608),tb=r(42268),tO=r(46117),t_=r(84376),tP=r(47191),tM=r(67998);function tD(e,t,r){return`${e.name}_${t}_${r}`}class tZ{constructor(e){this.target=e}isEpsilon(){return!1}}class tF extends tZ{constructor(e,t){super(e),this.tokenType=t}}class tU extends tZ{constructor(e){super(e)}isEpsilon(){return!0}}class tG extends tZ{constructor(e,t,r){super(e),this.rule=t,this.followState=r}isEpsilon(){return!0}}function tB(e,t,r){let n=(0,tM.Z)((0,tP.Z)(r.definition,r=>(function e(t,r,n){if(n instanceof t_.oI)return tH(t,r,n.terminalType,n);if(n instanceof t_.Sj){var i=t,a=r,s=n;let e=s.referencedRule,o=i.ruleToStartState.get(e),l=tY(i,a,s,{type:1}),u=tY(i,a,s,{type:1});return tq(l,new tG(o,e,u)),{left:l,right:u}}if(n instanceof t_.ue){var o=t,l=r,u=n;let i=tY(o,l,u,{type:1});tj(o,i);let a=(0,tP.Z)(u.definition,t=>e(o,l,t));return tW(o,l,i,u,...a)}if(n instanceof t_.Wx)return function(e,t,r){let n=tY(e,t,r,{type:1});tj(e,n);let i=tW(e,t,n,r,tB(e,t,r));var a=e,s=t,o=r,l=i;let u=l.left;return tz(u,l.right),a.decisionMap[tD(s,"Option",o.idx)]=u,l}(t,r,n);if(n instanceof t_.hI){var c=t,d=r,h=n;let e=tY(c,d,h,{type:5});tj(c,e);let i=tW(c,d,e,h,tB(c,d,h));return tK(c,d,h,i)}else if(n instanceof t_.pT){var f=t,p=r,m=n;let e=tY(f,p,m,{type:5});tj(f,e);let i=tW(f,p,e,m,tB(f,p,m)),a=tH(f,p,m.separator,m);return tK(f,p,m,i,a)}else if(n instanceof t_.ej){var g=t,y=r,T=n;let e=tY(g,y,T,{type:4});tj(g,e);let i=tW(g,y,e,T,tB(g,y,T));return tV(g,y,T,i)}else{if(!(n instanceof t_.fK))return tB(t,r,n);var v=t,R=r,E=n;let e=tY(v,R,E,{type:4});tj(v,e);let i=tW(v,R,e,E,tB(v,R,E)),a=tH(v,R,E.separator,E);return tV(v,R,E,i,a)}})(e,t,r)),e=>void 0!==e);return 1===n.length?n[0]:0===n.length?void 0:function(e,t){let r=t.length;for(let a=0;a<r-1;a++){var n,i;let r,s=t[a];1===s.left.transitions.length&&(r=s.left.transitions[0]);let o=r instanceof tG,l=r,u=t[a+1].left;1===s.left.type&&1===s.right.type&&void 0!==r&&(o&&l.followState===s.right||r.target===s.right)?(o?l.followState=u:r.target=u,n=e,i=s.right,n.states.splice(n.states.indexOf(i),1)):tz(s.right,u)}let a=t[0],s=t[r-1];return{left:a.left,right:s.right}}(e,n)}function tV(e,t,r,n,i){let a=n.left,s=n.right,o=tY(e,t,r,{type:11});tj(e,o);let l=tY(e,t,r,{type:12});return a.loopback=o,l.loopback=o,e.decisionMap[tD(t,i?"RepetitionMandatoryWithSeparator":"RepetitionMandatory",r.idx)]=o,tz(s,o),void 0===i?(tz(o,a),tz(o,l)):(tz(o,l),tz(o,i.left),tz(i.right,a)),{left:a,right:l}}function tK(e,t,r,n,i){let a=n.left,s=n.right,o=tY(e,t,r,{type:10});tj(e,o);let l=tY(e,t,r,{type:12}),u=tY(e,t,r,{type:9});return o.loopback=u,l.loopback=u,tz(o,a),tz(o,l),tz(s,u),void 0!==i?(tz(u,l),tz(u,i.left),tz(i.right,a)):tz(u,o),e.decisionMap[tD(t,i?"RepetitionWithSeparator":"Repetition",r.idx)]=o,{left:o,right:l}}function tj(e,t){return e.decisionStates.push(t),t.decision=e.decisionStates.length-1,t.decision}function tW(e,t,r,n,...i){let a=tY(e,t,n,{type:8,start:r});for(let e of(r.end=a,i))void 0!==e?(tz(r,e.left),tz(e.right,a)):tz(r,a);return e.decisionMap[tD(t,function(e){if(e instanceof t_.ue)return"Alternation";if(e instanceof t_.Wx)return"Option";if(e instanceof t_.hI)return"Repetition";if(e instanceof t_.pT)return"RepetitionWithSeparator";if(e instanceof t_.ej)return"RepetitionMandatory";else if(e instanceof t_.fK)return"RepetitionMandatoryWithSeparator";else throw Error("Invalid production type encountered")}(n),n.idx)]=r,{left:r,right:a}}function tH(e,t,r,n){let i=tY(e,t,n,{type:1}),a=tY(e,t,n,{type:1});return tq(i,new tF(a,r)),{left:i,right:a}}function tz(e,t){tq(e,new tU(t))}function tY(e,t,r,n){let i=Object.assign({atn:e,production:r,epsilonOnlyTransitions:!1,rule:t,transitions:[],nextTokenWithinRule:[],stateNumber:e.states.length},n);return e.states.push(i),i}function tq(e,t){0===e.transitions.length&&(e.epsilonOnlyTransitions=t.isEpsilon()),e.transitions.push(t)}let tX={};class tQ{constructor(){this.map={},this.configs=[]}get size(){return this.configs.length}finalize(){this.map={}}add(e){let t=tJ(e);t in this.map||(this.map[t]=this.configs.length,this.configs.push(e))}get elements(){return this.configs}get alts(){return(0,tP.Z)(this.configs,e=>e.alt)}get key(){let e="";for(let t in this.map)e+=t+":";return e}}function tJ(e,t=!0){return`${t?`a${e.alt}`:""}s${e.state.stateNumber}:${e.stack.map(e=>e.stateNumber.toString()).join("_")}`}var t0=r(82771),t1=r(24945),t2=r(36616),t6=r(18020),t9=r(74590),t3=r(84458),t7=r(46471),t4=r(65457);class t5{constructor(){this.predicates=[]}is(e){return e>=this.predicates.length||this.predicates[e]}set(e,t){this.predicates[e]=t}toString(){let e="",t=this.predicates.length;for(let r=0;r<t;r++)e+=!0===this.predicates[r]?"1":"0";return e}}let t8=new t5;class re extends t_.dV{constructor(e){var t;super(),this.logging=null!=(t=null==e?void 0:e.logging)?t:e=>console.log(e)}initialize(e){this.atn=function(e){let t={decisionMap:{},decisionStates:[],ruleToStartState:new Map,ruleToStopState:new Map,states:[]};!function(e,t){let r=t.length;for(let n=0;n<r;n++){let r=t[n],i=tY(e,r,void 0,{type:2}),a=tY(e,r,void 0,{type:7});i.stop=a,e.ruleToStartState.set(r,i),e.ruleToStopState.set(r,a)}}(t,e);let r=e.length;for(let n=0;n<r;n++){let r=e[n],i=tB(t,r,r);void 0!==i&&function(e,t,r){tz(e.ruleToStartState.get(t),r.left);let n=e.ruleToStopState.get(t);tz(r.right,n)}(t,r,i)}return t}(e.rules),this.dfas=function(e){let t=e.decisionStates.length,r=Array(t);for(let n=0;n<t;n++)r[n]=function(e,t){let r={};return n=>{let i=n.toString(),a=r[i];return void 0!==a||(a={atnStartState:e,decision:t,states:{}},r[i]=a),a}}(e.decisionStates[n],n);return r}(this.atn)}validateAmbiguousAlternationAlternatives(){return[]}validateEmptyOrAlternatives(){return[]}buildLookaheadForAlternation(e){let{prodOccurrence:t,rule:r,hasPredicates:n,dynamicTokensEnabled:i}=e,a=this.dfas,s=this.logging,o=tD(r,"Alternation",t),l=this.atn.decisionMap[o].decision,u=(0,tP.Z)((0,t_.oC)({maxLookahead:1,occurrence:t,prodType:"Alternation",rule:r}),e=>(0,tP.Z)(e,e=>e[0]));if(rt(u,!1)&&!i){let e=(0,t4.Z)(u,(e,t,r)=>((0,t3.Z)(t,t=>{t&&(e[t.tokenTypeIdx]=r,(0,t3.Z)(t.categoryMatches,t=>{e[t]=r}))}),e),{});return n?function(t){var r;let n=e[this.LA(1).tokenTypeIdx];if(void 0!==t&&void 0!==n){let e=null==(r=t[n])?void 0:r.GATE;if(void 0!==e&&!1===e.call(this))return}return n}:function(){return e[this.LA(1).tokenTypeIdx]}}return n?function(e){let t=new t5,r=void 0===e?0:e.length;for(let n=0;n<r;n++){let r=null==e?void 0:e[n].GATE;t.set(n,void 0===r||r.call(this))}let n=rr.call(this,a,l,t,s);return"number"==typeof n?n:void 0}:function(){let e=rr.call(this,a,l,t8,s);return"number"==typeof e?e:void 0}}buildLookaheadForOptional(e){let{prodOccurrence:t,rule:r,prodType:n,dynamicTokensEnabled:i}=e,a=this.dfas,s=this.logging,o=tD(r,n,t),l=this.atn.decisionMap[o].decision,u=(0,tP.Z)((0,t_.oC)({maxLookahead:1,occurrence:t,prodType:n,rule:r}),e=>(0,tP.Z)(e,e=>e[0]));if(rt(u)&&u[0][0]&&!i){let e=u[0],t=(0,t9.Z)(e);if(1===t.length&&(0,t7.Z)(t[0].categoryMatches)){let e=t[0].tokenTypeIdx;return function(){return this.LA(1).tokenTypeIdx===e}}{let e=(0,t4.Z)(t,(e,t)=>(void 0!==t&&(e[t.tokenTypeIdx]=!0,(0,t3.Z)(t.categoryMatches,t=>{e[t]=!0})),e),{});return function(){return!0===e[this.LA(1).tokenTypeIdx]}}}return function(){let e=rr.call(this,a,l,t8,s);return"object"!=typeof e&&0===e}}}function rt(e,t=!0){let r=new Set;for(let n of e){let e=new Set;for(let i of n){if(void 0===i)if(!t)return!1;else break;for(let t of[i.tokenTypeIdx].concat(i.categoryMatches))if(r.has(t)){if(!e.has(t))return!1}else r.add(t),e.add(t)}}return!0}function rr(e,t,r,n){let i=e[t](r),a=i.start;if(void 0===a){let e=function(e){let t=new tQ,r=e.transitions.length;for(let n=0;n<r;n++)ru({state:e.transitions[n].target,alt:n,stack:[]},t);return t}(i.atnStartState);a=rl(i,rs(e)),i.start=a}return rn.apply(this,[i,a,r,n])}function rn(e,t,r,n){let i=t,a=1,s=[],o=this.LA(a++);for(;;){var l,u;let t=(l=i,u=o,l.edges[u.tokenTypeIdx]);if(void 0===t&&(t=ri.apply(this,[e,i,o,a,r,n])),t===tX)return function(e,t,r){var n,i;return{actualToken:r,possibleTokenTypes:(n=(0,t1.Z)(t.configs.elements,e=>e.state.transitions).filter(e=>e instanceof tF).map(e=>e.tokenType),i=e=>e.tokenTypeIdx,n&&n.length?(0,t6.Z)(n,(0,t2.Z)(i,2)):[]),tokenPath:e}}(s,i,o);if(!0===t.isAcceptState)return t.prediction;i=t,s.push(o),o=this.LA(a++)}}function ri(e,t,r,n,i,a){let s=function(e,t,r){let n,i=new tQ,a=[];for(let n of e.elements){if(!1===r.is(n.alt))continue;if(7===n.state.type){a.push(n);continue}let e=n.state.transitions.length;for(let r=0;r<e;r++){let e=function(e,t){if(e instanceof tF&&(0,t_.ol)(t,e.tokenType))return e.target}(n.state.transitions[r],t);void 0!==e&&i.add({state:e,alt:n.alt,stack:n.stack})}}if(0===a.length&&1===i.size&&(n=i),void 0===n)for(let e of(n=new tQ,i.elements))ru(e,n);if(a.length>0&&!function(e){for(let t of e.elements)if(7===t.state.type)return!0;return!1}(n))for(let e of a)n.add(e);return n}(t.configs,r,i);if(0===s.size)return ro(e,t,r,tX),tX;let o=rs(s),l=function(e,t){let r;for(let n of e.elements)if(!0===t.is(n.alt)){if(void 0===r)r=n.alt;else if(r!==n.alt)return}return r}(s,i);if(void 0!==l)o.isAcceptState=!0,o.prediction=l,o.configs.uniqueAlt=l;else if(function(e){if(function(e){for(let t of e.elements)if(7!==t.state.type)return!1;return!0}(e))return!0;let t=function(e){let t=new Map;for(let r of e){let e=tJ(r,!1),n=t.get(e);void 0===n&&(n={},t.set(e,n)),n[r.alt]=!0}return t}(e.elements);return function(e){for(let t of Array.from(e.values()))if(Object.keys(t).length>1)return!0;return!1}(t)&&!function(e){for(let t of Array.from(e.values()))if(1===Object.keys(t).length)return!0;return!1}(t)}(s)){let t=(0,t0.Z)(s.alts);o.isAcceptState=!0,o.prediction=t,o.configs.uniqueAlt=t,ra.apply(this,[e,n,s.alts,a])}return ro(e,t,r,o)}function ra(e,t,r,n){let i=[];for(let e=1;e<=t;e++)i.push(this.LA(e).tokenType);let a=e.atnStartState;n(function(e){let t=(0,tP.Z)(e.prefixPath,e=>(0,t_.l$)(e)).join(", "),r=0===e.production.idx?"":e.production.idx,n=`Ambiguous Alternatives Detected: <${e.ambiguityIndices.join(", ")}> in <${function(e){if(e instanceof t_.Sj)return"SUBRULE";if(e instanceof t_.Wx)return"OPTION";if(e instanceof t_.ue)return"OR";if(e instanceof t_.ej)return"AT_LEAST_ONE";if(e instanceof t_.fK)return"AT_LEAST_ONE_SEP";else if(e instanceof t_.pT)return"MANY_SEP";else if(e instanceof t_.hI)return"MANY";else if(e instanceof t_.oI)return"CONSUME";else throw Error("non exhaustive match")}(e.production)}${r}> inside <${e.topLevelRule.name}> Rule,
<${t}> may appears as a prefix path in all these alternatives.
`;return n+`See: https://chevrotain.io/docs/guide/resolving_grammar_errors.html#AMBIGUOUS_ALTERNATIVES
`+"For Further details."}({topLevelRule:a.rule,ambiguityIndices:r,production:a.production,prefixPath:i}))}function rs(e){return{configs:e,edges:{},isAcceptState:!1,prediction:-1}}function ro(e,t,r,n){return n=rl(e,n),t.edges[r.tokenTypeIdx]=n,n}function rl(e,t){if(t===tX)return t;let r=t.configs.key,n=e.states[r];return void 0!==n?n:(t.configs.finalize(),e.states[r]=t,t)}function ru(e,t){let r=e.state;if(7===r.type){if(e.stack.length>0){let r=[...e.stack];ru({state:r.pop(),alt:e.alt,stack:r},t)}else t.add(e);return}r.epsilonOnlyTransitions||t.add(e);let n=r.transitions.length;for(let i=0;i<n;i++){let n=function(e,t){if(t instanceof tU)return{state:t.target,alt:e.alt,stack:e.stack};if(t instanceof tG){let r=[...e.stack,t.followState];return{state:t.target,alt:e.alt,stack:r}}}(e,r.transitions[i]);void 0!==n&&ru(n,t)}}var rc=r(58892);(el||(el={})).is=function(e){return"string"==typeof e},(eu||(eu={})).is=function(e){return"string"==typeof e},(n=ec||(ec={})).MIN_VALUE=-0x80000000,n.MAX_VALUE=0x7fffffff,n.is=function(e){return"number"==typeof e&&n.MIN_VALUE<=e&&e<=n.MAX_VALUE},(i=ed||(ed={})).MIN_VALUE=0,i.MAX_VALUE=0x7fffffff,i.is=function(e){return"number"==typeof e&&i.MIN_VALUE<=e&&e<=i.MAX_VALUE},(a=eh||(eh={})).create=function(e,t){return e===Number.MAX_VALUE&&(e=ed.MAX_VALUE),t===Number.MAX_VALUE&&(t=ed.MAX_VALUE),{line:e,character:t}},a.is=function(e){return tk.objectLiteral(e)&&tk.uinteger(e.line)&&tk.uinteger(e.character)},(s=ef||(ef={})).create=function(e,t,r,n){if(tk.uinteger(e)&&tk.uinteger(t)&&tk.uinteger(r)&&tk.uinteger(n))return{start:eh.create(e,t),end:eh.create(r,n)};if(eh.is(e)&&eh.is(t))return{start:e,end:t};throw Error(`Range#create called with invalid arguments[${e}, ${t}, ${r}, ${n}]`)},s.is=function(e){return tk.objectLiteral(e)&&eh.is(e.start)&&eh.is(e.end)},(o=ep||(ep={})).create=function(e,t){return{uri:e,range:t}},o.is=function(e){return tk.objectLiteral(e)&&ef.is(e.range)&&(tk.string(e.uri)||tk.undefined(e.uri))},(l=em||(em={})).create=function(e,t,r,n){return{targetUri:e,targetRange:t,targetSelectionRange:r,originSelectionRange:n}},l.is=function(e){return tk.objectLiteral(e)&&ef.is(e.targetRange)&&tk.string(e.targetUri)&&ef.is(e.targetSelectionRange)&&(ef.is(e.originSelectionRange)||tk.undefined(e.originSelectionRange))},(u=eg||(eg={})).create=function(e,t,r,n){return{red:e,green:t,blue:r,alpha:n}},u.is=function(e){return tk.objectLiteral(e)&&tk.numberRange(e.red,0,1)&&tk.numberRange(e.green,0,1)&&tk.numberRange(e.blue,0,1)&&tk.numberRange(e.alpha,0,1)},(c=ey||(ey={})).create=function(e,t){return{range:e,color:t}},c.is=function(e){return tk.objectLiteral(e)&&ef.is(e.range)&&eg.is(e.color)},(d=eT||(eT={})).create=function(e,t,r){return{label:e,textEdit:t,additionalTextEdits:r}},d.is=function(e){return tk.objectLiteral(e)&&tk.string(e.label)&&(tk.undefined(e.textEdit)||eS.is(e))&&(tk.undefined(e.additionalTextEdits)||tk.typedArray(e.additionalTextEdits,eS.is))},(h=ev||(ev={})).Comment="comment",h.Imports="imports",h.Region="region",(f=eR||(eR={})).create=function(e,t,r,n,i,a){let s={startLine:e,endLine:t};return tk.defined(r)&&(s.startCharacter=r),tk.defined(n)&&(s.endCharacter=n),tk.defined(i)&&(s.kind=i),tk.defined(a)&&(s.collapsedText=a),s},f.is=function(e){return tk.objectLiteral(e)&&tk.uinteger(e.startLine)&&tk.uinteger(e.startLine)&&(tk.undefined(e.startCharacter)||tk.uinteger(e.startCharacter))&&(tk.undefined(e.endCharacter)||tk.uinteger(e.endCharacter))&&(tk.undefined(e.kind)||tk.string(e.kind))},(p=eE||(eE={})).create=function(e,t){return{location:e,message:t}},p.is=function(e){return tk.defined(e)&&ep.is(e.location)&&tk.string(e.message)},(m=eA||(eA={})).Error=1,m.Warning=2,m.Information=3,m.Hint=4,(g=ek||(ek={})).Unnecessary=1,g.Deprecated=2,(e$||(e$={})).is=function(e){return tk.objectLiteral(e)&&tk.string(e.href)},(y=ex||(ex={})).create=function(e,t,r,n,i,a){let s={range:e,message:t};return tk.defined(r)&&(s.severity=r),tk.defined(n)&&(s.code=n),tk.defined(i)&&(s.source=i),tk.defined(a)&&(s.relatedInformation=a),s},y.is=function(e){var t;return tk.defined(e)&&ef.is(e.range)&&tk.string(e.message)&&(tk.number(e.severity)||tk.undefined(e.severity))&&(tk.integer(e.code)||tk.string(e.code)||tk.undefined(e.code))&&(tk.undefined(e.codeDescription)||tk.string(null==(t=e.codeDescription)?void 0:t.href))&&(tk.string(e.source)||tk.undefined(e.source))&&(tk.undefined(e.relatedInformation)||tk.typedArray(e.relatedInformation,eE.is))},(T=eI||(eI={})).create=function(e,t,...r){let n={title:e,command:t};return tk.defined(r)&&r.length>0&&(n.arguments=r),n},T.is=function(e){return tk.defined(e)&&tk.string(e.title)&&tk.string(e.command)},(v=eS||(eS={})).replace=function(e,t){return{range:e,newText:t}},v.insert=function(e,t){return{range:{start:e,end:e},newText:t}},v.del=function(e){return{range:e,newText:""}},v.is=function(e){return tk.objectLiteral(e)&&tk.string(e.newText)&&ef.is(e.range)},(R=eC||(eC={})).create=function(e,t,r){let n={label:e};return void 0!==t&&(n.needsConfirmation=t),void 0!==r&&(n.description=r),n},R.is=function(e){return tk.objectLiteral(e)&&tk.string(e.label)&&(tk.boolean(e.needsConfirmation)||void 0===e.needsConfirmation)&&(tk.string(e.description)||void 0===e.description)},(eN||(eN={})).is=function(e){return tk.string(e)},(E=ew||(ew={})).replace=function(e,t,r){return{range:e,newText:t,annotationId:r}},E.insert=function(e,t,r){return{range:{start:e,end:e},newText:t,annotationId:r}},E.del=function(e,t){return{range:e,newText:"",annotationId:t}},E.is=function(e){return eS.is(e)&&(eC.is(e.annotationId)||eN.is(e.annotationId))},(A=eL||(eL={})).create=function(e,t){return{textDocument:e,edits:t}},A.is=function(e){return tk.defined(e)&&eZ.is(e.textDocument)&&Array.isArray(e.edits)},(k=eb||(eb={})).create=function(e,t,r){let n={kind:"create",uri:e};return void 0!==t&&(void 0!==t.overwrite||void 0!==t.ignoreIfExists)&&(n.options=t),void 0!==r&&(n.annotationId=r),n},k.is=function(e){return e&&"create"===e.kind&&tk.string(e.uri)&&(void 0===e.options||(void 0===e.options.overwrite||tk.boolean(e.options.overwrite))&&(void 0===e.options.ignoreIfExists||tk.boolean(e.options.ignoreIfExists)))&&(void 0===e.annotationId||eN.is(e.annotationId))},($=eO||(eO={})).create=function(e,t,r,n){let i={kind:"rename",oldUri:e,newUri:t};return void 0!==r&&(void 0!==r.overwrite||void 0!==r.ignoreIfExists)&&(i.options=r),void 0!==n&&(i.annotationId=n),i},$.is=function(e){return e&&"rename"===e.kind&&tk.string(e.oldUri)&&tk.string(e.newUri)&&(void 0===e.options||(void 0===e.options.overwrite||tk.boolean(e.options.overwrite))&&(void 0===e.options.ignoreIfExists||tk.boolean(e.options.ignoreIfExists)))&&(void 0===e.annotationId||eN.is(e.annotationId))},(x=e_||(e_={})).create=function(e,t,r){let n={kind:"delete",uri:e};return void 0!==t&&(void 0!==t.recursive||void 0!==t.ignoreIfNotExists)&&(n.options=t),void 0!==r&&(n.annotationId=r),n},x.is=function(e){return e&&"delete"===e.kind&&tk.string(e.uri)&&(void 0===e.options||(void 0===e.options.recursive||tk.boolean(e.options.recursive))&&(void 0===e.options.ignoreIfNotExists||tk.boolean(e.options.ignoreIfNotExists)))&&(void 0===e.annotationId||eN.is(e.annotationId))},(eP||(eP={})).is=function(e){return e&&(void 0!==e.changes||void 0!==e.documentChanges)&&(void 0===e.documentChanges||e.documentChanges.every(e=>tk.string(e.kind)?eb.is(e)||eO.is(e)||e_.is(e):eL.is(e)))},(I=eM||(eM={})).create=function(e){return{uri:e}},I.is=function(e){return tk.defined(e)&&tk.string(e.uri)},(S=eD||(eD={})).create=function(e,t){return{uri:e,version:t}},S.is=function(e){return tk.defined(e)&&tk.string(e.uri)&&tk.integer(e.version)},(C=eZ||(eZ={})).create=function(e,t){return{uri:e,version:t}},C.is=function(e){return tk.defined(e)&&tk.string(e.uri)&&(null===e.version||tk.integer(e.version))},(N=eF||(eF={})).create=function(e,t,r,n){return{uri:e,languageId:t,version:r,text:n}},N.is=function(e){return tk.defined(e)&&tk.string(e.uri)&&tk.string(e.languageId)&&tk.integer(e.version)&&tk.string(e.text)},(w=eU||(eU={})).PlainText="plaintext",w.Markdown="markdown",w.is=function(e){return e===w.PlainText||e===w.Markdown},(eG||(eG={})).is=function(e){return tk.objectLiteral(e)&&eU.is(e.kind)&&tk.string(e.value)},(L=eB||(eB={})).Text=1,L.Method=2,L.Function=3,L.Constructor=4,L.Field=5,L.Variable=6,L.Class=7,L.Interface=8,L.Module=9,L.Property=10,L.Unit=11,L.Value=12,L.Enum=13,L.Keyword=14,L.Snippet=15,L.Color=16,L.File=17,L.Reference=18,L.Folder=19,L.EnumMember=20,L.Constant=21,L.Struct=22,L.Event=23,L.Operator=24,L.TypeParameter=25,(b=eV||(eV={})).PlainText=1,b.Snippet=2,(eK||(eK={})).Deprecated=1,(O=ej||(ej={})).create=function(e,t,r){return{newText:e,insert:t,replace:r}},O.is=function(e){return e&&tk.string(e.newText)&&ef.is(e.insert)&&ef.is(e.replace)},(_=eW||(eW={})).asIs=1,_.adjustIndentation=2,(eH||(eH={})).is=function(e){return e&&(tk.string(e.detail)||void 0===e.detail)&&(tk.string(e.description)||void 0===e.description)},(ez||(ez={})).create=function(e){return{label:e}},(eY||(eY={})).create=function(e,t){return{items:e||[],isIncomplete:!!t}},(P=eq||(eq={})).fromPlainText=function(e){return e.replace(/[\\`*_{}[\]()#+\-.!]/g,"\\$&")},P.is=function(e){return tk.string(e)||tk.objectLiteral(e)&&tk.string(e.language)&&tk.string(e.value)},(eX||(eX={})).is=function(e){return!!e&&tk.objectLiteral(e)&&(eG.is(e.contents)||eq.is(e.contents)||tk.typedArray(e.contents,eq.is))&&(void 0===e.range||ef.is(e.range))},(eQ||(eQ={})).create=function(e,t){return t?{label:e,documentation:t}:{label:e}},(eJ||(eJ={})).create=function(e,t,...r){let n={label:e};return tk.defined(t)&&(n.documentation=t),tk.defined(r)?n.parameters=r:n.parameters=[],n},(M=e0||(e0={})).Text=1,M.Read=2,M.Write=3,(e1||(e1={})).create=function(e,t){let r={range:e};return tk.number(t)&&(r.kind=t),r},(D=e2||(e2={})).File=1,D.Module=2,D.Namespace=3,D.Package=4,D.Class=5,D.Method=6,D.Property=7,D.Field=8,D.Constructor=9,D.Enum=10,D.Interface=11,D.Function=12,D.Variable=13,D.Constant=14,D.String=15,D.Number=16,D.Boolean=17,D.Array=18,D.Object=19,D.Key=20,D.Null=21,D.EnumMember=22,D.Struct=23,D.Event=24,D.Operator=25,D.TypeParameter=26,(e6||(e6={})).Deprecated=1,(e9||(e9={})).create=function(e,t,r,n,i){let a={name:e,kind:t,location:{uri:n,range:r}};return i&&(a.containerName=i),a},(e3||(e3={})).create=function(e,t,r,n){return void 0!==n?{name:e,kind:t,location:{uri:r,range:n}}:{name:e,kind:t,location:{uri:r}}},(Z=e7||(e7={})).create=function(e,t,r,n,i,a){let s={name:e,detail:t,kind:r,range:n,selectionRange:i};return void 0!==a&&(s.children=a),s},Z.is=function(e){return e&&tk.string(e.name)&&tk.number(e.kind)&&ef.is(e.range)&&ef.is(e.selectionRange)&&(void 0===e.detail||tk.string(e.detail))&&(void 0===e.deprecated||tk.boolean(e.deprecated))&&(void 0===e.children||Array.isArray(e.children))&&(void 0===e.tags||Array.isArray(e.tags))},(F=e4||(e4={})).Empty="",F.QuickFix="quickfix",F.Refactor="refactor",F.RefactorExtract="refactor.extract",F.RefactorInline="refactor.inline",F.RefactorRewrite="refactor.rewrite",F.Source="source",F.SourceOrganizeImports="source.organizeImports",F.SourceFixAll="source.fixAll",(U=e5||(e5={})).Invoked=1,U.Automatic=2,(G=e8||(e8={})).create=function(e,t,r){let n={diagnostics:e};return null!=t&&(n.only=t),null!=r&&(n.triggerKind=r),n},G.is=function(e){return tk.defined(e)&&tk.typedArray(e.diagnostics,ex.is)&&(void 0===e.only||tk.typedArray(e.only,tk.string))&&(void 0===e.triggerKind||e.triggerKind===e5.Invoked||e.triggerKind===e5.Automatic)},(B=te||(te={})).create=function(e,t,r){let n={title:e},i=!0;return"string"==typeof t?(i=!1,n.kind=t):eI.is(t)?n.command=t:n.edit=t,i&&void 0!==r&&(n.kind=r),n},B.is=function(e){return e&&tk.string(e.title)&&(void 0===e.diagnostics||tk.typedArray(e.diagnostics,ex.is))&&(void 0===e.kind||tk.string(e.kind))&&(void 0!==e.edit||void 0!==e.command)&&(void 0===e.command||eI.is(e.command))&&(void 0===e.isPreferred||tk.boolean(e.isPreferred))&&(void 0===e.edit||eP.is(e.edit))},(V=tt||(tt={})).create=function(e,t){let r={range:e};return tk.defined(t)&&(r.data=t),r},V.is=function(e){return tk.defined(e)&&ef.is(e.range)&&(tk.undefined(e.command)||eI.is(e.command))},(K=tr||(tr={})).create=function(e,t){return{tabSize:e,insertSpaces:t}},K.is=function(e){return tk.defined(e)&&tk.uinteger(e.tabSize)&&tk.boolean(e.insertSpaces)},(j=tn||(tn={})).create=function(e,t,r){return{range:e,target:t,data:r}},j.is=function(e){return tk.defined(e)&&ef.is(e.range)&&(tk.undefined(e.target)||tk.string(e.target))},(W=ti||(ti={})).create=function(e,t){return{range:e,parent:t}},W.is=function(e){return tk.objectLiteral(e)&&ef.is(e.range)&&(void 0===e.parent||W.is(e.parent))},(H=ta||(ta={})).namespace="namespace",H.type="type",H.class="class",H.enum="enum",H.interface="interface",H.struct="struct",H.typeParameter="typeParameter",H.parameter="parameter",H.variable="variable",H.property="property",H.enumMember="enumMember",H.event="event",H.function="function",H.method="method",H.macro="macro",H.keyword="keyword",H.modifier="modifier",H.comment="comment",H.string="string",H.number="number",H.regexp="regexp",H.operator="operator",H.decorator="decorator",(z=ts||(ts={})).declaration="declaration",z.definition="definition",z.readonly="readonly",z.static="static",z.deprecated="deprecated",z.abstract="abstract",z.async="async",z.modification="modification",z.documentation="documentation",z.defaultLibrary="defaultLibrary",(to||(to={})).is=function(e){return tk.objectLiteral(e)&&(void 0===e.resultId||"string"==typeof e.resultId)&&Array.isArray(e.data)&&(0===e.data.length||"number"==typeof e.data[0])},(Y=tl||(tl={})).create=function(e,t){return{range:e,text:t}},Y.is=function(e){return null!=e&&ef.is(e.range)&&tk.string(e.text)},(q=tu||(tu={})).create=function(e,t,r){return{range:e,variableName:t,caseSensitiveLookup:r}},q.is=function(e){return null!=e&&ef.is(e.range)&&tk.boolean(e.caseSensitiveLookup)&&(tk.string(e.variableName)||void 0===e.variableName)},(X=tc||(tc={})).create=function(e,t){return{range:e,expression:t}},X.is=function(e){return null!=e&&ef.is(e.range)&&(tk.string(e.expression)||void 0===e.expression)},(Q=td||(td={})).create=function(e,t){return{frameId:e,stoppedLocation:t}},Q.is=function(e){return tk.defined(e)&&ef.is(e.stoppedLocation)},(J=th||(th={})).Type=1,J.Parameter=2,J.is=function(e){return 1===e||2===e},(ee=tf||(tf={})).create=function(e){return{value:e}},ee.is=function(e){return tk.objectLiteral(e)&&(void 0===e.tooltip||tk.string(e.tooltip)||eG.is(e.tooltip))&&(void 0===e.location||ep.is(e.location))&&(void 0===e.command||eI.is(e.command))},(et=tp||(tp={})).create=function(e,t,r){let n={position:e,label:t};return void 0!==r&&(n.kind=r),n},et.is=function(e){return tk.objectLiteral(e)&&eh.is(e.position)&&(tk.string(e.label)||tk.typedArray(e.label,tf.is))&&(void 0===e.kind||th.is(e.kind))&&void 0===e.textEdits||tk.typedArray(e.textEdits,eS.is)&&(void 0===e.tooltip||tk.string(e.tooltip)||eG.is(e.tooltip))&&(void 0===e.paddingLeft||tk.boolean(e.paddingLeft))&&(void 0===e.paddingRight||tk.boolean(e.paddingRight))},(tm||(tm={})).createSnippet=function(e){return{kind:"snippet",value:e}},(tg||(tg={})).create=function(e,t,r,n){return{insertText:e,filterText:t,range:r,command:n}},(ty||(ty={})).create=function(e){return{items:e}},(er=tT||(tT={})).Invoked=0,er.Automatic=1,(tv||(tv={})).create=function(e,t){return{range:e,text:t}},(tR||(tR={})).create=function(e,t){return{triggerKind:e,selectedCompletionInfo:t}},(tE||(tE={})).is=function(e){return tk.objectLiteral(e)&&eu.is(e.uri)&&tk.string(e.name)},(en=tA||(tA={})).create=function(e,t,r,n){return new rd(e,t,r,n)},en.is=function(e){return!!(tk.defined(e)&&tk.string(e.uri)&&(tk.undefined(e.languageId)||tk.string(e.languageId))&&tk.uinteger(e.lineCount)&&tk.func(e.getText)&&tk.func(e.positionAt)&&tk.func(e.offsetAt))},en.applyEdits=function(e,t){let r=e.getText(),n=function e(t,r){if(t.length<=1)return t;let n=t.length/2|0,i=t.slice(0,n),a=t.slice(n);e(i,r),e(a,r);let s=0,o=0,l=0;for(;s<i.length&&o<a.length;)0>=r(i[s],a[o])?t[l++]=i[s++]:t[l++]=a[o++];for(;s<i.length;)t[l++]=i[s++];for(;o<a.length;)t[l++]=a[o++];return t}(t,(e,t)=>{let r=e.range.start.line-t.range.start.line;return 0===r?e.range.start.character-t.range.start.character:r}),i=r.length;for(let t=n.length-1;t>=0;t--){let a=n[t],s=e.offsetAt(a.range.start),o=e.offsetAt(a.range.end);if(o<=i)r=r.substring(0,s)+a.newText+r.substring(o,r.length);else throw Error("Overlapping edit");i=s}return r};class rd{constructor(e,t,r,n){this._uri=e,this._languageId=t,this._version=r,this._content=n,this._lineOffsets=void 0}get uri(){return this._uri}get languageId(){return this._languageId}get version(){return this._version}getText(e){if(e){let t=this.offsetAt(e.start),r=this.offsetAt(e.end);return this._content.substring(t,r)}return this._content}update(e,t){this._content=e.text,this._version=t,this._lineOffsets=void 0}getLineOffsets(){if(void 0===this._lineOffsets){let e=[],t=this._content,r=!0;for(let n=0;n<t.length;n++){r&&(e.push(n),r=!1);let i=t.charAt(n);r="\r"===i||"\n"===i,"\r"===i&&n+1<t.length&&"\n"===t.charAt(n+1)&&n++}r&&t.length>0&&e.push(t.length),this._lineOffsets=e}return this._lineOffsets}positionAt(e){e=Math.max(Math.min(e,this._content.length),0);let t=this.getLineOffsets(),r=0,n=t.length;if(0===n)return eh.create(0,e);for(;r<n;){let i=Math.floor((r+n)/2);t[i]>e?n=i:r=i+1}let i=r-1;return eh.create(i,e-t[i])}offsetAt(e){let t=this.getLineOffsets();if(e.line>=t.length)return this._content.length;if(e.line<0)return 0;let r=t[e.line],n=e.line+1<t.length?t[e.line+1]:this._content.length;return Math.max(Math.min(r+e.character,n),r)}get lineCount(){return this.getLineOffsets().length}}var rh=tk||(tk={});let rf=Object.prototype.toString;rh.defined=function(e){return void 0!==e},rh.undefined=function(e){return void 0===e},rh.boolean=function(e){return!0===e||!1===e},rh.string=function(e){return"[object String]"===rf.call(e)},rh.number=function(e){return"[object Number]"===rf.call(e)},rh.numberRange=function(e,t,r){return"[object Number]"===rf.call(e)&&t<=e&&e<=r},rh.integer=function(e){return"[object Number]"===rf.call(e)&&-0x80000000<=e&&e<=0x7fffffff},rh.uinteger=function(e){return"[object Number]"===rf.call(e)&&0<=e&&e<=0x7fffffff},rh.func=function(e){return"[object Function]"===rf.call(e)},rh.objectLiteral=function(e){return null!==e&&"object"==typeof e},rh.typedArray=function(e,t){return Array.isArray(e)&&e.every(t)};class rp{constructor(){this.nodeStack=[]}get current(){var e;return null!=(e=this.nodeStack[this.nodeStack.length-1])?e:this.rootNode}buildRootNode(e){return this.rootNode=new rv(e),this.rootNode.root=this.rootNode,this.nodeStack=[this.rootNode],this.rootNode}buildCompositeNode(e){let t=new ry;return t.grammarSource=e,t.root=this.rootNode,this.current.content.push(t),this.nodeStack.push(t),t}buildLeafNode(e,t){let r=new rg(e.startOffset,e.image.length,(0,tw.sp)(e),e.tokenType,!t);return r.grammarSource=t,r.root=this.rootNode,this.current.content.push(r),r}removeNode(e){let t=e.container;if(t){let r=t.content.indexOf(e);r>=0&&t.content.splice(r,1)}}addHiddenNodes(e){let t=[];for(let r of e){let e=new rg(r.startOffset,r.image.length,(0,tw.sp)(r),r.tokenType,!0);e.root=this.rootNode,t.push(e)}let r=this.current,n=!1;if(r.content.length>0)return void r.content.push(...t);for(;r.container;){let e=r.container.content.indexOf(r);if(e>0){r.container.content.splice(e,0,...t),n=!0;break}r=r.container}n||this.rootNode.content.unshift(...t)}construct(e){let t=this.current;"string"==typeof e.$type&&(this.current.astNode=e),e.$cstNode=t;let r=this.nodeStack.pop();(null==r?void 0:r.content.length)===0&&this.removeNode(r)}}class rm{get parent(){return this.container}get feature(){return this.grammarSource}get hidden(){return!1}get astNode(){var e,t;let r="string"==typeof(null==(e=this._astNode)?void 0:e.$type)?this._astNode:null==(t=this.container)?void 0:t.astNode;if(!r)throw Error("This node has no associated AST element");return r}set astNode(e){this._astNode=e}get element(){return this.astNode}get text(){return this.root.fullText.substring(this.offset,this.end)}}class rg extends rm{get offset(){return this._offset}get length(){return this._length}get end(){return this._offset+this._length}get hidden(){return this._hidden}get tokenType(){return this._tokenType}get range(){return this._range}constructor(e,t,r,n,i=!1){super(),this._hidden=i,this._offset=e,this._tokenType=n,this._length=t,this._range=r}}class ry extends rm{constructor(){super(...arguments),this.content=new rT(this)}get children(){return this.content}get offset(){var e,t;return null!=(t=null==(e=this.firstNonHiddenNode)?void 0:e.offset)?t:0}get length(){return this.end-this.offset}get end(){var e,t;return null!=(t=null==(e=this.lastNonHiddenNode)?void 0:e.end)?t:0}get range(){let e=this.firstNonHiddenNode,t=this.lastNonHiddenNode;if(!e||!t)return{start:eh.create(0,0),end:eh.create(0,0)};if(void 0===this._rangeCache){let{range:r}=e,{range:n}=t;this._rangeCache={start:r.start,end:n.end.line<r.start.line?r.start:n.end}}return this._rangeCache}get firstNonHiddenNode(){for(let e of this.content)if(!e.hidden)return e;return this.content[0]}get lastNonHiddenNode(){for(let e=this.content.length-1;e>=0;e--){let t=this.content[e];if(!t.hidden)return t}return this.content[this.content.length-1]}}class rT extends Array{constructor(e){super(),this.parent=e,Object.setPrototypeOf(this,rT.prototype)}push(...e){return this.addParents(e),super.push(...e)}unshift(...e){return this.addParents(e),super.unshift(...e)}splice(e,t,...r){return this.addParents(r),super.splice(e,t,...r)}addParents(e){for(let t of e)t.container=this.parent}}class rv extends ry{get text(){return this._text.substring(this.offset,this.end)}get fullText(){return this._text}constructor(e){super(),this._text="",this._text=null!=e?e:""}}let rR=Symbol("Datatype");function rE(e){return e.$type===rR}let rA=e=>e.endsWith("​")?e:e+"​";class rk{constructor(e){this._unorderedGroups=new Map,this.allRules=new Map,this.lexer=e.parser.Lexer;let t=this.lexer.definition,r="production"===e.LanguageMetaData.mode;this.wrapper=new rN(t,Object.assign(Object.assign({},e.parser.ParserConfig),{skipValidations:r,errorMessageProvider:e.parser.ParserErrorMessageProvider}))}alternatives(e,t){this.wrapper.wrapOr(e,t)}optional(e,t){this.wrapper.wrapOption(e,t)}many(e,t){this.wrapper.wrapMany(e,t)}atLeastOne(e,t){this.wrapper.wrapAtLeastOne(e,t)}getRule(e){return this.allRules.get(e)}isRecording(){return this.wrapper.IS_RECORDING}get unorderedGroups(){return this._unorderedGroups}getRuleStack(){return this.wrapper.RULE_STACK}finalize(){this.wrapper.wrapSelfAnalysis()}}class r$ extends rk{get current(){return this.stack[this.stack.length-1]}constructor(e){super(e),this.nodeBuilder=new rp,this.stack=[],this.assignmentMap=new Map,this.linker=e.references.Linker,this.converter=e.parser.ValueConverter,this.astReflection=e.shared.AstReflection}rule(e,t){let r=this.computeRuleType(e),n=this.wrapper.DEFINE_RULE(rA(e.name),this.startImplementation(r,t).bind(this));return this.allRules.set(e.name,n),e.entry&&(this.mainRule=n),n}computeRuleType(e){if(!e.fragment){if((0,tL.UP)(e))return rR;let t=(0,tL.$G)(e);return null!=t?t:e.name}}parse(e,t={}){this.nodeBuilder.buildRootNode(e);let r=this.lexerResult=this.lexer.tokenize(e);this.wrapper.input=r.tokens;let n=t.rule?this.allRules.get(t.rule):this.mainRule;if(!n)throw Error(t.rule?`No rule found with name '${t.rule}'`:"No main rule available.");let i=n.call(this.wrapper,{});return this.nodeBuilder.addHiddenNodes(r.hidden),this.unorderedGroups.clear(),this.lexerResult=void 0,{value:i,lexerErrors:r.errors,lexerReport:r.report,parserErrors:this.wrapper.errors}}startImplementation(e,t){return r=>{let n,i=!this.isRecording()&&void 0!==e;if(i){let t={$type:e};this.stack.push(t),e===rR&&(t.value="")}try{n=t(r)}catch(e){n=void 0}return void 0===n&&i&&(n=this.construct()),n}}extractHiddenTokens(e){let t=this.lexerResult.hidden;if(!t.length)return[];let r=e.startOffset;for(let e=0;e<t.length;e++)if(t[e].startOffset>r)return t.splice(0,e);return t.splice(0,t.length)}consume(e,t,r){let n=this.wrapper.wrapConsume(e,t);if(!this.isRecording()&&this.isValidToken(n)){let e=this.extractHiddenTokens(n);this.nodeBuilder.addHiddenNodes(e);let t=this.nodeBuilder.buildLeafNode(n,r),{assignment:i,isCrossRef:a}=this.getAssignment(r),s=this.current;if(i){let e=(0,tO.p1)(r)?n.image:this.converter.convert(n.image,t);this.assign(i.operator,i.feature,e,t,a)}else if(rE(s)){let e=n.image;(0,tO.p1)(r)||(e=this.converter.convert(e,t).toString()),s.value+=e}}}isValidToken(e){return!e.isInsertedInRecovery&&!isNaN(e.startOffset)&&"number"==typeof e.endOffset&&!isNaN(e.endOffset)}subrule(e,t,r,n,i){let a;this.isRecording()||r||(a=this.nodeBuilder.buildCompositeNode(n));let s=this.wrapper.wrapSubrule(e,t,i);!this.isRecording()&&a&&a.length>0&&this.performSubruleAssignment(s,n,a)}performSubruleAssignment(e,t,r){let{assignment:n,isCrossRef:i}=this.getAssignment(t);if(n)this.assign(n.operator,n.feature,e,r,i);else if(!n){let t=this.current;if(rE(t))t.value+=e.toString();else if("object"==typeof e&&e){let r=this.assignWithoutOverride(e,t);this.stack.pop(),this.stack.push(r)}}}action(e,t){if(!this.isRecording()){let r=this.current;t.feature&&t.operator?(r=this.construct(),this.nodeBuilder.removeNode(r.$cstNode),this.nodeBuilder.buildCompositeNode(t).content.push(r.$cstNode),this.stack.push({$type:e}),this.assign(t.operator,t.feature,r,r.$cstNode,!1)):r.$type=e}}construct(){if(this.isRecording())return;let e=this.current;return((0,rc.b2)(e),this.nodeBuilder.construct(e),this.stack.pop(),rE(e))?this.converter.convert(e.value,e.$cstNode):((0,rc.a1)(this.astReflection,e),e)}getAssignment(e){if(!this.assignmentMap.has(e)){let t=(0,rc.V_)(e,tO.B7);this.assignmentMap.set(e,{assignment:t,isCrossRef:!!t&&(0,tO.Ki)(t.terminal)})}return this.assignmentMap.get(e)}assign(e,t,r,n,i){let a,s=this.current;switch(a=i&&"string"==typeof r?this.linker.buildReference(s,t,n,r):r,e){case"=":s[t]=a;break;case"?=":s[t]=!0;break;case"+=":Array.isArray(s[t])||(s[t]=[]),s[t].push(a)}}assignWithoutOverride(e,t){for(let[r,n]of Object.entries(t)){let t=e[r];void 0===t?e[r]=n:Array.isArray(t)&&Array.isArray(n)&&(n.push(...t),e[r]=n)}let r=e.$cstNode;return r&&(r.astNode=void 0,e.$cstNode=void 0),e}get definitionErrors(){return this.wrapper.definitionErrors}}class rx{buildMismatchTokenMessage(e){return t_.Hs.buildMismatchTokenMessage(e)}buildNotAllInputParsedMessage(e){return t_.Hs.buildNotAllInputParsedMessage(e)}buildNoViableAltMessage(e){return t_.Hs.buildNoViableAltMessage(e)}buildEarlyExitMessage(e){return t_.Hs.buildEarlyExitMessage(e)}}class rI extends rx{buildMismatchTokenMessage({expected:e,actual:t}){let r=e.LABEL?"`"+e.LABEL+"`":e.name.endsWith(":KW")?`keyword '${e.name.substring(0,e.name.length-3)}'`:`token of type '${e.name}'`;return`Expecting ${r} but found \`${t.image}\`.`}buildNotAllInputParsedMessage({firstRedundant:e}){return`Expecting end of file but found \`${e.image}\`.`}}class rS extends rk{constructor(){super(...arguments),this.tokens=[],this.elementStack=[],this.lastElementStack=[],this.nextTokenIndex=0,this.stackSize=0}action(){}construct(){}parse(e){this.resetState();let t=this.lexer.tokenize(e,{mode:"partial"});return this.tokens=t.tokens,this.wrapper.input=[...this.tokens],this.mainRule.call(this.wrapper,{}),this.unorderedGroups.clear(),{tokens:this.tokens,elementStack:[...this.lastElementStack],tokenIndex:this.nextTokenIndex}}rule(e,t){let r=this.wrapper.DEFINE_RULE(rA(e.name),this.startImplementation(t).bind(this));return this.allRules.set(e.name,r),e.entry&&(this.mainRule=r),r}resetState(){this.elementStack=[],this.lastElementStack=[],this.nextTokenIndex=0,this.stackSize=0}startImplementation(e){return t=>{let r=this.keepStackSize();try{e(t)}finally{this.resetStackSize(r)}}}removeUnexpectedElements(){this.elementStack.splice(this.stackSize)}keepStackSize(){let e=this.elementStack.length;return this.stackSize=e,e}resetStackSize(e){this.removeUnexpectedElements(),this.stackSize=e}consume(e,t,r){this.wrapper.wrapConsume(e,t),this.isRecording()||(this.lastElementStack=[...this.elementStack,r],this.nextTokenIndex=this.currIdx+1)}subrule(e,t,r,n,i){this.before(n),this.wrapper.wrapSubrule(e,t,i),this.after(n)}before(e){this.isRecording()||this.elementStack.push(e)}after(e){if(!this.isRecording()){let t=this.elementStack.lastIndexOf(e);t>=0&&this.elementStack.splice(t)}}get currIdx(){return this.wrapper.currIdx}}let rC={recoveryEnabled:!0,nodeLocationTracking:"full",skipValidations:!0,errorMessageProvider:new rI};class rN extends t_.nu{constructor(e,t){super(e,Object.assign(Object.assign(Object.assign({},rC),{lookaheadStrategy:t&&"maxLookahead"in t?new t_.dV({maxLookahead:t.maxLookahead}):new re({logging:t.skipValidations?()=>{}:void 0})}),t))}get IS_RECORDING(){return this.RECORDING_PHASE}DEFINE_RULE(e,t){return this.RULE(e,t)}wrapSelfAnalysis(){this.performSelfAnalysis()}wrapConsume(e,t){return this.consume(e,t)}wrapSubrule(e,t,r){return this.subrule(e,t,{ARGS:[r]})}wrapOr(e,t){this.or(e,t)}wrapOption(e,t){this.option(e,t)}wrapMany(e,t){this.many(e,t)}wrapAtLeastOne(e,t){this.atLeastOne(e,t)}}var rw=r(63532),rL=r(31714);function rb(e,t,r){return function(e,t){let r=(0,tL.VD)(t,!1);for(let n of(0,rL.Vw)(t.rules).filter(tO.F9).filter(e=>r.has(e))){let t=Object.assign(Object.assign({},e),{consume:1,optional:1,subrule:1,many:1,or:1});e.parser.rule(n,function e(t,r,n=!1){let i;if((0,tO.p1)(r))i=function(e,t){let r=e.consume++,n=e.tokens[t.value];if(!n)throw Error("Could not find token for keyword: "+t.value);return()=>e.parser.consume(r,n,t)}(t,r);else if((0,tO.LG)(r))i=function(e,t){let r=(0,tL.z$)(t);return()=>e.parser.action(r,t)}(t,r);else if((0,tO.B7)(r))i=e(t,r.terminal);else if((0,tO.Ki)(r))i=function e(t,r,n=r.terminal){if(n)if((0,tO.t3)(n)&&(0,tO.F9)(n.rule.ref)){let e=n.rule.ref,i=t.subrule++;return n=>t.parser.subrule(i,rM(t,e),!1,r,n)}else if((0,tO.t3)(n)&&(0,tO.MS)(n.rule.ref)){let e=t.consume++,i=rD(t,n.rule.ref.name);return()=>t.parser.consume(e,i,r)}else if((0,tO.p1)(n)){let e=t.consume++,i=rD(t,n.value);return()=>t.parser.consume(e,i,r)}else throw Error("Could not build cross reference parser");{if(!r.type.ref)throw Error("Could not resolve reference to type: "+r.type.$refText);let n=(0,tL.ib)(r.type.ref),i=null==n?void 0:n.terminal;if(!i)throw Error("Could not find name assignment for type: "+(0,tL.z$)(r.type.ref));return e(t,r,i)}}(t,r);else if((0,tO.t3)(r))i=function(e,t){let r=t.rule.ref;if((0,tO.F9)(r)){let n=e.subrule++,i=r.fragment,a=t.arguments.length>0?function(e,t){let r=t.map(e=>rO(e.value));return t=>{let n={};for(let i=0;i<r.length;i++){let a=e.parameters[i],s=r[i];n[a.name]=s(t)}return n}}(r,t.arguments):()=>({});return s=>e.parser.subrule(n,rM(e,r),i,t,a(s))}if((0,tO.MS)(r)){let n=e.consume++,i=rD(e,r.name);return()=>e.parser.consume(n,i,t)}if(r)(0,rw.U)(r);else throw new rw.h(t.$cstNode,`Undefined rule: ${t.rule.$refText}`)}(t,r);else if((0,tO.MZ)(r))i=function(t,r){if(1===r.elements.length)return e(t,r.elements[0]);{let n=[];for(let i of r.elements){let r={ALT:e(t,i,!0)},a=r_(i);a&&(r.GATE=rO(a)),n.push(r)}let i=t.or++;return e=>t.parser.alternatives(i,n.map(t=>{let r={ALT:()=>t.ALT(e)},n=t.GATE;return n&&(r.GATE=()=>n(e)),r}))}}(t,r);else if((0,tO.W1)(r))i=function(t,r){if(1===r.elements.length)return e(t,r.elements[0]);let n=[];for(let i of r.elements){let r={ALT:e(t,i,!0)},a=r_(i);a&&(r.GATE=rO(a)),n.push(r)}let i=t.or++,a=(e,t)=>{let r=t.getRuleStack().join("-");return`uGroup_${e}_${r}`},s=rP(t,r_(r),e=>t.parser.alternatives(i,n.map((r,n)=>{let s={ALT:()=>!0},o=t.parser;s.ALT=()=>{if(r.ALT(e),!o.isRecording()){let e=a(i,o);o.unorderedGroups.get(e)||o.unorderedGroups.set(e,[]);let t=o.unorderedGroups.get(e);void 0===(null==t?void 0:t[n])&&(t[n]=!0)}};let l=r.GATE;return l?s.GATE=()=>l(e):s.GATE=()=>{let e=o.unorderedGroups.get(a(i,o));return!(null==e?void 0:e[n])},s})),"*");return e=>{s(e),t.parser.isRecording()||t.parser.unorderedGroups.delete(a(i,t.parser))}}(t,r);else if((0,tO.ty)(r))i=function(t,r){let n=r.elements.map(r=>e(t,r));return e=>n.forEach(t=>t(e))}(t,r);else if((0,tO.rT)(r)){let e=t.consume++;i=()=>t.parser.consume(e,t_.sd,r)}else throw new rw.h(r.$cstNode,`Unexpected element type: ${r.$type}`);return rP(t,n?void 0:r_(r),i,r.cardinality)}(t,n.definition))}}({parser:t,tokens:r,ruleNames:new Map},e),t}function rO(e){if((0,tO.F8)(e)){let t=rO(e.left),r=rO(e.right);return e=>t(e)||r(e)}if((0,tO.TB)(e)){let t=rO(e.left),r=rO(e.right);return e=>t(e)&&r(e)}if((0,tO.Ii)(e)){let t=rO(e.value);return e=>!t(e)}if((0,tO.yW)(e)){let t=e.parameter.ref.name;return e=>void 0!==e&&!0===e[t]}if((0,tO.L)(e)){let t=!!e.true;return()=>t}(0,rw.U)(e)}function r_(e){if((0,tO.ty)(e))return e.guardCondition}function rP(e,t,r,n){let i=t&&rO(t);if(!n)if(!i)return r;else{let t=e.or++;return n=>e.parser.alternatives(t,[{ALT:()=>r(n),GATE:()=>i(n)},{ALT:(0,t_._o)(),GATE:()=>!i(n)}])}if("*"===n){let t=e.many++;return n=>e.parser.many(t,{DEF:()=>r(n),GATE:i?()=>i(n):void 0})}if("+"===n){let t=e.many++;if(!i)return n=>e.parser.atLeastOne(t,{DEF:()=>r(n)});{let n=e.or++;return a=>e.parser.alternatives(n,[{ALT:()=>e.parser.atLeastOne(t,{DEF:()=>r(a)}),GATE:()=>i(a)},{ALT:(0,t_._o)(),GATE:()=>!i(a)}])}}if("?"===n){let t=e.optional++;return n=>e.parser.optional(t,{DEF:()=>r(n),GATE:i?()=>i(n):void 0})}(0,rw.U)(n)}function rM(e,t){let r=function(e,t){if((0,tO.F9)(t))return t.name;{if(e.ruleNames.has(t))return e.ruleNames.get(t);let r=t,n=r.$container,i=t.$type;for(;!(0,tO.F9)(n);)((0,tO.ty)(n)||(0,tO.MZ)(n)||(0,tO.W1)(n))&&(i=n.elements.indexOf(r).toString()+":"+i),r=n,n=n.$container;return i=n.name+":"+i,e.ruleNames.set(t,i),i}}(e,t),n=e.parser.getRule(r);if(!n)throw Error(`Rule "${r}" not found."`);return n}function rD(e,t){let r=e.tokens[t];if(!r)throw Error(`Token "${t}" not found."`);return r}var rZ=r(93774),rF=r(78249),rU=r(16213),rG=r(89365);let rB=0,rV=Symbol("OperationCancelled");async function rK(e){if(e===rU.CancellationToken.None)return;let t=performance.now();if(t-rB>=10&&(rB=t,await new Promise(e=>{"undefined"==typeof setImmediate?setTimeout(e,0):setImmediate(e)}),rB=performance.now()),e.isCancellationRequested)throw rV}class rj{constructor(){this.promise=new Promise((e,t)=>{this.resolve=t=>(e(t),this),this.reject=e=>(t(e),this)})}}class rW{constructor(e,t,r,n){this._uri=e,this._languageId=t,this._version=r,this._content=n,this._lineOffsets=void 0}get uri(){return this._uri}get languageId(){return this._languageId}get version(){return this._version}getText(e){if(e){let t=this.offsetAt(e.start),r=this.offsetAt(e.end);return this._content.substring(t,r)}return this._content}update(e,t){for(let t of e)if(rW.isIncremental(t)){let e=rY(t.range),r=this.offsetAt(e.start),n=this.offsetAt(e.end);this._content=this._content.substring(0,r)+t.text+this._content.substring(n,this._content.length);let i=Math.max(e.start.line,0),a=Math.max(e.end.line,0),s=this._lineOffsets,o=rH(t.text,!1,r);if(a-i===o.length)for(let e=0,t=o.length;e<t;e++)s[e+i+1]=o[e];else o.length<1e4?s.splice(i+1,a-i,...o):this._lineOffsets=s=s.slice(0,i+1).concat(o,s.slice(a+1));let l=t.text.length-(n-r);if(0!==l)for(let e=i+1+o.length,t=s.length;e<t;e++)s[e]=s[e]+l}else if(rW.isFull(t))this._content=t.text,this._lineOffsets=void 0;else throw Error("Unknown change event received");this._version=t}getLineOffsets(){return void 0===this._lineOffsets&&(this._lineOffsets=rH(this._content,!0)),this._lineOffsets}positionAt(e){e=Math.max(Math.min(e,this._content.length),0);let t=this.getLineOffsets(),r=0,n=t.length;if(0===n)return{line:0,character:e};for(;r<n;){let i=Math.floor((r+n)/2);t[i]>e?n=i:r=i+1}let i=r-1;return e=this.ensureBeforeEOL(e,t[i]),{line:i,character:e-t[i]}}offsetAt(e){let t=this.getLineOffsets();if(e.line>=t.length)return this._content.length;if(e.line<0)return 0;let r=t[e.line];if(e.character<=0)return r;let n=e.line+1<t.length?t[e.line+1]:this._content.length,i=Math.min(r+e.character,n);return this.ensureBeforeEOL(i,r)}ensureBeforeEOL(e,t){for(;e>t&&rz(this._content.charCodeAt(e-1));)e--;return e}get lineCount(){return this.getLineOffsets().length}static isIncremental(e){return null!=e&&"string"==typeof e.text&&void 0!==e.range&&(void 0===e.rangeLength||"number"==typeof e.rangeLength)}static isFull(e){return null!=e&&"string"==typeof e.text&&void 0===e.range&&void 0===e.rangeLength}}function rH(e,t,r=0){let n=t?[r]:[];for(let t=0;t<e.length;t++){let i=e.charCodeAt(t);rz(i)&&(13===i&&t+1<e.length&&10===e.charCodeAt(t+1)&&t++,n.push(r+t+1))}return n}function rz(e){return 13===e||10===e}function rY(e){let t=e.start,r=e.end;return t.line>r.line||t.line===r.line&&t.character>r.character?{start:r,end:t}:e}function rq(e){let t=rY(e.range);return t!==e.range?{newText:e.newText,range:t}:e}(ei=t$||(t$={})).create=function(e,t,r,n){return new rW(e,t,r,n)},ei.update=function(e,t,r){if(e instanceof rW)return e.update(t,r),e;throw Error("TextDocument.update: document must be created by TextDocument.create")},ei.applyEdits=function(e,t){let r=e.getText(),n=function e(t,r){if(t.length<=1)return t;let n=t.length/2|0,i=t.slice(0,n),a=t.slice(n);e(i,r),e(a,r);let s=0,o=0,l=0;for(;s<i.length&&o<a.length;)0>=r(i[s],a[o])?t[l++]=i[s++]:t[l++]=a[o++];for(;s<i.length;)t[l++]=i[s++];for(;o<a.length;)t[l++]=a[o++];return t}(t.map(rq),(e,t)=>{let r=e.range.start.line-t.range.start.line;return 0===r?e.range.start.character-t.range.start.character:r}),i=0,a=[];for(let t of n){let n=e.offsetAt(t.range.start);if(n<i)throw Error("Overlapping edit");n>i&&a.push(r.substring(i,n)),t.newText.length&&a.push(t.newText),i=e.offsetAt(t.range.end)}return a.push(r.substr(i)),a.join("")};var rX=r(1862);(ea=tx||(tx={}))[ea.Changed=0]="Changed",ea[ea.Parsed=1]="Parsed",ea[ea.IndexedContent=2]="IndexedContent",ea[ea.ComputedScopes=3]="ComputedScopes",ea[ea.Linked=4]="Linked",ea[ea.IndexedReferences=5]="IndexedReferences",ea[ea.Validated=6]="Validated";class rQ{constructor(e){this.serviceRegistry=e.ServiceRegistry,this.textDocuments=e.workspace.TextDocuments,this.fileSystemProvider=e.workspace.FileSystemProvider}async fromUri(e,t=rU.CancellationToken.None){let r=await this.fileSystemProvider.readFile(e);return this.createAsync(e,r,t)}fromTextDocument(e,t,r){return(t=null!=t?t:rX.o.parse(e.uri),rU.CancellationToken.is(r))?this.createAsync(t,e,r):this.create(t,e,r)}fromString(e,t,r){return rU.CancellationToken.is(r)?this.createAsync(t,e,r):this.create(t,e,r)}fromModel(e,t){return this.create(t,{$model:e})}create(e,t,r){if("string"==typeof t){let n=this.parse(e,t,r);return this.createLangiumDocument(n,e,void 0,t)}if("$model"in t){let r={value:t.$model,parserErrors:[],lexerErrors:[]};return this.createLangiumDocument(r,e)}{let n=this.parse(e,t.getText(),r);return this.createLangiumDocument(n,e,t)}}async createAsync(e,t,r){if("string"==typeof t){let n=await this.parseAsync(e,t,r);return this.createLangiumDocument(n,e,void 0,t)}{let n=await this.parseAsync(e,t.getText(),r);return this.createLangiumDocument(n,e,t)}}createLangiumDocument(e,t,r,n){let i;if(r)i={parseResult:e,uri:t,state:tx.Parsed,references:[],textDocument:r};else{let r=this.createTextDocumentGetter(t,n);i={parseResult:e,uri:t,state:tx.Parsed,references:[],get textDocument(){return r()}}}return e.value.$document=i,i}async update(e,t){var r,n;let i=null==(r=e.parseResult.value.$cstNode)?void 0:r.root.fullText,a=null==(n=this.textDocuments)?void 0:n.get(e.uri.toString()),s=a?a.getText():await this.fileSystemProvider.readFile(e.uri);if(a)Object.defineProperty(e,"textDocument",{value:a});else{let t=this.createTextDocumentGetter(e.uri,s);Object.defineProperty(e,"textDocument",{get:t})}return i!==s&&(e.parseResult=await this.parseAsync(e.uri,s,t),e.parseResult.value.$document=e),e.state=tx.Parsed,e}parse(e,t,r){return this.serviceRegistry.getServices(e).parser.LangiumParser.parse(t,r)}parseAsync(e,t,r){return this.serviceRegistry.getServices(e).parser.AsyncParser.parse(t,r)}createTextDocumentGetter(e,t){let r,n=this.serviceRegistry;return()=>null!=r?r:r=t$.create(e.toString(),n.getServices(e).LanguageMetaData.languageId,0,null!=t?t:"")}}class rJ{constructor(e){this.documentMap=new Map,this.langiumDocumentFactory=e.workspace.LangiumDocumentFactory,this.serviceRegistry=e.ServiceRegistry}get all(){return(0,rL.Vw)(this.documentMap.values())}addDocument(e){let t=e.uri.toString();if(this.documentMap.has(t))throw Error(`A document with the URI '${t}' is already present.`);this.documentMap.set(t,e)}getDocument(e){let t=e.toString();return this.documentMap.get(t)}async getOrCreateDocument(e,t){let r=this.getDocument(e);return r||(r=await this.langiumDocumentFactory.fromUri(e,t),this.addDocument(r)),r}createDocument(e,t,r){if(r)return this.langiumDocumentFactory.fromString(t,e,r).then(e=>(this.addDocument(e),e));{let r=this.langiumDocumentFactory.fromString(t,e);return this.addDocument(r),r}}hasDocument(e){return this.documentMap.has(e.toString())}invalidateDocument(e){let t=e.toString(),r=this.documentMap.get(t);return r&&(this.serviceRegistry.getServices(e).references.Linker.unlink(r),r.state=tx.Changed,r.precomputedScopes=void 0,r.diagnostics=void 0),r}deleteDocument(e){let t=e.toString(),r=this.documentMap.get(t);return r&&(r.state=tx.Changed,this.documentMap.delete(t)),r}}let r0=Symbol("ref_resolving");class r1{constructor(e){this.reflection=e.shared.AstReflection,this.langiumDocuments=()=>e.shared.workspace.LangiumDocuments,this.scopeProvider=e.references.ScopeProvider,this.astNodeLocator=e.workspace.AstNodeLocator}async link(e,t=rU.CancellationToken.None){for(let r of(0,rc.Zc)(e.parseResult.value))await rK(t),(0,rc.fy)(r).forEach(t=>this.doLink(t,e))}doLink(e,t){var r;let n=e.reference;if(void 0===n._ref){n._ref=r0;try{let t=this.getCandidate(e);if((0,rG.et)(t))n._ref=t;else if(n._nodeDescription=t,this.langiumDocuments().hasDocument(t.documentUri)){let r=this.loadAstNode(t);n._ref=null!=r?r:this.createLinkingError(e,t)}else n._ref=void 0}catch(i){console.error(`An error occurred while resolving reference to '${n.$refText}':`,i);let t=null!=(r=i.message)?r:String(i);n._ref=Object.assign(Object.assign({},e),{message:`An error occurred while resolving reference to '${n.$refText}': ${t}`})}t.references.push(n)}}unlink(e){for(let t of e.references)delete t._ref,delete t._nodeDescription;e.references=[]}getCandidate(e){let t=this.scopeProvider.getScope(e).getElement(e.reference.$refText);return null!=t?t:this.createLinkingError(e)}buildReference(e,t,r,n){let i=this,a={$refNode:r,$refText:n,get ref(){var s;if((0,rG.xA)(this._ref))return this._ref;if((0,rG.SI)(this._nodeDescription)){let r=i.loadAstNode(this._nodeDescription);this._ref=null!=r?r:i.createLinkingError({reference:a,container:e,property:t},this._nodeDescription)}else if(void 0===this._ref){this._ref=r0;let r=(0,rc.E$)(e).$document,n=i.getLinkedNode({reference:a,container:e,property:t});if(n.error&&r&&r.state<tx.ComputedScopes)return this._ref=void 0;this._ref=null!=(s=n.node)?s:n.error,this._nodeDescription=n.descr,null==r||r.references.push(this)}else if(this._ref===r0)throw Error(`Cyclic reference resolution detected: ${i.astNodeLocator.getAstNodePath(e)}/${t} (symbol '${n}')`);return(0,rG.xA)(this._ref)?this._ref:void 0},get $nodeDescription(){return this._nodeDescription},get error(){return(0,rG.et)(this._ref)?this._ref:void 0}};return a}getLinkedNode(e){var t;try{let t=this.getCandidate(e);if((0,rG.et)(t))return{error:t};let r=this.loadAstNode(t);if(r)return{node:r,descr:t};return{descr:t,error:this.createLinkingError(e,t)}}catch(n){console.error(`An error occurred while resolving reference to '${e.reference.$refText}':`,n);let r=null!=(t=n.message)?t:String(n);return{error:Object.assign(Object.assign({},e),{message:`An error occurred while resolving reference to '${e.reference.$refText}': ${r}`})}}}loadAstNode(e){if(e.node)return e.node;let t=this.langiumDocuments().getDocument(e.documentUri);if(t)return this.astNodeLocator.getAstNode(t.parseResult.value,e.path)}createLinkingError(e,t){let r=(0,rc.E$)(e.container).$document;r&&r.state<tx.ComputedScopes&&console.warn(`Attempted reference resolution before document reached ComputedScopes state (${r.uri}).`);let n=this.reflection.getReferenceType(e);return Object.assign(Object.assign({},e),{message:`Could not resolve reference to ${n} named '${e.reference.$refText}'.`,targetDescription:t})}}class r2{getName(e){if("string"==typeof e.name)return e.name}getNameNode(e){return(0,tL.vb)(e.$cstNode,"name")}}(es=tI||(tI={})).basename=rX.c.basename,es.dirname=rX.c.dirname,es.extname=rX.c.extname,es.joinPath=rX.c.joinPath,es.resolvePath=rX.c.resolvePath,es.equals=function(e,t){return(null==e?void 0:e.toString())===(null==t?void 0:t.toString())},es.relative=function(e,t){let r="string"==typeof e?e:e.path,n="string"==typeof t?t:t.path,i=r.split("/").filter(e=>e.length>0),a=n.split("/").filter(e=>e.length>0),s=0;for(;s<i.length&&i[s]===a[s];s++);return"../".repeat(i.length-s)+a.slice(s).join("/")},es.normalize=function(e){return rX.o.parse(e.toString()).toString()};class r6{constructor(e){this.nameProvider=e.references.NameProvider,this.index=e.shared.workspace.IndexManager,this.nodeLocator=e.workspace.AstNodeLocator}findDeclaration(e){if(e){let t=(0,tL.h7)(e),r=e.astNode;if(t&&r){let n=r[t.feature];if((0,rG.Yk)(n))return n.ref;if(Array.isArray(n)){for(let t of n)if((0,rG.Yk)(t)&&t.$refNode&&t.$refNode.offset<=e.offset&&t.$refNode.end>=e.end)return t.ref}}if(r){let t=this.nameProvider.getNameNode(r);if(t&&(t===e||(0,tw.OB)(e,t)))return r}}}findDeclarationNode(e){let t=this.findDeclaration(e);if(null==t?void 0:t.$cstNode){let e=this.nameProvider.getNameNode(t);return null!=e?e:t.$cstNode}}findReferences(e,t){let r=[];if(t.includeDeclaration){let t=this.getReferenceToSelf(e);t&&r.push(t)}let n=this.index.findAllReferences(e,this.nodeLocator.getAstNodePath(e));return t.documentUri&&(n=n.filter(e=>tI.equals(e.sourceUri,t.documentUri))),r.push(...n),(0,rL.Vw)(r)}getReferenceToSelf(e){let t=this.nameProvider.getNameNode(e);if(t){let r=(0,rc.Me)(e),n=this.nodeLocator.getAstNodePath(e);return{sourceUri:r.uri,sourcePath:n,targetUri:r.uri,targetPath:n,segment:(0,tw.yn)(t),local:!0}}}}class r9{constructor(e){if(this.map=new Map,e)for(let[t,r]of e)this.add(t,r)}get size(){return rL.IH.sum((0,rL.Vw)(this.map.values()).map(e=>e.length))}clear(){this.map.clear()}delete(e,t){if(void 0===t)return this.map.delete(e);{let r=this.map.get(e);if(r){let n=r.indexOf(t);if(n>=0)return 1===r.length?this.map.delete(e):r.splice(n,1),!0}return!1}}get(e){var t;return null!=(t=this.map.get(e))?t:[]}has(e,t){if(void 0===t)return this.map.has(e);{let r=this.map.get(e);return!!r&&r.indexOf(t)>=0}}add(e,t){return this.map.has(e)?this.map.get(e).push(t):this.map.set(e,[t]),this}addAll(e,t){return this.map.has(e)?this.map.get(e).push(...t):this.map.set(e,Array.from(t)),this}forEach(e){this.map.forEach((t,r)=>t.forEach(t=>e(t,r,this)))}[Symbol.iterator](){return this.entries().iterator()}entries(){return(0,rL.Vw)(this.map.entries()).flatMap(([e,t])=>t.map(t=>[e,t]))}keys(){return(0,rL.Vw)(this.map.keys())}values(){return(0,rL.Vw)(this.map.values()).flat()}entriesGroupedByKey(){return(0,rL.Vw)(this.map.entries())}}class r3{get size(){return this.map.size}constructor(e){if(this.map=new Map,this.inverse=new Map,e)for(let[t,r]of e)this.set(t,r)}clear(){this.map.clear(),this.inverse.clear()}set(e,t){return this.map.set(e,t),this.inverse.set(t,e),this}get(e){return this.map.get(e)}getKey(e){return this.inverse.get(e)}delete(e){let t=this.map.get(e);return void 0!==t&&(this.map.delete(e),this.inverse.delete(t),!0)}}class r7{constructor(e){this.nameProvider=e.references.NameProvider,this.descriptions=e.workspace.AstNodeDescriptionProvider}async computeExports(e,t=rU.CancellationToken.None){return this.computeExportsForNode(e.parseResult.value,e,void 0,t)}async computeExportsForNode(e,t,r=rc.sx,n=rU.CancellationToken.None){let i=[];for(let a of(this.exportNode(e,i,t),r(e)))await rK(n),this.exportNode(a,i,t);return i}exportNode(e,t,r){let n=this.nameProvider.getName(e);n&&t.push(this.descriptions.createDescription(e,n,r))}async computeLocalScopes(e,t=rU.CancellationToken.None){let r=e.parseResult.value,n=new r9;for(let i of(0,rc.VY)(r))await rK(t),this.processNode(i,e,n);return n}processNode(e,t,r){let n=e.$container;if(n){let i=this.nameProvider.getName(e);i&&r.add(n,this.descriptions.createDescription(e,i,t))}}}class r4{constructor(e,t,r){var n;this.elements=e,this.outerScope=t,this.caseInsensitive=null!=(n=null==r?void 0:r.caseInsensitive)&&n}getAllElements(){return this.outerScope?this.elements.concat(this.outerScope.getAllElements()):this.elements}getElement(e){let t=this.caseInsensitive?this.elements.find(t=>t.name.toLowerCase()===e.toLowerCase()):this.elements.find(t=>t.name===e);return t||(this.outerScope?this.outerScope.getElement(e):void 0)}}class r5{constructor(e,t,r){var n;for(let t of(this.elements=new Map,this.caseInsensitive=null!=(n=null==r?void 0:r.caseInsensitive)&&n,e)){let e=this.caseInsensitive?t.name.toLowerCase():t.name;this.elements.set(e,t)}this.outerScope=t}getElement(e){let t=this.caseInsensitive?e.toLowerCase():e,r=this.elements.get(t);return r||(this.outerScope?this.outerScope.getElement(e):void 0)}getAllElements(){let e=(0,rL.Vw)(this.elements.values());return this.outerScope&&(e=e.concat(this.outerScope.getAllElements())),e}}class r8{constructor(){this.toDispose=[],this.isDisposed=!1}onDispose(e){this.toDispose.push(e)}dispose(){this.throwIfDisposed(),this.clear(),this.isDisposed=!0,this.toDispose.forEach(e=>e.dispose())}throwIfDisposed(){if(this.isDisposed)throw Error("This cache has already been disposed")}}class ne extends r8{constructor(){super(...arguments),this.cache=new Map}has(e){return this.throwIfDisposed(),this.cache.has(e)}set(e,t){this.throwIfDisposed(),this.cache.set(e,t)}get(e,t){if(this.throwIfDisposed(),this.cache.has(e))return this.cache.get(e);if(t){let r=t();return this.cache.set(e,r),r}}delete(e){return this.throwIfDisposed(),this.cache.delete(e)}clear(){this.throwIfDisposed(),this.cache.clear()}}class nt extends r8{constructor(e){super(),this.cache=new Map,this.converter=null!=e?e:e=>e}has(e,t){return this.throwIfDisposed(),this.cacheForContext(e).has(t)}set(e,t,r){this.throwIfDisposed(),this.cacheForContext(e).set(t,r)}get(e,t,r){this.throwIfDisposed();let n=this.cacheForContext(e);if(n.has(t))return n.get(t);if(r){let e=r();return n.set(t,e),e}}delete(e,t){return this.throwIfDisposed(),this.cacheForContext(e).delete(t)}clear(e){if(this.throwIfDisposed(),e){let t=this.converter(e);this.cache.delete(t)}else this.cache.clear()}cacheForContext(e){let t=this.converter(e),r=this.cache.get(t);return r||(r=new Map,this.cache.set(t,r)),r}}class nr extends ne{constructor(e,t){super(),t?(this.toDispose.push(e.workspace.DocumentBuilder.onBuildPhase(t,()=>{this.clear()})),this.toDispose.push(e.workspace.DocumentBuilder.onUpdate((e,t)=>{t.length>0&&this.clear()}))):this.toDispose.push(e.workspace.DocumentBuilder.onUpdate(()=>{this.clear()}))}}class nn{constructor(e){this.reflection=e.shared.AstReflection,this.nameProvider=e.references.NameProvider,this.descriptions=e.workspace.AstNodeDescriptionProvider,this.indexManager=e.shared.workspace.IndexManager,this.globalScopeCache=new nr(e.shared)}getScope(e){let t=[],r=this.reflection.getReferenceType(e),n=(0,rc.Me)(e.container).precomputedScopes;if(n){let i=e.container;do{let e=n.get(i);e.length>0&&t.push((0,rL.Vw)(e).filter(e=>this.reflection.isSubtype(e.type,r))),i=i.$container}while(i)}let i=this.getGlobalScope(r,e);for(let e=t.length-1;e>=0;e--)i=this.createScope(t[e],i);return i}createScope(e,t,r){return new r4((0,rL.Vw)(e),t,r)}createScopeForNodes(e,t,r){return new r4((0,rL.Vw)(e).map(e=>{let t=this.nameProvider.getName(e);if(t)return this.descriptions.createDescription(e,t)}).nonNullable(),t,r)}getGlobalScope(e,t){return this.globalScopeCache.get(e,()=>new r5(this.indexManager.allElements(e)))}}function ni(e){return"object"==typeof e&&!!e&&("$ref"in e||"$error"in e)}class na{constructor(e){this.ignoreProperties=new Set(["$container","$containerProperty","$containerIndex","$document","$cstNode"]),this.langiumDocuments=e.shared.workspace.LangiumDocuments,this.astNodeLocator=e.workspace.AstNodeLocator,this.nameProvider=e.references.NameProvider,this.commentProvider=e.documentation.CommentProvider}serialize(e,t){let r=null!=t?t:{},n=null==t?void 0:t.replacer,i=(e,t)=>this.replacer(e,t,r),a=n?(e,t)=>n(e,t,i):i;try{return this.currentDocument=(0,rc.Me)(e),JSON.stringify(e,a,null==t?void 0:t.space)}finally{this.currentDocument=void 0}}deserialize(e,t){let r=JSON.parse(e);return this.linkNode(r,r,null!=t?t:{}),r}replacer(e,t,{refText:r,sourceText:n,textRegions:i,comments:a,uriConverter:s}){var o,l,u,c;if(!this.ignoreProperties.has(e)){if((0,rG.Yk)(t)){let e=t.ref,n=r?t.$refText:void 0;if(!e)return{$error:null!=(l=null==(o=t.error)?void 0:o.message)?l:"Could not resolve reference",$refText:n};{let r=(0,rc.Me)(e),i="";this.currentDocument&&this.currentDocument!==r&&(i=s?s(r.uri,t):r.uri.toString());let a=this.astNodeLocator.getAstNodePath(e);return{$ref:`${i}#${a}`,$refText:n}}}if(!(0,rG.xA)(t))return t;{let r;if(i&&(r=this.addAstNodeRegionWithAssignmentsTo(Object.assign({},t)),(!e||t.$document)&&(null==r?void 0:r.$textRegion)&&(r.$textRegion.documentURI=null==(u=this.currentDocument)?void 0:u.uri.toString())),n&&!e&&(null!=r||(r=Object.assign({},t)),r.$sourceText=null==(c=t.$cstNode)?void 0:c.text),a){null!=r||(r=Object.assign({},t));let e=this.commentProvider.getComment(t);e&&(r.$comment=e.replace(/\r/g,""))}return null!=r?r:t}}}addAstNodeRegionWithAssignmentsTo(e){let t=e=>({offset:e.offset,end:e.end,length:e.length,range:e.range});if(e.$cstNode){let r=(e.$textRegion=t(e.$cstNode)).assignments={};return Object.keys(e).filter(e=>!e.startsWith("$")).forEach(n=>{let i=(0,tL.EL)(e.$cstNode,n).map(t);0!==i.length&&(r[n]=i)}),e}}linkNode(e,t,r,n,i,a){for(let[n,i]of Object.entries(e))if(Array.isArray(i))for(let a=0;a<i.length;a++){let s=i[a];ni(s)?i[a]=this.reviveReference(e,n,t,s,r):(0,rG.xA)(s)&&this.linkNode(s,t,r,e,n,a)}else ni(i)?e[n]=this.reviveReference(e,n,t,i,r):(0,rG.xA)(i)&&this.linkNode(i,t,r,e,n);e.$container=n,e.$containerProperty=i,e.$containerIndex=a}reviveReference(e,t,r,n,i){let a=n.$refText,s=n.$error;if(n.$ref){let e=this.getRefNode(r,n.$ref,i.uriConverter);if((0,rG.xA)(e))return a||(a=this.nameProvider.getName(e)),{$refText:null!=a?a:"",ref:e};s=e}if(s){let r={$refText:null!=a?a:""};return r.error={container:e,property:t,message:s,reference:r},r}}getRefNode(e,t,r){try{let n=t.indexOf("#");if(0===n){let r=this.astNodeLocator.getAstNode(e,t.substring(1));if(!r)return"Could not resolve path: "+t;return r}if(n<0){let e=r?r(t):rX.o.parse(t),n=this.langiumDocuments.getDocument(e);if(!n)return"Could not find document for URI: "+t;return n.parseResult.value}let i=r?r(t.substring(0,n)):rX.o.parse(t.substring(0,n)),a=this.langiumDocuments.getDocument(i);if(!a)return"Could not find document for URI: "+t;if(n===t.length-1)return a.parseResult.value;let s=this.astNodeLocator.getAstNode(a.parseResult.value,t.substring(n+1));if(!s)return"Could not resolve URI: "+t;return s}catch(e){return String(e)}}}class ns{get map(){return this.fileExtensionMap}constructor(e){this.languageIdMap=new Map,this.fileExtensionMap=new Map,this.textDocuments=null==e?void 0:e.workspace.TextDocuments}register(e){let t=e.LanguageMetaData;for(let r of t.fileExtensions)this.fileExtensionMap.has(r)&&console.warn(`The file extension ${r} is used by multiple languages. It is now assigned to '${t.languageId}'.`),this.fileExtensionMap.set(r,e);this.languageIdMap.set(t.languageId,e),1===this.languageIdMap.size?this.singleton=e:this.singleton=void 0}getServices(e){var t,r;if(void 0!==this.singleton)return this.singleton;if(0===this.languageIdMap.size)throw Error("The service registry is empty. Use `register` to register the services of a language.");let n=null==(r=null==(t=this.textDocuments)?void 0:t.get(e))?void 0:r.languageId;if(void 0!==n){let e=this.languageIdMap.get(n);if(e)return e}let i=tI.extname(e),a=this.fileExtensionMap.get(i);if(!a)if(n)throw Error(`The service registry contains no services for the extension '${i}' for language '${n}'.`);else throw Error(`The service registry contains no services for the extension '${i}'.`);return a}hasServices(e){try{return this.getServices(e),!0}catch(e){return!1}}get all(){return Array.from(this.languageIdMap.values())}}(tS||(tS={})).all=["fast","slow","built-in"];class no{constructor(e){this.entries=new r9,this.entriesBefore=[],this.entriesAfter=[],this.reflection=e.shared.AstReflection}register(e,t=this,r="fast"){if("built-in"===r)throw Error("The 'built-in' category is reserved for lexer, parser, and linker errors.");for(let[n,i]of Object.entries(e))if(Array.isArray(i))for(let e of i){let i={check:this.wrapValidationException(e,t),category:r};this.addEntry(n,i)}else if("function"==typeof i){let e={check:this.wrapValidationException(i,t),category:r};this.addEntry(n,e)}else(0,rw.U)(i)}wrapValidationException(e,t){return async(r,n,i)=>{await this.handleException(()=>e.call(t,r,n,i),"An error occurred during validation",n,r)}}async handleException(e,t,r,n){try{await e()}catch(i){if(i===rV)throw i;console.error(`${t}:`,i),i instanceof Error&&i.stack&&console.error(i.stack);let e=i instanceof Error?i.message:String(i);r("error",`${t}: ${e}`,{node:n})}}addEntry(e,t){if("AstNode"===e)return void this.entries.add("AstNode",t);for(let r of this.reflection.getAllSubTypes(e))this.entries.add(r,t)}getChecks(e,t){let r=(0,rL.Vw)(this.entries.get(e)).concat(this.entries.get("AstNode"));return t&&(r=r.filter(e=>t.includes(e.category))),r.map(e=>e.check)}registerBeforeDocument(e,t=this){this.entriesBefore.push(this.wrapPreparationException(e,"An error occurred during set-up of the validation",t))}registerAfterDocument(e,t=this){this.entriesAfter.push(this.wrapPreparationException(e,"An error occurred during tear-down of the validation",t))}wrapPreparationException(e,t,r){return async(n,i,a,s)=>{await this.handleException(()=>e.call(r,n,i,a,s),t,i,n)}}get checksBefore(){return this.entriesBefore}get checksAfter(){return this.entriesAfter}}class nl{constructor(e){this.validationRegistry=e.validation.ValidationRegistry,this.metadata=e.LanguageMetaData}async validateDocument(e,t={},r=rU.CancellationToken.None){let n=e.parseResult,i=[];if((await rK(r),!t.categories||t.categories.includes("built-in"))&&(this.processLexingErrors(n,i,t),t.stopAfterLexingErrors&&i.some(e=>{var t;return(null==(t=e.data)?void 0:t.code)===tC.LexingError})||(this.processParsingErrors(n,i,t),t.stopAfterParsingErrors&&i.some(e=>{var t;return(null==(t=e.data)?void 0:t.code)===tC.ParsingError}))||(this.processLinkingErrors(e,i,t),t.stopAfterLinkingErrors&&i.some(e=>{var t;return(null==(t=e.data)?void 0:t.code)===tC.LinkingError}))))return i;try{i.push(...await this.validateAst(n.value,t,r))}catch(e){if(e===rV)throw e;console.error("An error occurred during validation:",e)}return await rK(r),i}processLexingErrors(e,t,r){var n,i,a;for(let r of[...e.lexerErrors,...null!=(i=null==(n=e.lexerReport)?void 0:n.diagnostics)?i:[]]){let e=null!=(a=r.severity)?a:"error",n={severity:nu(e),range:{start:{line:r.line-1,character:r.column-1},end:{line:r.line-1,character:r.column+r.length-1}},message:r.message,data:function(e){switch(e){case"error":return{code:tC.LexingError};case"warning":return{code:tC.LexingWarning};case"info":return{code:tC.LexingInfo};case"hint":return{code:tC.LexingHint};default:throw Error("Invalid diagnostic severity: "+e)}}(e),source:this.getSource()};t.push(n)}}processParsingErrors(e,t,r){for(let r of e.parserErrors){let e;if(isNaN(r.token.startOffset)){if("previousToken"in r){let t=r.previousToken;if(isNaN(t.startOffset)){let t={line:0,character:0};e={start:t,end:t}}else{let r={line:t.endLine-1,character:t.endColumn};e={start:r,end:r}}}}else e=(0,tw.sp)(r.token);if(e){let n={severity:nu("error"),range:e,message:r.message,data:{code:tC.ParsingError},source:this.getSource()};t.push(n)}}}processLinkingErrors(e,t,r){for(let r of e.references){let e=r.error;if(e){let r={node:e.container,property:e.property,index:e.index,data:{code:tC.LinkingError,containerType:e.container.$type,property:e.property,refText:e.reference.$refText}};t.push(this.toDiagnostic("error",e.message,r))}}}async validateAst(e,t,r=rU.CancellationToken.None){let n=[],i=(e,t,r)=>{n.push(this.toDiagnostic(e,t,r))};return await this.validateAstBefore(e,t,i,r),await this.validateAstNodes(e,t,i,r),await this.validateAstAfter(e,t,i,r),n}async validateAstBefore(e,t,r,n=rU.CancellationToken.None){var i;for(let a of this.validationRegistry.checksBefore)await rK(n),await a(e,r,null!=(i=t.categories)?i:[],n)}async validateAstNodes(e,t,r,n=rU.CancellationToken.None){await Promise.all((0,rc.Zc)(e).map(async e=>{for(let i of(await rK(n),this.validationRegistry.getChecks(e.$type,t.categories)))await i(e,r,n)}))}async validateAstAfter(e,t,r,n=rU.CancellationToken.None){var i;for(let a of this.validationRegistry.checksAfter)await rK(n),await a(e,r,null!=(i=t.categories)?i:[],n)}toDiagnostic(e,t,r){var n;let i;return{message:t,range:(n=r).range?n.range:("string"==typeof n.property?i=(0,tL.vb)(n.node.$cstNode,n.property,n.index):"string"==typeof n.keyword&&(i=(0,tL.lA)(n.node.$cstNode,n.keyword,n.index)),null!=i||(i=n.node.$cstNode),i)?i.range:{start:{line:0,character:0},end:{line:0,character:0}},severity:nu(e),code:r.code,codeDescription:r.codeDescription,tags:r.tags,relatedInformation:r.relatedInformation,data:r.data,source:this.getSource()}}getSource(){return this.metadata.languageId}}function nu(e){switch(e){case"error":return 1;case"warning":return 2;case"info":return 3;case"hint":return 4;default:throw Error("Invalid diagnostic severity: "+e)}}(eo=tC||(tC={})).LexingError="lexing-error",eo.LexingWarning="lexing-warning",eo.LexingInfo="lexing-info",eo.LexingHint="lexing-hint",eo.ParsingError="parsing-error",eo.LinkingError="linking-error";class nc{constructor(e){this.astNodeLocator=e.workspace.AstNodeLocator,this.nameProvider=e.references.NameProvider}createDescription(e,t,r){let n,i=null!=r?r:(0,rc.Me)(e);null!=t||(t=this.nameProvider.getName(e));let a=this.astNodeLocator.getAstNodePath(e);if(!t)throw Error(`Node at path ${a} has no name.`);let s=()=>{var t;return null!=n?n:n=(0,tw.yn)(null!=(t=this.nameProvider.getNameNode(e))?t:e.$cstNode)};return{node:e,name:t,get nameSegment(){return s()},selectionSegment:(0,tw.yn)(e.$cstNode),type:e.$type,documentUri:i.uri,path:a}}}class nd{constructor(e){this.nodeLocator=e.workspace.AstNodeLocator}async createDescriptions(e,t=rU.CancellationToken.None){let r=[],n=e.parseResult.value;for(let e of(0,rc.Zc)(n))await rK(t),(0,rc.fy)(e).filter(e=>!(0,rG.et)(e)).forEach(e=>{let t=this.createDescription(e);t&&r.push(t)});return r}createDescription(e){let t=e.reference.$nodeDescription,r=e.reference.$refNode;if(!t||!r)return;let n=(0,rc.Me)(e.container).uri;return{sourceUri:n,sourcePath:this.nodeLocator.getAstNodePath(e.container),targetUri:t.documentUri,targetPath:t.path,segment:(0,tw.yn)(r),local:tI.equals(t.documentUri,n)}}}class nh{constructor(){this.segmentSeparator="/",this.indexSeparator="@"}getAstNodePath(e){if(e.$container){let t=this.getAstNodePath(e.$container),r=this.getPathSegment(e);return t+this.segmentSeparator+r}return""}getPathSegment({$containerProperty:e,$containerIndex:t}){if(!e)throw Error("Missing '$containerProperty' in AST node.");return void 0!==t?e+this.indexSeparator+t:e}getAstNode(e,t){return t.split(this.segmentSeparator).reduce((e,t)=>{if(!e||0===t.length)return e;let r=t.indexOf(this.indexSeparator);if(r>0){let n=t.substring(0,r),i=parseInt(t.substring(r+1)),a=e[n];return null==a?void 0:a[i]}return e[t]},e)}}var nf=r(38711);class np{constructor(e){this._ready=new rj,this.settings={},this.workspaceConfig=!1,this.onConfigurationSectionUpdateEmitter=new nf.Emitter,this.serviceRegistry=e.ServiceRegistry}get ready(){return this._ready.promise}initialize(e){var t,r;this.workspaceConfig=null!=(r=null==(t=e.capabilities.workspace)?void 0:t.configuration)&&r}async initialized(e){if(this.workspaceConfig){if(e.register){let t=this.serviceRegistry.all;e.register({section:t.map(e=>this.toSectionName(e.LanguageMetaData.languageId))})}if(e.fetchConfiguration){let t=this.serviceRegistry.all.map(e=>({section:this.toSectionName(e.LanguageMetaData.languageId)})),r=await e.fetchConfiguration(t);t.forEach((e,t)=>{this.updateSectionConfiguration(e.section,r[t])})}}this._ready.resolve()}updateConfiguration(e){e.settings&&Object.keys(e.settings).forEach(t=>{let r=e.settings[t];this.updateSectionConfiguration(t,r),this.onConfigurationSectionUpdateEmitter.fire({section:t,configuration:r})})}updateSectionConfiguration(e,t){this.settings[e]=t}async getConfiguration(e,t){await this.ready;let r=this.toSectionName(e);if(this.settings[r])return this.settings[r][t]}toSectionName(e){return`${e}`}get onConfigurationSectionUpdate(){return this.onConfigurationSectionUpdateEmitter.event}}(tN||(tN={})).create=function(e){return{dispose:async()=>await e()}};class nm{constructor(e){this.updateBuildOptions={validation:{categories:["built-in","fast"]}},this.updateListeners=[],this.buildPhaseListeners=new r9,this.documentPhaseListeners=new r9,this.buildState=new Map,this.documentBuildWaiters=new Map,this.currentState=tx.Changed,this.langiumDocuments=e.workspace.LangiumDocuments,this.langiumDocumentFactory=e.workspace.LangiumDocumentFactory,this.textDocuments=e.workspace.TextDocuments,this.indexManager=e.workspace.IndexManager,this.serviceRegistry=e.ServiceRegistry}async build(e,t={},r=rU.CancellationToken.None){var n,i;for(let r of e){let e=r.uri.toString();if(r.state===tx.Validated){if("boolean"==typeof t.validation&&t.validation)r.state=tx.IndexedReferences,r.diagnostics=void 0,this.buildState.delete(e);else if("object"==typeof t.validation){let a=this.buildState.get(e),s=null==(n=null==a?void 0:a.result)?void 0:n.validationChecks;if(s){let n=(null!=(i=t.validation.categories)?i:tS.all).filter(e=>!s.includes(e));n.length>0&&(this.buildState.set(e,{completed:!1,options:{validation:Object.assign(Object.assign({},t.validation),{categories:n})},result:a.result}),r.state=tx.IndexedReferences)}}}else this.buildState.delete(e)}this.currentState=tx.Changed,await this.emitUpdate(e.map(e=>e.uri),[]),await this.buildDocuments(e,t,r)}async update(e,t,r=rU.CancellationToken.None){for(let e of(this.currentState=tx.Changed,t))this.langiumDocuments.deleteDocument(e),this.buildState.delete(e.toString()),this.indexManager.remove(e);for(let t of e){if(!this.langiumDocuments.invalidateDocument(t)){let e=this.langiumDocumentFactory.fromModel({$type:"INVALID"},t);e.state=tx.Changed,this.langiumDocuments.addDocument(e)}this.buildState.delete(t.toString())}let n=(0,rL.Vw)(e).concat(t).map(e=>e.toString()).toSet();this.langiumDocuments.all.filter(e=>!n.has(e.uri.toString())&&this.shouldRelink(e,n)).forEach(e=>{this.serviceRegistry.getServices(e.uri).references.Linker.unlink(e),e.state=Math.min(e.state,tx.ComputedScopes),e.diagnostics=void 0}),await this.emitUpdate(e,t),await rK(r);let i=this.sortDocuments(this.langiumDocuments.all.filter(e=>{var t;return e.state<tx.Linked||!(null==(t=this.buildState.get(e.uri.toString()))?void 0:t.completed)}).toArray());await this.buildDocuments(i,this.updateBuildOptions,r)}async emitUpdate(e,t){await Promise.all(this.updateListeners.map(r=>r(e,t)))}sortDocuments(e){let t=0,r=e.length-1;for(;t<r;){for(;t<e.length&&this.hasTextDocument(e[t]);)t++;for(;r>=0&&!this.hasTextDocument(e[r]);)r--;t<r&&([e[t],e[r]]=[e[r],e[t]])}return e}hasTextDocument(e){var t;return!!(null==(t=this.textDocuments)?void 0:t.get(e.uri))}shouldRelink(e,t){return!!e.references.some(e=>void 0!==e.error)||this.indexManager.isAffected(e,t)}onUpdate(e){return this.updateListeners.push(e),tN.create(()=>{let t=this.updateListeners.indexOf(e);t>=0&&this.updateListeners.splice(t,1)})}async buildDocuments(e,t,r){this.prepareBuild(e,t),await this.runCancelable(e,tx.Parsed,r,e=>this.langiumDocumentFactory.update(e,r)),await this.runCancelable(e,tx.IndexedContent,r,e=>this.indexManager.updateContent(e,r)),await this.runCancelable(e,tx.ComputedScopes,r,async e=>{let t=this.serviceRegistry.getServices(e.uri).references.ScopeComputation;e.precomputedScopes=await t.computeLocalScopes(e,r)}),await this.runCancelable(e,tx.Linked,r,e=>this.serviceRegistry.getServices(e.uri).references.Linker.link(e,r)),await this.runCancelable(e,tx.IndexedReferences,r,e=>this.indexManager.updateReferences(e,r));let n=e.filter(e=>this.shouldValidate(e));for(let t of(await this.runCancelable(n,tx.Validated,r,e=>this.validate(e,r)),e)){let e=this.buildState.get(t.uri.toString());e&&(e.completed=!0)}}prepareBuild(e,t){for(let r of e){let e=r.uri.toString(),n=this.buildState.get(e);(!n||n.completed)&&this.buildState.set(e,{completed:!1,options:t,result:null==n?void 0:n.result})}}async runCancelable(e,t,r,n){for(let i of e.filter(e=>e.state<t))await rK(r),await n(i),i.state=t,await this.notifyDocumentPhase(i,t,r);let i=e.filter(e=>e.state===t);await this.notifyBuildPhase(i,t,r),this.currentState=t}onBuildPhase(e,t){return this.buildPhaseListeners.add(e,t),tN.create(()=>{this.buildPhaseListeners.delete(e,t)})}onDocumentPhase(e,t){return this.documentPhaseListeners.add(e,t),tN.create(()=>{this.documentPhaseListeners.delete(e,t)})}waitUntil(e,t,r){let n;if(t&&"path"in t?n=t:r=t,null!=r||(r=rU.CancellationToken.None),n){let t=this.langiumDocuments.getDocument(n);if(t&&t.state>e)return Promise.resolve(n)}return this.currentState>=e?Promise.resolve(void 0):r.isCancellationRequested?Promise.reject(rV):new Promise((t,i)=>{let a=this.onBuildPhase(e,()=>{if(a.dispose(),s.dispose(),n){let e=this.langiumDocuments.getDocument(n);t(null==e?void 0:e.uri)}else t(void 0)}),s=r.onCancellationRequested(()=>{a.dispose(),s.dispose(),i(rV)})})}async notifyDocumentPhase(e,t,r){for(let n of this.documentPhaseListeners.get(t).slice())try{await n(e,r)}catch(e){if(e!==rV)throw e}}async notifyBuildPhase(e,t,r){if(0!==e.length)for(let n of this.buildPhaseListeners.get(t).slice())await rK(r),await n(e,r)}shouldValidate(e){return!!this.getBuildOptions(e).validation}async validate(e,t){var r,n;let i=this.serviceRegistry.getServices(e.uri).validation.DocumentValidator,a=this.getBuildOptions(e).validation,s="object"==typeof a?a:void 0,o=await i.validateDocument(e,s,t);e.diagnostics?e.diagnostics.push(...o):e.diagnostics=o;let l=this.buildState.get(e.uri.toString());if(l){null!=l.result||(l.result={});let e=null!=(n=null==s?void 0:s.categories)?n:tS.all;l.result.validationChecks?l.result.validationChecks.push(...e):l.result.validationChecks=[...e]}}getBuildOptions(e){var t,r;return null!=(r=null==(t=this.buildState.get(e.uri.toString()))?void 0:t.options)?r:{}}}class ng{constructor(e){this.symbolIndex=new Map,this.symbolByTypeIndex=new nt,this.referenceIndex=new Map,this.documents=e.workspace.LangiumDocuments,this.serviceRegistry=e.ServiceRegistry,this.astReflection=e.AstReflection}findAllReferences(e,t){let r=(0,rc.Me)(e).uri,n=[];return this.referenceIndex.forEach(e=>{e.forEach(e=>{tI.equals(e.targetUri,r)&&e.targetPath===t&&n.push(e)})}),(0,rL.Vw)(n)}allElements(e,t){let r=(0,rL.Vw)(this.symbolIndex.keys());return t&&(r=r.filter(e=>!t||t.has(e))),r.map(t=>this.getFileDescriptions(t,e)).flat()}getFileDescriptions(e,t){var r;return t?this.symbolByTypeIndex.get(e,t,()=>{var r;return(null!=(r=this.symbolIndex.get(e))?r:[]).filter(e=>this.astReflection.isSubtype(e.type,t))}):null!=(r=this.symbolIndex.get(e))?r:[]}remove(e){let t=e.toString();this.symbolIndex.delete(t),this.symbolByTypeIndex.clear(t),this.referenceIndex.delete(t)}async updateContent(e,t=rU.CancellationToken.None){let r=this.serviceRegistry.getServices(e.uri),n=await r.references.ScopeComputation.computeExports(e,t),i=e.uri.toString();this.symbolIndex.set(i,n),this.symbolByTypeIndex.clear(i)}async updateReferences(e,t=rU.CancellationToken.None){let r=this.serviceRegistry.getServices(e.uri),n=await r.workspace.ReferenceDescriptionProvider.createDescriptions(e,t);this.referenceIndex.set(e.uri.toString(),n)}isAffected(e,t){let r=this.referenceIndex.get(e.uri.toString());return!!r&&r.some(e=>!e.local&&t.has(e.targetUri.toString()))}}class ny{constructor(e){this.initialBuildOptions={},this._ready=new rj,this.serviceRegistry=e.ServiceRegistry,this.langiumDocuments=e.workspace.LangiumDocuments,this.documentBuilder=e.workspace.DocumentBuilder,this.fileSystemProvider=e.workspace.FileSystemProvider,this.mutex=e.workspace.WorkspaceLock}get ready(){return this._ready.promise}get workspaceFolders(){return this.folders}initialize(e){var t;this.folders=null!=(t=e.workspaceFolders)?t:void 0}initialized(e){return this.mutex.write(e=>{var t;return this.initializeWorkspace(null!=(t=this.folders)?t:[],e)})}async initializeWorkspace(e,t=rU.CancellationToken.None){let r=await this.performStartup(e);await rK(t),await this.documentBuilder.build(r,this.initialBuildOptions,t)}async performStartup(e){let t=this.serviceRegistry.all.flatMap(e=>e.LanguageMetaData.fileExtensions),r=[],n=e=>{r.push(e),this.langiumDocuments.hasDocument(e.uri)||this.langiumDocuments.addDocument(e)};return await this.loadAdditionalDocuments(e,n),await Promise.all(e.map(e=>[e,this.getRootFolder(e)]).map(async e=>this.traverseFolder(...e,t,n))),this._ready.resolve(),r}loadAdditionalDocuments(e,t){return Promise.resolve()}getRootFolder(e){return rX.o.parse(e.uri)}async traverseFolder(e,t,r,n){let i=await this.fileSystemProvider.readDirectory(t);await Promise.all(i.map(async t=>{this.includeEntry(e,t,r)&&(t.isDirectory?await this.traverseFolder(e,t.uri,r,n):t.isFile&&n(await this.langiumDocuments.getOrCreateDocument(t.uri)))}))}includeEntry(e,t,r){let n=tI.basename(t.uri);if(n.startsWith("."))return!1;if(t.isDirectory)return"node_modules"!==n&&"out"!==n;if(t.isFile){let e=tI.extname(t.uri);return r.includes(e)}return!1}}class nT{buildUnexpectedCharactersMessage(e,t,r,n,i){return t_.ZW.buildUnexpectedCharactersMessage(e,t,r,n,i)}buildUnableToPopLexerModeMessage(e){return t_.ZW.buildUnableToPopLexerModeMessage(e)}}let nv={mode:"full"};class nR{constructor(e){this.errorMessageProvider=e.parser.LexerErrorMessageProvider,this.tokenBuilder=e.parser.TokenBuilder;let t=this.tokenBuilder.buildTokens(e.Grammar,{caseInsensitive:e.LanguageMetaData.caseInsensitive});this.tokenTypes=this.toTokenTypeDictionary(t);let r=nA(t)?Object.values(t):t,n="production"===e.LanguageMetaData.mode;this.chevrotainLexer=new t_.hW(r,{positionTracking:"full",skipValidations:n,errorMessageProvider:this.errorMessageProvider})}get definition(){return this.tokenTypes}tokenize(e,t=nv){var r,n,i;let a=this.chevrotainLexer.tokenize(e);return{tokens:a.tokens,errors:a.errors,hidden:null!=(r=a.groups.hidden)?r:[],report:null==(i=(n=this.tokenBuilder).flushLexingReport)?void 0:i.call(n,e)}}toTokenTypeDictionary(e){if(nA(e))return e;let t=nE(e)?Object.values(e.modes).flat():e,r={};return t.forEach(e=>r[e.name]=e),r}}function nE(e){return e&&"modes"in e&&"defaultMode"in e}function nA(e){return!(Array.isArray(e)&&(0===e.length||"name"in e[0]))&&!nE(e)}function nk(e){let t="";return("string"==typeof e?e:e.text).split(tb.K0)}let n$=/\s*(@([\p{L}][\p{L}\p{N}]*)?)/uy,nx=/\{(@[\p{L}][\p{L}\p{N}]*)(\s*)([^\r\n}]+)?\}/gu,nI=/\S/,nS=/\s*$/;function nC(e,t){let r=e.substring(t).match(nI);return r?t+r.index:e.length}function nN(e){let t=e.tokens[e.index],r=t,n=t,i=[];for(;t&&"break"!==t.type&&"tag"!==t.type;){var a;i.push("inline-tag"===(a=e).tokens[a.index].type?nw(a,!0):nL(a)),n=t,t=e.tokens[e.index]}return new nM(i,ef.create(r.range.start,n.range.end))}function nw(e,t){let r=e.tokens[e.index++],n=r.content.substring(1),i=e.tokens[e.index];if((null==i?void 0:i.type)==="text")if(t){let i=nL(e);return new nP(n,new nM([i],i.range),t,ef.create(r.range.start,i.range.end))}else{let i=nN(e);return new nP(n,i,t,ef.create(r.range.start,i.range.end))}{let e=r.range;return new nP(n,new nM([],e),t,e)}}function nL(e){let t=e.tokens[e.index++];return new nD(t.content,t.range)}function nb(e){if(!e)return nb({start:"/**",end:"*/",line:"*"});let{start:t,end:r,line:n}=e;return{start:nO(t,!0),end:nO(r,!1),line:nO(n,!0)}}function nO(e,t){if("string"!=typeof e&&"object"!=typeof e)return e;{let r="string"==typeof e?(0,tb.hr)(e):e.source;return t?RegExp(`^\\s*${r}`):RegExp(`\\s*${r}\\s*$`)}}class n_{constructor(e,t){this.elements=e,this.range=t}getTag(e){return this.getAllTags().find(t=>t.name===e)}getTags(e){return this.getAllTags().filter(t=>t.name===e)}getAllTags(){return this.elements.filter(e=>"name"in e)}toString(){let e="";for(let t of this.elements)if(0===e.length)e=t.toString();else{let r=t.toString();e+=nZ(e)+r}return e.trim()}toMarkdown(e){let t="";for(let r of this.elements)if(0===t.length)t=r.toMarkdown(e);else{let n=r.toMarkdown(e);t+=nZ(t)+n}return t.trim()}}class nP{constructor(e,t,r,n){this.name=e,this.content=t,this.inline=r,this.range=n}toString(){let e=`@${this.name}`,t=this.content.toString();return(1===this.content.inlines.length?e=`${e} ${t}`:this.content.inlines.length>1&&(e=`${e}
${t}`),this.inline)?`{${e}}`:e}toMarkdown(e){var t,r;return null!=(r=null==(t=null==e?void 0:e.renderTag)?void 0:t.call(e,this))?r:this.toMarkdownDefault(e)}toMarkdownDefault(e){let t=this.content.toMarkdown(e);if(this.inline){let r=function(e,t,r){var n,i;if("linkplain"===e||"linkcode"===e||"link"===e){let a=t.indexOf(" "),s=t;if(a>0){let e=nC(t,a);s=t.substring(e),t=t.substring(0,a)}return("linkcode"===e||"link"===e&&"code"===r.link)&&(s=`\`${s}\``),null!=(i=null==(n=r.renderLink)?void 0:n.call(r,t,s))?i:function(e,t){try{return rX.o.parse(e,!0),`[${t}](${e})`}catch(t){return e}}(t,s)}}(this.name,t,null!=e?e:{});if("string"==typeof r)return r}let r="";(null==e?void 0:e.tag)==="italic"||(null==e?void 0:e.tag)===void 0?r="*":(null==e?void 0:e.tag)==="bold"?r="**":(null==e?void 0:e.tag)==="bold-italic"&&(r="***");let n=`${r}@${this.name}${r}`;return(1===this.content.inlines.length?n=`${n} — ${t}`:this.content.inlines.length>1&&(n=`${n}
${t}`),this.inline)?`{${n}}`:n}}class nM{constructor(e,t){this.inlines=e,this.range=t}toString(){let e="";for(let t=0;t<this.inlines.length;t++){let r=this.inlines[t],n=this.inlines[t+1];e+=r.toString(),n&&n.range.start.line>r.range.start.line&&(e+="\n")}return e}toMarkdown(e){let t="";for(let r=0;r<this.inlines.length;r++){let n=this.inlines[r],i=this.inlines[r+1];t+=n.toMarkdown(e),i&&i.range.start.line>n.range.start.line&&(t+="\n")}return t}}class nD{constructor(e,t){this.text=e,this.range=t}toString(){return this.text}toMarkdown(){return this.text}}function nZ(e){return e.endsWith("\n")?"\n":"\n\n"}class nF{constructor(e){this.indexManager=e.shared.workspace.IndexManager,this.commentProvider=e.documentation.CommentProvider}getDocumentation(e){let t=this.commentProvider.getComment(e);if(t&&function(e,t){let r=nb(void 0),n=nk(e);if(0===n.length)return!1;let i=n[0],a=n[n.length-1],s=r.start,o=r.end;return!!(null==s?void 0:s.exec(i))&&!!(null==o?void 0:o.exec(a))}(t)){var r,n,i;let a,s;return("string"==typeof(r=t)?(s=void 0,a=void 0):(s=r.range.start,a=void 0),s||(s=eh.create(0,0)),function(e){var t,r,n,i;let a=eh.create(e.position.line,e.position.character);if(0===e.tokens.length)return new n_([],ef.create(a,a));let s=[];for(;e.index<e.tokens.length;){let t=function(e,t){let r=e.tokens[e.index];return"tag"===r.type?nw(e,!1):"text"===r.type||"inline-tag"===r.type?nN(e):(function(e,t){if(t){let r=new nD("",e.range);"inlines"in t?t.inlines.push(r):t.content.inlines.push(r)}}(r,t),void e.index++)}(e,s[s.length-1]);t&&s.push(t)}let o=null!=(r=null==(t=s[0])?void 0:t.range.start)?r:a,l=null!=(i=null==(n=s[s.length-1])?void 0:n.range.end)?i:a;return new n_(s,ef.create(o,l))}({index:0,tokens:function(e){var t,r,n;let i=[],a=e.position.line,s=e.position.character;for(let o=0;o<e.lines.length;o++){let l=0===o,u=o===e.lines.length-1,c=e.lines[o],d=0;if(l&&e.options.start){let r=null==(t=e.options.start)?void 0:t.exec(c);r&&(d=r.index+r[0].length)}else{let t=null==(r=e.options.line)?void 0:r.exec(c);t&&(d=t.index+t[0].length)}if(u){let t=null==(n=e.options.end)?void 0:n.exec(c);t&&(c=c.substring(0,t.index))}if(nC(c=c.substring(0,function(e){let t=e.match(nS);if(t&&"number"==typeof t.index)return t.index}(c)),d)>=c.length){if(i.length>0){let e=eh.create(a,s);i.push({type:"break",content:"",range:ef.create(e,e)})}}else{n$.lastIndex=d;let e=n$.exec(c);if(e){let t=e[0],r=e[1],n=eh.create(a,s+d),o=eh.create(a,s+d+t.length);i.push({type:"tag",content:r,range:ef.create(n,o)}),d+=t.length,d=nC(c,d)}if(d<c.length){let e=c.substring(d),t=Array.from(e.matchAll(nx));i.push(...function(e,t,r,n){let i=[];if(0===e.length){let e=eh.create(r,n),a=eh.create(r,n+t.length);i.push({type:"text",content:t,range:ef.create(e,a)})}else{let a=0;for(let s of e){let e=s.index,o=t.substring(a,e);o.length>0&&i.push({type:"text",content:t.substring(a,e),range:ef.create(eh.create(r,a+n),eh.create(r,e+n))});let l=o.length+1,u=s[1];if(i.push({type:"inline-tag",content:u,range:ef.create(eh.create(r,a+l+n),eh.create(r,a+l+u.length+n))}),l+=u.length,4===s.length){l+=s[2].length;let e=s[3];i.push({type:"text",content:e,range:ef.create(eh.create(r,a+l+n),eh.create(r,a+l+e.length+n))})}else i.push({type:"text",content:"",range:ef.create(eh.create(r,a+l+n),eh.create(r,a+l+n))});a=e+s[0].length}let s=t.substring(a);s.length>0&&i.push({type:"text",content:s,range:ef.create(eh.create(r,a+n),eh.create(r,a+n+s.length))})}return i}(t,e,a,s+d))}}a++,s=0}return i.length>0&&"break"===i[i.length-1].type?i.slice(0,-1):i}({lines:nk(r),position:s,options:nb(a)}),position:s})).toMarkdown({renderLink:(t,r)=>this.documentationLinkRenderer(e,t,r),renderTag:t=>this.documentationTagRenderer(e,t)})}}documentationLinkRenderer(e,t,r){var n;let i=null!=(n=this.findNameInPrecomputedScopes(e,t))?n:this.findNameInGlobalScope(e,t);if(i&&i.nameSegment){let e=i.nameSegment.range.start.line+1,t=i.nameSegment.range.start.character+1,n=i.documentUri.with({fragment:`L${e},${t}`});return`[${r}](${n.toString()})`}}documentationTagRenderer(e,t){}findNameInPrecomputedScopes(e,t){let r=(0,rc.Me)(e).precomputedScopes;if(!r)return;let n=e;do{let e=r.get(n).find(e=>e.name===t);if(e)return e;n=n.$container}while(n)}findNameInGlobalScope(e,t){return this.indexManager.allElements().find(e=>e.name===t)}}class nU{constructor(e){this.grammarConfig=()=>e.parser.GrammarConfig}getComment(e){var t;return"string"==typeof e.$comment?e.$comment:null==(t=(0,tw.LK)(e.$cstNode,this.grammarConfig().multilineCommentRules))?void 0:t.text}}class nG{constructor(e){this.syncParser=e.parser.LangiumParser}parse(e,t){return Promise.resolve(this.syncParser.parse(e))}}class nB{constructor(){this.previousTokenSource=new rU.CancellationTokenSource,this.writeQueue=[],this.readQueue=[],this.done=!0}write(e){this.cancelWrite();let t=(rB=performance.now(),new rU.CancellationTokenSource);return this.previousTokenSource=t,this.enqueue(this.writeQueue,e,t.token)}read(e){return this.enqueue(this.readQueue,e)}enqueue(e,t,r=rU.CancellationToken.None){let n=new rj;return e.push({action:t,deferred:n,cancellationToken:r}),this.performNextOperation(),n.promise}async performNextOperation(){if(!this.done)return;let e=[];if(this.writeQueue.length>0)e.push(this.writeQueue.shift());else{if(!(this.readQueue.length>0))return;e.push(...this.readQueue.splice(0,this.readQueue.length))}this.done=!1,await Promise.all(e.map(async({action:e,deferred:t,cancellationToken:r})=>{try{let n=await Promise.resolve().then(()=>e(r));t.resolve(n)}catch(e){e===rV?t.resolve(void 0):t.reject(e)}})),this.done=!0,this.performNextOperation()}cancelWrite(){this.previousTokenSource.cancel()}}class nV{constructor(e){this.grammarElementIdMap=new r3,this.tokenTypeIdMap=new r3,this.grammar=e.Grammar,this.lexer=e.parser.Lexer,this.linker=e.references.Linker}dehydrate(e){return{lexerErrors:e.lexerErrors,lexerReport:e.lexerReport?this.dehydrateLexerReport(e.lexerReport):void 0,parserErrors:e.parserErrors.map(e=>Object.assign(Object.assign({},e),{message:e.message})),value:this.dehydrateAstNode(e.value,this.createDehyrationContext(e.value))}}dehydrateLexerReport(e){return e}createDehyrationContext(e){let t=new Map,r=new Map;for(let r of(0,rc.Zc)(e))t.set(r,{});if(e.$cstNode)for(let t of(0,tw._t)(e.$cstNode))r.set(t,{});return{astNodes:t,cstNodes:r}}dehydrateAstNode(e,t){let r=t.astNodes.get(e);for(let[n,i]of(r.$type=e.$type,r.$containerIndex=e.$containerIndex,r.$containerProperty=e.$containerProperty,void 0!==e.$cstNode&&(r.$cstNode=this.dehydrateCstNode(e.$cstNode,t)),Object.entries(e)))if(!n.startsWith("$"))if(Array.isArray(i)){let e=[];for(let a of(r[n]=e,i))(0,rG.xA)(a)?e.push(this.dehydrateAstNode(a,t)):(0,rG.Yk)(a)?e.push(this.dehydrateReference(a,t)):e.push(a)}else(0,rG.xA)(i)?r[n]=this.dehydrateAstNode(i,t):(0,rG.Yk)(i)?r[n]=this.dehydrateReference(i,t):void 0!==i&&(r[n]=i);return r}dehydrateReference(e,t){let r={};return r.$refText=e.$refText,e.$refNode&&(r.$refNode=t.cstNodes.get(e.$refNode)),r}dehydrateCstNode(e,t){let r=t.cstNodes.get(e);return(0,rG.U8)(e)?r.fullText=e.fullText:r.grammarSource=this.getGrammarElementId(e.grammarSource),r.hidden=e.hidden,r.astNode=t.astNodes.get(e.astNode),(0,rG.al)(e)?r.content=e.content.map(e=>this.dehydrateCstNode(e,t)):(0,rG.dm)(e)&&(r.tokenType=e.tokenType.name,r.offset=e.offset,r.length=e.length,r.startLine=e.range.start.line,r.startColumn=e.range.start.character,r.endLine=e.range.end.line,r.endColumn=e.range.end.character),r}hydrate(e){let t=e.value,r=this.createHydrationContext(t);return"$cstNode"in t&&this.hydrateCstNode(t.$cstNode,r),{lexerErrors:e.lexerErrors,lexerReport:e.lexerReport,parserErrors:e.parserErrors,value:this.hydrateAstNode(t,r)}}createHydrationContext(e){let t,r=new Map,n=new Map;for(let t of(0,rc.Zc)(e))r.set(t,{});if(e.$cstNode)for(let r of(0,tw._t)(e.$cstNode)){let e;"fullText"in r?t=e=new rv(r.fullText):"content"in r?e=new ry:"tokenType"in r&&(e=this.hydrateCstLeafNode(r)),e&&(n.set(r,e),e.root=t)}return{astNodes:r,cstNodes:n}}hydrateAstNode(e,t){let r=t.astNodes.get(e);for(let[n,i]of(r.$type=e.$type,r.$containerIndex=e.$containerIndex,r.$containerProperty=e.$containerProperty,e.$cstNode&&(r.$cstNode=t.cstNodes.get(e.$cstNode)),Object.entries(e)))if(!n.startsWith("$"))if(Array.isArray(i)){let e=[];for(let a of(r[n]=e,i))(0,rG.xA)(a)?e.push(this.setParent(this.hydrateAstNode(a,t),r)):(0,rG.Yk)(a)?e.push(this.hydrateReference(a,r,n,t)):e.push(a)}else(0,rG.xA)(i)?r[n]=this.setParent(this.hydrateAstNode(i,t),r):(0,rG.Yk)(i)?r[n]=this.hydrateReference(i,r,n,t):void 0!==i&&(r[n]=i);return r}setParent(e,t){return e.$container=t,e}hydrateReference(e,t,r,n){return this.linker.buildReference(t,r,n.cstNodes.get(e.$refNode),e.$refText)}hydrateCstNode(e,t,r=0){let n=t.cstNodes.get(e);if("number"==typeof e.grammarSource&&(n.grammarSource=this.getGrammarElement(e.grammarSource)),n.astNode=t.astNodes.get(e.astNode),(0,rG.al)(n))for(let i of e.content){let e=this.hydrateCstNode(i,t,r++);n.content.push(e)}return n}hydrateCstLeafNode(e){let t=this.getTokenType(e.tokenType),r=e.offset,n=e.length,i=e.startLine,a=e.startColumn,s=e.endLine;return new rg(r,n,{start:{line:i,character:a},end:{line:s,character:e.endColumn}},t,e.hidden)}getTokenType(e){return this.lexer.definition[e]}getGrammarElementId(e){if(e)return 0===this.grammarElementIdMap.size&&this.createGrammarElementIdMap(),this.grammarElementIdMap.get(e)}getGrammarElement(e){return 0===this.grammarElementIdMap.size&&this.createGrammarElementIdMap(),this.grammarElementIdMap.getKey(e)}createGrammarElementIdMap(){let e=0;for(let t of(0,rc.Zc)(this.grammar))(0,tO.zJ)(t)&&this.grammarElementIdMap.set(t,e++)}}function nK(e){return{documentation:{CommentProvider:e=>new nU(e),DocumentationProvider:e=>new nF(e)},parser:{AsyncParser:e=>new nG(e),GrammarConfig:e=>(function(e){let t=[];for(let r of e.Grammar.rules)(0,tO.MS)(r)&&(0,tL.md)(r)&&(0,tb.Rn)((0,tL.s1)(r))&&t.push(r.name);return{multilineCommentRules:t,nameRegexp:tw.uz}})(e),LangiumParser:e=>(function(e){let t=function(e){let t=e.Grammar,r=e.parser.Lexer;return rb(t,new r$(e),r.definition)}(e);return t.finalize(),t})(e),CompletionParser:e=>(function(e){let t=e.Grammar,r=e.parser.Lexer,n=new rS(e);return rb(t,n,r.definition),n.finalize(),n})(e),ValueConverter:()=>new rF.t,TokenBuilder:()=>new rZ.P,Lexer:e=>new nR(e),ParserErrorMessageProvider:()=>new rI,LexerErrorMessageProvider:()=>new nT},workspace:{AstNodeLocator:()=>new nh,AstNodeDescriptionProvider:e=>new nc(e),ReferenceDescriptionProvider:e=>new nd(e)},references:{Linker:e=>new r1(e),NameProvider:()=>new r2,ScopeProvider:e=>new nn(e),ScopeComputation:e=>new r7(e),References:e=>new r6(e)},serializer:{Hydrator:e=>new nV(e),JsonSerializer:e=>new na(e)},validation:{DocumentValidator:e=>new nl(e),ValidationRegistry:e=>new no(e)},shared:()=>e.shared}}function nj(e){return{ServiceRegistry:e=>new ns(e),workspace:{LangiumDocuments:e=>new rJ(e),LangiumDocumentFactory:e=>new rQ(e),DocumentBuilder:e=>new nm(e),IndexManager:e=>new ng(e),WorkspaceManager:e=>new ny(e),FileSystemProvider:t=>e.fileSystemProvider(t),WorkspaceLock:()=>new nB,ConfigurationProvider:e=>new np(e)}}}},29266:function(e,t,r){var n;function i(e,t,r,n,i,a,o,l,c){return s([e,t,r,n,i,a,o,l,c].reduce(u,{}))}r.d(t,{f3:()=>i}),(n||(n={})).merge=(e,t)=>u(u({},e),t);let a=Symbol("isProxy");function s(e,t){let r=new Proxy({},{deleteProperty:()=>!1,set:()=>{throw Error("Cannot set property on injected service container")},get:(n,i)=>i===a||l(n,i,e,t||r),getOwnPropertyDescriptor:(n,i)=>(l(n,i,e,t||r),Object.getOwnPropertyDescriptor(n,i)),has:(t,r)=>r in e,ownKeys:()=>[...Object.getOwnPropertyNames(e)]});return r}let o=Symbol();function l(e,t,r,n){if(t in e){if(e[t]instanceof Error)throw Error("Construction failure. Please make sure that your dependencies are constructable.",{cause:e[t]});if(e[t]===o)throw Error('Cycle detected. Please make "'+String(t)+'" lazy. Visit https://langium.org/docs/reference/configuration-services/#resolving-cyclic-dependencies');return e[t]}if(t in r){let i=r[t];e[t]=o;try{e[t]="function"==typeof i?i(n):s(i,n)}catch(r){throw e[t]=r instanceof Error?r:void 0,r}return e[t]}}function u(e,t){if(t){for(let[r,n]of Object.entries(t))if(void 0!==n){let t=e[r];null!==t&&null!==n&&"object"==typeof t&&"object"==typeof n?e[r]=u(t,n):e[r]=n}}return e}},46117:function(e,t,r){r.d(t,{B7:()=>X,Bf:()=>J,Bi:()=>eu,F8:()=>T,F9:()=>O,Ii:()=>S,Iy:()=>Z,Ki:()=>et,L:()=>p,LG:()=>H,MS:()=>G,MZ:()=>Y,Mp:()=>M,OG:()=>ek,P9:()=>V,QV:()=>$,SV:()=>eI,S_:()=>A,Sg:()=>ed,TB:()=>g,V7:()=>em,W1:()=>eE,X9:()=>ey,gf:()=>ev,p1:()=>eo,qm:()=>ex,rT:()=>en,t3:()=>ef,ty:()=>ea,yW:()=>L,zJ:()=>c});var n=r(89365);let i="AbstractRule",a="AbstractType",s="Condition",o="TypeDefinition",l="ValueLiteral",u="AbstractElement";function c(e){return eS.isInstance(e,u)}let d="ArrayLiteral",h="ArrayType",f="BooleanLiteral";function p(e){return eS.isInstance(e,f)}let m="Conjunction";function g(e){return eS.isInstance(e,m)}let y="Disjunction";function T(e){return eS.isInstance(e,y)}let v="Grammar",R="GrammarImport",E="InferredType";function A(e){return eS.isInstance(e,E)}let k="Interface";function $(e){return eS.isInstance(e,k)}let x="NamedArgument",I="Negation";function S(e){return eS.isInstance(e,I)}let C="NumberLiteral",N="Parameter",w="ParameterReference";function L(e){return eS.isInstance(e,w)}let b="ParserRule";function O(e){return eS.isInstance(e,b)}let _="ReferenceType",P="ReturnType";function M(e){return eS.isInstance(e,P)}let D="SimpleType";function Z(e){return eS.isInstance(e,D)}let F="StringLiteral",U="TerminalRule";function G(e){return eS.isInstance(e,U)}let B="Type";function V(e){return eS.isInstance(e,B)}let K="TypeAttribute",j="UnionType",W="Action";function H(e){return eS.isInstance(e,W)}let z="Alternatives";function Y(e){return eS.isInstance(e,z)}let q="Assignment";function X(e){return eS.isInstance(e,q)}let Q="CharacterRange";function J(e){return eS.isInstance(e,Q)}let ee="CrossReference";function et(e){return eS.isInstance(e,ee)}let er="EndOfFile";function en(e){return eS.isInstance(e,er)}let ei="Group";function ea(e){return eS.isInstance(e,ei)}let es="Keyword";function eo(e){return eS.isInstance(e,es)}let el="NegatedToken";function eu(e){return eS.isInstance(e,el)}let ec="RegexToken";function ed(e){return eS.isInstance(e,ec)}let eh="RuleCall";function ef(e){return eS.isInstance(e,eh)}let ep="TerminalAlternatives";function em(e){return eS.isInstance(e,ep)}let eg="TerminalGroup";function ey(e){return eS.isInstance(e,eg)}let eT="TerminalRuleCall";function ev(e){return eS.isInstance(e,eT)}let eR="UnorderedGroup";function eE(e){return eS.isInstance(e,eR)}let eA="UntilToken";function ek(e){return eS.isInstance(e,eA)}let e$="Wildcard";function ex(e){return eS.isInstance(e,e$)}class eI extends n.$v{getAllTypes(){return[u,i,a,W,z,d,h,q,f,Q,s,m,ee,y,er,v,R,ei,E,k,es,x,el,I,C,N,w,b,_,ec,P,eh,D,F,ep,eg,U,eT,B,K,o,j,eR,eA,l,e$]}computeIsSubtype(e,t){switch(e){case W:case z:case q:case Q:case ee:case er:case ei:case es:case el:case ec:case eh:case ep:case eg:case eT:case eR:case eA:case e$:return this.isSubtype(u,t);case d:case C:case F:return this.isSubtype(l,t);case h:case _:case D:case j:return this.isSubtype(o,t);case f:return this.isSubtype(s,t)||this.isSubtype(l,t);case m:case y:case I:case w:return this.isSubtype(s,t);case E:case k:case B:return this.isSubtype(a,t);case b:return this.isSubtype(i,t)||this.isSubtype(a,t);case U:return this.isSubtype(i,t);default:return!1}}getReferenceType(e){let t=`${e.container.$type}:${e.property}`;switch(t){case"Action:type":case"CrossReference:type":case"Interface:superTypes":case"ParserRule:returnType":case"SimpleType:typeRef":return a;case"Grammar:hiddenTokens":case"ParserRule:hiddenTokens":case"RuleCall:rule":return i;case"Grammar:usedGrammars":return v;case"NamedArgument:parameter":case"ParameterReference:parameter":return N;case"TerminalRuleCall:rule":return U;default:throw Error(`${t} is not a valid reference id.`)}}getTypeMetaData(e){switch(e){case u:return{name:u,properties:[{name:"cardinality"},{name:"lookahead"}]};case d:return{name:d,properties:[{name:"elements",defaultValue:[]}]};case h:return{name:h,properties:[{name:"elementType"}]};case f:return{name:f,properties:[{name:"true",defaultValue:!1}]};case m:return{name:m,properties:[{name:"left"},{name:"right"}]};case y:return{name:y,properties:[{name:"left"},{name:"right"}]};case v:return{name:v,properties:[{name:"definesHiddenTokens",defaultValue:!1},{name:"hiddenTokens",defaultValue:[]},{name:"imports",defaultValue:[]},{name:"interfaces",defaultValue:[]},{name:"isDeclared",defaultValue:!1},{name:"name"},{name:"rules",defaultValue:[]},{name:"types",defaultValue:[]},{name:"usedGrammars",defaultValue:[]}]};case R:return{name:R,properties:[{name:"path"}]};case E:return{name:E,properties:[{name:"name"}]};case k:return{name:k,properties:[{name:"attributes",defaultValue:[]},{name:"name"},{name:"superTypes",defaultValue:[]}]};case x:return{name:x,properties:[{name:"calledByName",defaultValue:!1},{name:"parameter"},{name:"value"}]};case I:return{name:I,properties:[{name:"value"}]};case C:return{name:C,properties:[{name:"value"}]};case N:return{name:N,properties:[{name:"name"}]};case w:return{name:w,properties:[{name:"parameter"}]};case b:return{name:b,properties:[{name:"dataType"},{name:"definesHiddenTokens",defaultValue:!1},{name:"definition"},{name:"entry",defaultValue:!1},{name:"fragment",defaultValue:!1},{name:"hiddenTokens",defaultValue:[]},{name:"inferredType"},{name:"name"},{name:"parameters",defaultValue:[]},{name:"returnType"},{name:"wildcard",defaultValue:!1}]};case _:return{name:_,properties:[{name:"referenceType"}]};case P:return{name:P,properties:[{name:"name"}]};case D:return{name:D,properties:[{name:"primitiveType"},{name:"stringType"},{name:"typeRef"}]};case F:return{name:F,properties:[{name:"value"}]};case U:return{name:U,properties:[{name:"definition"},{name:"fragment",defaultValue:!1},{name:"hidden",defaultValue:!1},{name:"name"},{name:"type"}]};case B:return{name:B,properties:[{name:"name"},{name:"type"}]};case K:return{name:K,properties:[{name:"defaultValue"},{name:"isOptional",defaultValue:!1},{name:"name"},{name:"type"}]};case j:return{name:j,properties:[{name:"types",defaultValue:[]}]};case W:return{name:W,properties:[{name:"cardinality"},{name:"feature"},{name:"inferredType"},{name:"lookahead"},{name:"operator"},{name:"type"}]};case z:return{name:z,properties:[{name:"cardinality"},{name:"elements",defaultValue:[]},{name:"lookahead"}]};case q:return{name:q,properties:[{name:"cardinality"},{name:"feature"},{name:"lookahead"},{name:"operator"},{name:"terminal"}]};case Q:return{name:Q,properties:[{name:"cardinality"},{name:"left"},{name:"lookahead"},{name:"right"}]};case ee:return{name:ee,properties:[{name:"cardinality"},{name:"deprecatedSyntax",defaultValue:!1},{name:"lookahead"},{name:"terminal"},{name:"type"}]};case er:return{name:er,properties:[{name:"cardinality"},{name:"lookahead"}]};case ei:return{name:ei,properties:[{name:"cardinality"},{name:"elements",defaultValue:[]},{name:"guardCondition"},{name:"lookahead"}]};case es:return{name:es,properties:[{name:"cardinality"},{name:"lookahead"},{name:"value"}]};case el:return{name:el,properties:[{name:"cardinality"},{name:"lookahead"},{name:"terminal"}]};case ec:return{name:ec,properties:[{name:"cardinality"},{name:"lookahead"},{name:"regex"}]};case eh:return{name:eh,properties:[{name:"arguments",defaultValue:[]},{name:"cardinality"},{name:"lookahead"},{name:"rule"}]};case ep:return{name:ep,properties:[{name:"cardinality"},{name:"elements",defaultValue:[]},{name:"lookahead"}]};case eg:return{name:eg,properties:[{name:"cardinality"},{name:"elements",defaultValue:[]},{name:"lookahead"}]};case eT:return{name:eT,properties:[{name:"cardinality"},{name:"lookahead"},{name:"rule"}]};case eR:return{name:eR,properties:[{name:"cardinality"},{name:"elements",defaultValue:[]},{name:"lookahead"}]};case eA:return{name:eA,properties:[{name:"cardinality"},{name:"lookahead"},{name:"terminal"}]};case e$:return{name:e$,properties:[{name:"cardinality"},{name:"lookahead"}]};default:return{name:e,properties:[]}}}}let eS=new eI},93774:function(e,t,r){r.d(t,{P:()=>u});var n=r(84376),i=r(46117),a=r(58892),s=r(84608),o=r(42268),l=r(31714);class u{constructor(){this.diagnostics=[]}buildTokens(e,t){let r=(0,l.Vw)((0,s.VD)(e,!1)),n=this.buildTerminalTokens(r),i=this.buildKeywordTokens(r,n,t);return n.forEach(e=>{let t=e.PATTERN;"object"==typeof t&&t&&"test"in t&&(0,o.cb)(t)?i.unshift(e):i.push(e)}),i}flushLexingReport(e){return{diagnostics:this.popDiagnostics()}}popDiagnostics(){let e=[...this.diagnostics];return this.diagnostics=[],e}buildTerminalTokens(e){return e.filter(i.MS).filter(e=>!e.fragment).map(e=>this.buildTerminalToken(e)).toArray()}buildTerminalToken(e){let t=(0,s.s1)(e),r=this.requiresCustomPattern(t)?this.regexPatternFunction(t):t,i={name:e.name,PATTERN:r};return"function"==typeof r&&(i.LINE_BREAKS=!0),e.hidden&&(i.GROUP=(0,o.cb)(t)?n.hW.SKIPPED:"hidden"),i}requiresCustomPattern(e){return!!(e.flags.includes("u")||e.flags.includes("s"))||!!(e.source.includes("?<=")||e.source.includes("?<!"))}regexPatternFunction(e){let t=RegExp(e,e.flags+"y");return(e,r)=>(t.lastIndex=r,t.exec(e))}buildKeywordTokens(e,t,r){return e.filter(i.F9).flatMap(e=>(0,a.VY)(e).filter(i.p1)).distinct(e=>e.value).toArray().sort((e,t)=>t.value.length-e.value.length).map(e=>this.buildKeywordToken(e,t,!!(null==r?void 0:r.caseInsensitive)))}buildKeywordToken(e,t,r){let n=this.buildKeywordPattern(e,r),i={name:e.value,PATTERN:n,LONGER_ALT:this.findLongerAlt(e,t)};return"function"==typeof n&&(i.LINE_BREAKS=!0),i}buildKeywordPattern(e,t){return t?new RegExp((0,o.cp)(e.value)):e.value}findLongerAlt(e,t){return t.reduce((t,r)=>{let n=null==r?void 0:r.PATTERN;return(null==n?void 0:n.source)&&(0,o.XC)("^"+n.source+"$",e.value)&&t.push(r),t},[])}}},78249:function(e,t,r){r.d(t,{t:()=>o});var n,i,a=r(46117),s=r(84608);class o{convert(e,t){let r=t.grammarSource;if((0,a.Ki)(r)&&(r=(0,s.eN)(r)),(0,a.t3)(r)){let n=r.rule.ref;if(!n)throw Error("This cst node was not parsed by a rule.");return this.runConverter(n,e,t)}return e}runConverter(e,t,r){var n;switch(e.name.toUpperCase()){case"INT":return i.convertInt(t);case"STRING":return i.convertString(t);case"ID":return i.convertID(t)}switch(null==(n=(0,s.mJ)(e))?void 0:n.toLowerCase()){case"number":return i.convertNumber(t);case"boolean":return i.convertBoolean(t);case"bigint":return i.convertBigint(t);case"date":return i.convertDate(t);default:return t}}}(n=i||(i={})).convertString=function(e){let t="";for(let r=1;r<e.length-1;r++){let n=e.charAt(r);"\\"===n?t+=function(e){switch(e){case"b":return"\b";case"f":return"\f";case"n":return"\n";case"r":return"\r";case"t":return"	";case"v":return"\v";case"0":return"\0";default:return e}}(e.charAt(++r)):t+=n}return t},n.convertID=function(e){return"^"===e.charAt(0)?e.substring(1):e},n.convertInt=function(e){return parseInt(e)},n.convertBigint=function(e){return BigInt(e)},n.convertDate=function(e){return new Date(e)},n.convertNumber=function(e){return Number(e)},n.convertBoolean=function(e){return"true"===e.toLowerCase()}},89365:function(e,t,r){function n(e){return"object"==typeof e&&null!==e&&"string"==typeof e.$type}function i(e){return"object"==typeof e&&null!==e&&"string"==typeof e.$refText}function a(e){return"object"==typeof e&&null!==e&&"string"==typeof e.name&&"string"==typeof e.type&&"string"==typeof e.path}function s(e){return"object"==typeof e&&null!==e&&n(e.container)&&i(e.reference)&&"string"==typeof e.message}r.d(t,{$v:()=>o,SI:()=>a,U8:()=>c,Yk:()=>i,al:()=>l,dm:()=>u,et:()=>s,xA:()=>n});class o{constructor(){this.subtypes={},this.allSubtypes={}}isInstance(e,t){return n(e)&&this.isSubtype(e.$type,t)}isSubtype(e,t){if(e===t)return!0;let r=this.subtypes[e];r||(r=this.subtypes[e]={});let n=r[t];if(void 0!==n)return n;{let n=this.computeIsSubtype(e,t);return r[t]=n,n}}getAllSubTypes(e){let t=this.allSubtypes[e];if(t)return t;{let t=this.getAllTypes(),r=[];for(let n of t)this.isSubtype(n,e)&&r.push(n);return this.allSubtypes[e]=r,r}}}function l(e){return"object"==typeof e&&null!==e&&Array.isArray(e.content)}function u(e){return"object"==typeof e&&null!==e&&"object"==typeof e.tokenType}function c(e){return l(e)&&"string"==typeof e.fullText}},58892:function(e,t,r){r.d(t,{E$:()=>u,Me:()=>l,VY:()=>d,V_:()=>o,Zc:()=>h,a1:()=>m,b2:()=>s,fy:()=>p,sx:()=>c});var n=r(89365),i=r(31714),a=r(10769);function s(e){for(let[t,r]of Object.entries(e))!t.startsWith("$")&&(Array.isArray(r)?r.forEach((r,i)=>{(0,n.xA)(r)&&(r.$container=e,r.$containerProperty=t,r.$containerIndex=i)}):(0,n.xA)(r)&&(r.$container=e,r.$containerProperty=t))}function o(e,t){let r=e;for(;r;){if(t(r))return r;r=r.$container}}function l(e){let t=u(e).$document;if(!t)throw Error("AST node has no document.");return t}function u(e){for(;e.$container;)e=e.$container;return e}function c(e,t){if(!e)throw Error("Node must be an AstNode.");let r=null==t?void 0:t.range;return new i.i(()=>({keys:Object.keys(e),keyIndex:0,arrayIndex:0}),t=>{for(;t.keyIndex<t.keys.length;){let i=t.keys[t.keyIndex];if(!i.startsWith("$")){let a=e[i];if((0,n.xA)(a)){if(t.keyIndex++,f(a,r))return{done:!1,value:a}}else if(Array.isArray(a)){for(;t.arrayIndex<a.length;){let e=a[t.arrayIndex++];if((0,n.xA)(e)&&f(e,r))return{done:!1,value:e}}t.arrayIndex=0}}t.keyIndex++}return i.Ry})}function d(e,t){if(!e)throw Error("Root node must be an AstNode.");return new i.i8(e,e=>c(e,t))}function h(e,t){if(e){if((null==t?void 0:t.range)&&!f(e,t.range))return new i.i8(e,()=>[])}else throw Error("Root node must be an AstNode.");return new i.i8(e,e=>c(e,t),{includeRoot:!0})}function f(e,t){var r;if(!t)return!0;let n=null==(r=e.$cstNode)?void 0:r.range;return!!n&&(0,a.Z2)(n,t)}function p(e){return new i.i(()=>({keys:Object.keys(e),keyIndex:0,arrayIndex:0}),t=>{for(;t.keyIndex<t.keys.length;){let r=t.keys[t.keyIndex];if(!r.startsWith("$")){let i=e[r];if((0,n.Yk)(i))return t.keyIndex++,{done:!1,value:{reference:i,container:e,property:r}};if(Array.isArray(i)){for(;t.arrayIndex<i.length;){let a=t.arrayIndex++,s=i[a];if((0,n.Yk)(s))return{done:!1,value:{reference:s,container:e,property:r,index:a}}}t.arrayIndex=0}}t.keyIndex++}return i.Ry})}function m(e,t){for(let r of e.getTypeMetaData(t.$type).properties)void 0!==r.defaultValue&&void 0===t[r.name]&&(t[r.name]=function e(t){return Array.isArray(t)?[...t.map(e)]:t}(r.defaultValue))}},10769:function(e,t,r){r.d(t,{LK:()=>f,OB:()=>l,Z2:()=>d,_t:()=>o,sp:()=>u,uz:()=>h,yn:()=>c});var n,i,a=r(89365),s=r(31714);function o(e){return new s.i8(e,e=>(0,a.al)(e)?e.content:[],{includeRoot:!0})}function l(e,t){for(;e.container;)if((e=e.container)===t)return!0;return!1}function u(e){return{start:{character:e.startColumn-1,line:e.startLine-1},end:{character:e.endColumn,line:e.endLine-1}}}function c(e){if(!e)return;let{offset:t,end:r,range:n}=e;return{range:n,offset:t,end:r,length:r-t}}function d(e,t){return function(e,t){if(e.end.line<t.start.line||e.end.line===t.start.line&&e.end.character<=t.start.character)return i.Before;if(e.start.line>t.end.line||e.start.line===t.end.line&&e.start.character>=t.end.character)return i.After;let r=e.start.line>t.start.line||e.start.line===t.start.line&&e.start.character>=t.start.character,n=e.end.line<t.end.line||e.end.line===t.end.line&&e.end.character<=t.end.character;return r&&n?i.Inside:r?i.OverlapBack:n?i.OverlapFront:i.Outside}(e,t)>i.After}(n=i||(i={}))[n.Before=0]="Before",n[n.After=1]="After",n[n.OverlapFront=2]="OverlapFront",n[n.OverlapBack=3]="OverlapBack",n[n.Inside=4]="Inside",n[n.Outside=5]="Outside";let h=/^[\w\p{L}]$/u;function f(e,t){if(e){let r=function(e,t=!0){for(;e.container;){let r=e.container,n=r.content.indexOf(e);for(;n>0;){n--;let e=r.content[n];if(t||!e.hidden)return e}e=r}}(e,!0);if(r&&p(r,t))return r;if((0,a.U8)(e)){let r=e.content.findIndex(e=>!e.hidden);for(let n=r-1;n>=0;n--){let r=e.content[n];if(p(r,t))return r}}}}function p(e,t){return(0,a.dm)(e)&&t.includes(e.tokenType.name)}},63532:function(e,t,r){r.d(t,{U:()=>i,h:()=>n});class n extends Error{constructor(e,t){super(e?`${t} at ${e.range.start.line}:${e.range.start.character}`:t)}}function i(e){throw Error("Error! The input value was not handled.")}},84608:function(e,t,r){r.d(t,{$G:()=>v,EL:()=>h,UP:()=>T,VD:()=>u,eN:()=>c,h7:()=>g,ib:()=>y,lA:()=>m,mJ:()=>R,md:()=>d,s1:()=>E,vb:()=>f,z$:()=>function e(t){var r,n,a;if(i.F9(t))return T(t)?t.name:null!=(r=v(t))?r:t.name;if(i.QV(t)||i.P9(t)||i.Mp(t))return t.name;if(i.LG(t)){let r=(n=t).inferredType?n.inferredType.name:(null==(a=n.type)?void 0:a.ref)?e(n.type.ref):void 0;if(r)return r}else if(i.S_(t))return t.name;throw Error("Cannot get name of Unknown Type")}});var n=r(63532),i=r(46117),a=r(89365),s=r(58892),o=r(10769),l=r(42268);function u(e,t){let r=new Set,n=e.rules.find(e=>i.F9(e)&&e.entry);if(!n)return new Set(e.rules);for(let a of[n].concat(e.rules.filter(e=>i.MS(e)&&e.hidden)))!function e(t,r,n){r.add(t.name),(0,s.VY)(t).forEach(t=>{if(i.t3(t)||n&&i.gf(t)){let i=t.rule.ref;i&&!r.has(i.name)&&e(i,r,n)}})}(a,r,t);let a=new Set;for(let t of e.rules)(r.has(t.name)||i.MS(t)&&t.hidden)&&a.add(t);return a}function c(e){if(e.terminal)return e.terminal;if(e.type.ref){let t=y(e.type.ref);return null==t?void 0:t.terminal}}function d(e){return e.hidden&&!(0,l.cb)(E(e))}function h(e,t){return e&&t?p(e,t,e.astNode,!0):[]}function f(e,t,r){if(!e||!t)return;let n=p(e,t,e.astNode,!0);if(0!==n.length)return r=void 0!==r?Math.max(0,Math.min(r,n.length-1)):0,n[r]}function p(e,t,r,n){if(!n){let r=(0,s.V_)(e.grammarSource,i.B7);if(r&&r.feature===t)return[e]}return(0,a.al)(e)&&e.astNode===r?e.content.flatMap(e=>p(e,t,r,!1)):[]}function m(e,t,r){if(!e)return;let n=function(e,t,r){let n;if(e.astNode!==r)return[];if(i.p1(e.grammarSource)&&e.grammarSource.value===t)return[e];let a=(0,o._t)(e).iterator(),s=[];do if(!(n=a.next()).done){let e=n.value;e.astNode===r?i.p1(e.grammarSource)&&e.grammarSource.value===t&&s.push(e):a.prune()}while(!n.done);return s}(e,t,null==e?void 0:e.astNode);if(0!==n.length)return r=void 0!==r?Math.max(0,Math.min(r,n.length-1)):0,n[r]}function g(e){var t;let r=e.astNode;for(;r===(null==(t=e.container)?void 0:t.astNode);){let t=(0,s.V_)(e.grammarSource,i.B7);if(t)return t;e=e.container}}function y(e){let t=e;return i.S_(t)&&(i.LG(t.$container)?t=t.$container.$container:i.F9(t.$container)?t=t.$container:(0,n.U)(t.$container)),function e(t,r,n){var a;function o(r,a){let o;return(0,s.V_)(r,i.B7)||(o=e(a,a,n)),n.set(t,o),o}if(n.has(t))return n.get(t);for(let e of(n.set(t,void 0),(0,s.VY)(r)))if(i.B7(e)&&"name"===e.feature.toLowerCase())return n.set(t,e),e;else if(i.t3(e)&&i.F9(e.rule.ref))return o(e,e.rule.ref);else if(i.Iy(e)&&(null==(a=e.typeRef)?void 0:a.ref))return o(e,e.typeRef.ref)}(e,t,new Map)}function T(e){return function e(t,r){if(r.has(t))return!0;for(let n of(r.add(t),(0,s.VY)(t)))if(i.t3(n)){if(!n.rule.ref||i.F9(n.rule.ref)&&!e(n.rule.ref,r))return!1}else if(i.B7(n))return!1;else if(i.LG(n))return!1;return!!t.definition}(e,new Set)}function v(e){if(e.inferredType)return e.inferredType.name;if(e.dataType)return e.dataType;if(e.returnType){let t=e.returnType.ref;if(t){if(i.F9(t))return t.name;else if(i.QV(t)||i.P9(t))return t.name}}}function R(e){var t,r,n;return i.MS(e)?null!=(r=null==(t=e.type)?void 0:t.name)?r:"string":null!=(n=v(e))?n:e.name}function E(e){let t={s:!1,i:!1,u:!1};return new RegExp(function e(t,r){var n,a,s,o,l;if(i.V7(t)){return $((n=t).elements.map(t=>e(t)).join("|"),{cardinality:n.cardinality,lookahead:n.lookahead})}if(i.X9(t)){return $((a=t).elements.map(t=>e(t)).join(""),{cardinality:a.cardinality,lookahead:a.lookahead})}if(i.Bf(t)){return(s=t).right?$(`[${k(s.left)}-${k(s.right)}]`,{cardinality:s.cardinality,lookahead:s.lookahead,wrap:!1}):$(k(s.left),{cardinality:s.cardinality,lookahead:s.lookahead,wrap:!1})}if(i.gf(t)){let r=t.rule.ref;if(!r)throw Error("Missing rule reference.");return $(e(r.definition),{cardinality:t.cardinality,lookahead:t.lookahead})}if(i.Bi(t)){return o=t,$(`(?!${e(o.terminal)})${A}*?`,{cardinality:o.cardinality,lookahead:o.lookahead})}else if(i.OG(t)){return l=t,$(`${A}*?${e(l.terminal)}`,{cardinality:l.cardinality,lookahead:l.lookahead})}else if(i.Sg(t)){let e=t.regex.lastIndexOf("/"),n=t.regex.substring(1,e),i=t.regex.substring(e+1);return r&&(r.i=i.includes("i"),r.s=i.includes("s"),r.u=i.includes("u")),$(n,{cardinality:t.cardinality,lookahead:t.lookahead,wrap:!1})}else if(i.qm(t))return $(A,{cardinality:t.cardinality,lookahead:t.lookahead});else throw Error(`Invalid terminal element: ${null==t?void 0:t.$type}`)}(e.definition,t),Object.entries(t).filter(([,e])=>e).map(([e])=>e).join(""))}let A=/[\s\S]/.source;function k(e){return(0,l.hr)(e.value)}function $(e,t){var r;return((!1!==t.wrap||t.lookahead)&&(e=`(${null!=(r=t.lookahead)?r:""}${e})`),t.cardinality)?`${e}${t.cardinality}`:e}},42268:function(e,t,r){r.d(t,{K0:()=>i,Rn:()=>l,XC:()=>f,cb:()=>c,cp:()=>h,hr:()=>d});var n=r(51026);let i=/\r?\n/gm,a=new n.O;class s extends n.e{constructor(){super(...arguments),this.isStarting=!0,this.endRegexpStack=[],this.multiline=!1}get endRegex(){return this.endRegexpStack.join("")}reset(e){this.multiline=!1,this.regex=e,this.startRegexp="",this.isStarting=!0,this.endRegexpStack=[]}visitGroup(e){e.quantifier&&(this.isStarting=!1,this.endRegexpStack=[])}visitCharacter(e){let t=String.fromCharCode(e.value);if(this.multiline||"\n"!==t||(this.multiline=!0),e.quantifier)this.isStarting=!1,this.endRegexpStack=[];else{let e=d(t);this.endRegexpStack.push(e),this.isStarting&&(this.startRegexp+=e)}}visitSet(e){if(!this.multiline){let t=new RegExp(this.regex.substring(e.loc.begin,e.loc.end));this.multiline=!!"\n".match(t)}if(e.quantifier)this.isStarting=!1,this.endRegexpStack=[];else{let t=this.regex.substring(e.loc.begin,e.loc.end);this.endRegexpStack.push(t),this.isStarting&&(this.startRegexp+=t)}}visitChildren(e){"Group"===e.type&&e.quantifier||super.visitChildren(e)}}let o=new s;function l(e){try{return"string"==typeof e&&(e=new RegExp(e)),e=e.toString(),o.reset(e),o.visit(a.pattern(e)),o.multiline}catch(e){return!1}}let u="\f\n\r	\v \xa0            \u2028\u2029  　\uFEFF".split("");function c(e){let t="string"==typeof e?new RegExp(e):e;return u.some(e=>t.test(e))}function d(e){return e.replace(/[.*+?^${}()|[\]\\]/g,"\\$&")}function h(e){return Array.prototype.map.call(e,e=>/\w/.test(e)?`[${e.toLowerCase()}${e.toUpperCase()}]`:d(e)).join("")}function f(e,t){let r=function(e){"string"==typeof e&&(e=new RegExp(e));let t=e,r=e.source,n=0;return new RegExp(function e(){let i="",a;function s(e){i+=r.substr(n,e),n+=e}function o(e){i+="(?:"+r.substr(n,e)+"|$)",n+=e}for(;n<r.length;)switch(r[n]){case"\\":switch(r[n+1]){case"c":o(3);break;case"x":o(4);break;case"u":o(t.unicode?"{"===r[n+2]?r.indexOf("}",n)-n+1:6:2);break;case"p":case"P":o(t.unicode?r.indexOf("}",n)-n+1:2);break;case"k":o(r.indexOf(">",n)-n+1);break;default:o(2)}break;case"[":(a=/\[(?:\\.|.)*?\]/g).lastIndex=n,o((a=a.exec(r)||[])[0].length);break;case"|":case"^":case"$":case"*":case"+":case"?":s(1);break;case"{":(a=/\{\d+,?\d*\}/g).lastIndex=n,(a=a.exec(r))?s(a[0].length):o(1);break;case"(":if("?"===r[n+1])switch(r[n+2]){case":":i+="(?:",n+=3,i+=e()+"|$)";break;case"=":i+="(?=",n+=3,i+=e()+")";break;case"!":a=n,n+=3,e(),i+=r.substr(a,n-a);break;case"<":switch(r[n+3]){case"=":case"!":a=n,n+=4,e(),i+=r.substr(a,n-a);break;default:s(r.indexOf(">",n)-n+1),i+=e()+"|$)"}}else s(1),i+=e()+"|$)";break;case")":return++n,i;default:o(1)}return i}(),e.flags)}(e),n=t.match(r);return!!n&&n[0].length>0}},31714:function(e,t,r){var n,i;r.d(t,{IH:()=>n,Ry:()=>l,Vw:()=>u,i:()=>a,i8:()=>c});class a{constructor(e,t){this.startFn=e,this.nextFn=t}iterator(){let e={state:this.startFn(),next:()=>this.nextFn(e.state),[Symbol.iterator]:()=>e};return e}[Symbol.iterator](){return this.iterator()}isEmpty(){return!!this.iterator().next().done}count(){let e=this.iterator(),t=0,r=e.next();for(;!r.done;)t++,r=e.next();return t}toArray(){let e,t=[],r=this.iterator();do void 0!==(e=r.next()).value&&t.push(e.value);while(!e.done);return t}toSet(){return new Set(this)}toMap(e,t){return new Map(this.map(r=>[e?e(r):r,t?t(r):r]))}toString(){return this.join()}concat(e){return new a(()=>({first:this.startFn(),firstDone:!1,iterator:e[Symbol.iterator]()}),e=>{let t;if(!e.firstDone){do if(!(t=this.nextFn(e.first)).done)return t;while(!t.done);e.firstDone=!0}do if(!(t=e.iterator.next()).done)return t;while(!t.done);return l})}join(e=","){let t,r=this.iterator(),n="",i=!1;do{var a;(t=r.next()).done||(i&&(n+=e),n+="string"==typeof(a=t.value)?a:void 0===a?"undefined":"function"==typeof a.toString?a.toString():Object.prototype.toString.call(a)),i=!0}while(!t.done);return n}indexOf(e,t=0){let r=this.iterator(),n=0,i=r.next();for(;!i.done;){if(n>=t&&i.value===e)return n;i=r.next(),n++}return -1}every(e){let t=this.iterator(),r=t.next();for(;!r.done;){if(!e(r.value))return!1;r=t.next()}return!0}some(e){let t=this.iterator(),r=t.next();for(;!r.done;){if(e(r.value))return!0;r=t.next()}return!1}forEach(e){let t=this.iterator(),r=0,n=t.next();for(;!n.done;)e(n.value,r),n=t.next(),r++}map(e){return new a(this.startFn,t=>{let{done:r,value:n}=this.nextFn(t);return r?l:{done:!1,value:e(n)}})}filter(e){return new a(this.startFn,t=>{let r;do if(!(r=this.nextFn(t)).done&&e(r.value))return r;while(!r.done);return l})}nonNullable(){return this.filter(e=>null!=e)}reduce(e,t){let r=this.iterator(),n=t,i=r.next();for(;!i.done;)n=void 0===n?i.value:e(n,i.value),i=r.next();return n}reduceRight(e,t){return this.recursiveReduce(this.iterator(),e,t)}recursiveReduce(e,t,r){let n=e.next();if(n.done)return r;let i=this.recursiveReduce(e,t,r);return void 0===i?n.value:t(i,n.value)}find(e){let t=this.iterator(),r=t.next();for(;!r.done;){if(e(r.value))return r.value;r=t.next()}}findIndex(e){let t=this.iterator(),r=0,n=t.next();for(;!n.done;){if(e(n.value))return r;n=t.next(),r++}return -1}includes(e){let t=this.iterator(),r=t.next();for(;!r.done;){if(r.value===e)return!0;r=t.next()}return!1}flatMap(e){return new a(()=>({this:this.startFn()}),t=>{do{if(t.iterator){let e=t.iterator.next();if(!e.done)return e;t.iterator=void 0}let{done:r,value:n}=this.nextFn(t.this);if(!r){let r=e(n);if(!s(r))return{done:!1,value:r};t.iterator=r[Symbol.iterator]()}}while(t.iterator);return l})}flat(e){if(void 0===e&&(e=1),e<=0)return this;let t=e>1?this.flat(e-1):this;return new a(()=>({this:t.startFn()}),e=>{do{if(e.iterator){let t=e.iterator.next();if(!t.done)return t;e.iterator=void 0}let{done:r,value:n}=t.nextFn(e.this);if(!r)if(!s(n))return{done:!1,value:n};else e.iterator=n[Symbol.iterator]()}while(e.iterator);return l})}head(){let e=this.iterator().next();if(!e.done)return e.value}tail(e=1){return new a(()=>{let t=this.startFn();for(let r=0;r<e&&!this.nextFn(t).done;r++);return t},this.nextFn)}limit(e){return new a(()=>({size:0,state:this.startFn()}),t=>(t.size++,t.size>e)?l:this.nextFn(t.state))}distinct(e){return new a(()=>({set:new Set,internalState:this.startFn()}),t=>{let r;do if(!(r=this.nextFn(t.internalState)).done){let n=e?e(r.value):r.value;if(!t.set.has(n))return t.set.add(n),r}while(!r.done);return l})}exclude(e,t){let r=new Set;for(let n of e){let e=t?t(n):n;r.add(e)}return this.filter(e=>{let n=t?t(e):e;return!r.has(n)})}}function s(e){return!!e&&"function"==typeof e[Symbol.iterator]}let o=new a(()=>void 0,()=>l),l=Object.freeze({done:!0,value:void 0});function u(...e){if(1===e.length){let t=e[0];if(t instanceof a)return t;if(s(t))return new a(()=>t[Symbol.iterator](),e=>e.next());if("number"==typeof t.length)return new a(()=>({index:0}),e=>e.index<t.length?{done:!1,value:t[e.index++]}:l)}return e.length>1?new a(()=>({collIndex:0,arrIndex:0}),t=>{do{if(t.iterator){let e=t.iterator.next();if(!e.done)return e;t.iterator=void 0}if(t.array){if(t.arrIndex<t.array.length)return{done:!1,value:t.array[t.arrIndex++]};t.array=void 0,t.arrIndex=0}if(t.collIndex<e.length){let r=e[t.collIndex++];s(r)?t.iterator=r[Symbol.iterator]():r&&"number"==typeof r.length&&(t.array=r)}}while(t.iterator||t.array||t.collIndex<e.length);return l}):o}class c extends a{constructor(e,t,r){super(()=>({iterators:(null==r?void 0:r.includeRoot)?[[e][Symbol.iterator]()]:[t(e)[Symbol.iterator]()],pruned:!1}),e=>{for(e.pruned&&(e.iterators.pop(),e.pruned=!1);e.iterators.length>0;){let r=e.iterators[e.iterators.length-1].next();if(!r.done)return e.iterators.push(t(r.value)[Symbol.iterator]()),r;e.iterators.pop()}return l})}iterator(){let e={state:this.startFn(),next:()=>this.nextFn(e.state),prune:()=>{e.state.pruned=!0},[Symbol.iterator]:()=>e};return e}}(i=n||(n={})).sum=function(e){return e.reduce((e,t)=>e+t,0)},i.product=function(e){return e.reduce((e,t)=>e*t,0)},i.min=function(e){return e.reduce((e,t)=>Math.min(e,t))},i.max=function(e){return e.reduce((e,t)=>Math.max(e,t))}},62297:function(e,t,r){r.d(t,{u:()=>i});class n{readFile(){throw Error("No file system is available.")}async readDirectory(){return[]}}let i={fileSystemProvider:()=>new n}},27014:function(e,t,r){r.d(t,{Z:()=>i});var n=r(56721);let i=function(e,t,r){for(var i=-1,a=e.length;++i<a;){var s=e[i],o=t(s);if(null!=o&&(void 0===l?o==o&&!(0,n.Z)(o):r(o,l)))var l=o,u=s}return u}},80400:function(e,t,r){r.d(t,{Z:()=>n});let n=function(e,t){return e<t}},57050:function(e,t,r){r.d(t,{Z:()=>a});var n=r(61411),i=r(67737);let a=function(e,t){var r=-1,a=(0,i.Z)(e)?Array(e.length):[];return(0,n.Z)(e,function(e,n,i){a[++r]=t(e,n,i)}),a}},63519:function(e,t,r){r.d(t,{Z:()=>c});var n=r(80954),i=r(26028),a=r(17966),s=r(1400),o=r(26129),l=r(32452);let u=function(e,t,r,n){if(!(0,o.Z)(e))return e;t=(0,a.Z)(t,e);for(var u=-1,c=t.length,d=c-1,h=e;null!=h&&++u<c;){var f=(0,l.Z)(t[u]),p=r;if("__proto__"===f||"constructor"===f||"prototype"===f)break;if(u!=d){var m=h[f];void 0===(p=n?n(m,f,h):void 0)&&(p=(0,o.Z)(m)?m:(0,s.Z)(t[u+1])?[]:{})}(0,i.Z)(h,f,p),h=h[f]}return e},c=function(e,t,r){for(var i=-1,s=t.length,o={};++i<s;){var l=t[i],c=(0,n.Z)(e,l);r(c,l)&&u(o,(0,a.Z)(l,e),c)}return o}},1209:function(e,t,r){r.d(t,{Z:()=>i});var n=r(94735);let i=function(e){return(0,n.Z)(e,4)}},27272:function(e,t,r){r.d(t,{Z:()=>u});var n=r(11021),i=r(94596),a=r(99302),s=r(27042),o=Object.prototype,l=o.hasOwnProperty;let u=(0,n.Z)(function(e,t){e=Object(e);var r=-1,n=t.length,u=n>2?t[2]:void 0;for(u&&(0,a.Z)(t[0],t[1],u)&&(n=1);++r<n;)for(var c=t[r],d=(0,s.Z)(c),h=-1,f=d.length;++h<f;){var p=d[h],m=e[p];(void 0===m||(0,i.Z)(m,o[p])&&!l.call(e,p))&&(e[p]=c[p])}return e})},90083:function(e,t,r){r.d(t,{Z:()=>c});var n,i=r(36616),a=r(67737),s=r(71257),o=r(88343),l=r(47527),u=Math.max;let c=(n=function(e,t,r){var n=null==e?0:e.length;if(!n)return -1;var a=null==r?0:(0,l.Z)(r);return a<0&&(a=u(n+a,0)),(0,o.Z)(e,(0,i.Z)(t,3),a)},function(e,t,r){var o=Object(e);if(!(0,a.Z)(e)){var l=(0,i.Z)(t,3);e=(0,s.Z)(e),t=function(e){return l(o[e],e,o)}}var u=n(e,t,r);return u>-1?o[l?e[u]:u]:void 0})},24945:function(e,t,r){r.d(t,{Z:()=>a});var n=r(27796),i=r(47191);let a=function(e,t){return(0,n.Z)((0,i.Z)(e,t),1)}},40290:function(e,t,r){r.d(t,{Z:()=>s});var n=Object.prototype.hasOwnProperty;let i=function(e,t){return null!=e&&n.call(e,t)};var a=r(61105);let s=function(e,t){return null!=e&&(0,a.Z)(e,t,i)}},88514:function(e,t,r){r.d(t,{Z:()=>s});var n=r(21452),i=r(3073),a=r(32398);let s=function(e){return"string"==typeof e||!(0,i.Z)(e)&&(0,a.Z)(e)&&"[object String]"==(0,n.Z)(e)}},90437:function(e,t,r){r.d(t,{Z:()=>n});let n=function(e){var t=null==e?0:e.length;return t?e[t-1]:void 0}},47191:function(e,t,r){r.d(t,{Z:()=>o});var n=r(75952),i=r(36616),a=r(57050),s=r(3073);let o=function(e,t){return((0,s.Z)(e)?n.Z:a.Z)(e,(0,i.Z)(t,3))}},82771:function(e,t,r){r.d(t,{Z:()=>s});var n=r(27014),i=r(80400),a=r(85627);let s=function(e){return e&&e.length?(0,n.Z)(e,a.Z,i.Z):void 0}},69295:function(e,t,r){r.d(t,{Z:()=>m});var n=/\s/;let i=function(e){for(var t=e.length;t--&&n.test(e.charAt(t)););return t};var a=/^\s+/,s=r(26129),o=r(56721),l=0/0,u=/^[-+]0x[0-9a-f]+$/i,c=/^0b[01]+$/i,d=/^0o[0-7]+$/i,h=parseInt;let f=function(e){if("number"==typeof e)return e;if((0,o.Z)(e))return l;if((0,s.Z)(e)){var t,r="function"==typeof e.valueOf?e.valueOf():e;e=(0,s.Z)(r)?r+"":r}if("string"!=typeof e)return 0===e?e:+e;e=(t=e)?t.slice(0,i(t)+1).replace(a,""):t;var n=c.test(e);return n||d.test(e)?h(e.slice(2),n?2:8):u.test(e)?l:+e};var p=1/0;let m=function(e){return e?(e=f(e))===p||e===-p?(e<0?-1:1)*17976931348623157e292:e==e?e:0:0===e?e:0}},47527:function(e,t,r){r.d(t,{Z:()=>i});var n=r(69295);let i=function(e){var t=(0,n.Z)(e),r=t%1;return t==t?r?t-r:t:0}},1862:function(e,t,r){r.d(t,{c:()=>l,o:()=>o});var n={470:e=>{function t(e){if("string"!=typeof e)throw TypeError("Path must be a string. Received "+JSON.stringify(e))}function r(e,t){for(var r,n="",i=0,a=-1,s=0,o=0;o<=e.length;++o){if(o<e.length)r=e.charCodeAt(o);else{if(47===r)break;r=47}if(47===r){if(a===o-1||1===s);else if(a!==o-1&&2===s){if(n.length<2||2!==i||46!==n.charCodeAt(n.length-1)||46!==n.charCodeAt(n.length-2)){if(n.length>2){var l=n.lastIndexOf("/");if(l!==n.length-1){-1===l?(n="",i=0):i=(n=n.slice(0,l)).length-1-n.lastIndexOf("/"),a=o,s=0;continue}}else if(2===n.length||1===n.length){n="",i=0,a=o,s=0;continue}}t&&(n.length>0?n+="/..":n="..",i=2)}else n.length>0?n+="/"+e.slice(a+1,o):n=e.slice(a+1,o),i=o-a-1;a=o,s=0}else 46===r&&-1!==s?++s:s=-1}return n}var n={resolve:function(){for(var e,n,i="",a=!1,s=arguments.length-1;s>=-1&&!a;s--)s>=0?e=arguments[s]:(void 0===n&&(n=process.cwd()),e=n),t(e),0!==e.length&&(i=e+"/"+i,a=47===e.charCodeAt(0));return i=r(i,!a),a?i.length>0?"/"+i:"/":i.length>0?i:"."},normalize:function(e){if(t(e),0===e.length)return".";var n=47===e.charCodeAt(0),i=47===e.charCodeAt(e.length-1);return 0!==(e=r(e,!n)).length||n||(e="."),e.length>0&&i&&(e+="/"),n?"/"+e:e},isAbsolute:function(e){return t(e),e.length>0&&47===e.charCodeAt(0)},join:function(){if(0==arguments.length)return".";for(var e,r=0;r<arguments.length;++r){var i=arguments[r];t(i),i.length>0&&(void 0===e?e=i:e+="/"+i)}return void 0===e?".":n.normalize(e)},relative:function(e,r){if(t(e),t(r),e===r||(e=n.resolve(e))===(r=n.resolve(r)))return"";for(var i=1;i<e.length&&47===e.charCodeAt(i);++i);for(var a=e.length,s=a-i,o=1;o<r.length&&47===r.charCodeAt(o);++o);for(var l=r.length-o,u=s<l?s:l,c=-1,d=0;d<=u;++d){if(d===u){if(l>u){if(47===r.charCodeAt(o+d))return r.slice(o+d+1);if(0===d)return r.slice(o+d)}else s>u&&(47===e.charCodeAt(i+d)?c=d:0===d&&(c=0));break}var h=e.charCodeAt(i+d);if(h!==r.charCodeAt(o+d))break;47===h&&(c=d)}var f="";for(d=i+c+1;d<=a;++d)d!==a&&47!==e.charCodeAt(d)||(0===f.length?f+="..":f+="/..");return f.length>0?f+r.slice(o+c):(o+=c,47===r.charCodeAt(o)&&++o,r.slice(o))},_makeLong:function(e){return e},dirname:function(e){if(t(e),0===e.length)return".";for(var r=e.charCodeAt(0),n=47===r,i=-1,a=!0,s=e.length-1;s>=1;--s)if(47===(r=e.charCodeAt(s))){if(!a){i=s;break}}else a=!1;return -1===i?n?"/":".":n&&1===i?"//":e.slice(0,i)},basename:function(e,r){if(void 0!==r&&"string"!=typeof r)throw TypeError('"ext" argument must be a string');t(e);var n,i=0,a=-1,s=!0;if(void 0!==r&&r.length>0&&r.length<=e.length){if(r.length===e.length&&r===e)return"";var o=r.length-1,l=-1;for(n=e.length-1;n>=0;--n){var u=e.charCodeAt(n);if(47===u){if(!s){i=n+1;break}}else -1===l&&(s=!1,l=n+1),o>=0&&(u===r.charCodeAt(o)?-1==--o&&(a=n):(o=-1,a=l))}return i===a?a=l:-1===a&&(a=e.length),e.slice(i,a)}for(n=e.length-1;n>=0;--n)if(47===e.charCodeAt(n)){if(!s){i=n+1;break}}else -1===a&&(s=!1,a=n+1);return -1===a?"":e.slice(i,a)},extname:function(e){t(e);for(var r=-1,n=0,i=-1,a=!0,s=0,o=e.length-1;o>=0;--o){var l=e.charCodeAt(o);if(47!==l)-1===i&&(a=!1,i=o+1),46===l?-1===r?r=o:1!==s&&(s=1):-1!==r&&(s=-1);else if(!a){n=o+1;break}}return -1===r||-1===i||0===s||1===s&&r===i-1&&r===n+1?"":e.slice(r,i)},format:function(e){var t,r;if(null===e||"object"!=typeof e)throw TypeError('The "pathObject" argument must be of type Object. Received type '+typeof e);return t=e.dir||e.root,r=e.base||(e.name||"")+(e.ext||""),t?t===e.root?t+r:t+"/"+r:r},parse:function(e){t(e);var r={root:"",dir:"",base:"",ext:"",name:""};if(0===e.length)return r;var n,i=e.charCodeAt(0),a=47===i;a?(r.root="/",n=1):n=0;for(var s=-1,o=0,l=-1,u=!0,c=e.length-1,d=0;c>=n;--c)if(47!==(i=e.charCodeAt(c)))-1===l&&(u=!1,l=c+1),46===i?-1===s?s=c:1!==d&&(d=1):-1!==s&&(d=-1);else if(!u){o=c+1;break}return -1===s||-1===l||0===d||1===d&&s===l-1&&s===o+1?-1!==l&&(r.base=r.name=0===o&&a?e.slice(1,l):e.slice(o,l)):(0===o&&a?(r.name=e.slice(1,s),r.base=e.slice(1,l)):(r.name=e.slice(o,s),r.base=e.slice(o,l)),r.ext=e.slice(s,l)),o>0?r.dir=e.slice(0,o-1):a&&(r.dir="/"),r},sep:"/",delimiter:":",win32:null,posix:null};n.posix=n,e.exports=n}},i={};function a(e){var t=i[e];if(void 0!==t)return t.exports;var r=i[e]={exports:{}};return n[e](r,r.exports,a),r.exports}a.d=(e,t)=>{for(var r in t)a.o(t,r)&&!a.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},a.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),a.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var s={};(()=>{let e;(a.r(s),a.d(s,{URI:()=>l,Utils:()=>v}),"object"==typeof process)?e="win32"===process.platform:"object"==typeof navigator&&(e=navigator.userAgent.indexOf("Windows")>=0);let t=/^\w[\w\d+.-]*$/,r=/^\//,n=/^\/\//;function i(e,i){if(!e.scheme&&i)throw Error(`[UriError]: Scheme is missing: {scheme: "", authority: "${e.authority}", path: "${e.path}", query: "${e.query}", fragment: "${e.fragment}"}`);if(e.scheme&&!t.test(e.scheme))throw Error("[UriError]: Scheme contains illegal characters.");if(e.path){if(e.authority){if(!r.test(e.path))throw Error('[UriError]: If a URI contains an authority component, then the path component must either be empty or begin with a slash ("/") character')}else if(n.test(e.path))throw Error('[UriError]: If a URI does not contain an authority component, then the path cannot begin with two slash characters ("//")')}}let o=/^(([^:/?#]+?):)?(\/\/([^/?#]*))?([^?#]*)(\?([^#]*))?(#(.*))?/;class l{static isUri(e){return e instanceof l||!!e&&"string"==typeof e.authority&&"string"==typeof e.fragment&&"string"==typeof e.path&&"string"==typeof e.query&&"string"==typeof e.scheme&&"string"==typeof e.fsPath&&"function"==typeof e.with&&"function"==typeof e.toString}scheme;authority;path;query;fragment;constructor(e,t,r,n,a,s=!1){"object"==typeof e?(this.scheme=e.scheme||"",this.authority=e.authority||"",this.path=e.path||"",this.query=e.query||"",this.fragment=e.fragment||""):(this.scheme=e||s?e:"file",this.authority=t||"",this.path=function(e,t){switch(e){case"https":case"http":case"file":t?"/"!==t[0]&&(t="/"+t):t="/"}return t}(this.scheme,r||""),this.query=n||"",this.fragment=a||"",i(this,s))}get fsPath(){return p(this,!1)}with(e){if(!e)return this;let{scheme:t,authority:r,path:n,query:i,fragment:a}=e;return void 0===t?t=this.scheme:null===t&&(t=""),void 0===r?r=this.authority:null===r&&(r=""),void 0===n?n=this.path:null===n&&(n=""),void 0===i?i=this.query:null===i&&(i=""),void 0===a?a=this.fragment:null===a&&(a=""),t===this.scheme&&r===this.authority&&n===this.path&&i===this.query&&a===this.fragment?this:new c(t,r,n,i,a)}static parse(e,t=!1){let r=o.exec(e);return r?new c(r[2]||"",y(r[4]||""),y(r[5]||""),y(r[7]||""),y(r[9]||""),t):new c("","","","","")}static file(t){let r="";if(e&&(t=t.replace(/\\/g,"/")),"/"===t[0]&&"/"===t[1]){let e=t.indexOf("/",2);-1===e?(r=t.substring(2),t="/"):(r=t.substring(2,e),t=t.substring(e)||"/")}return new c("file",r,t,"","")}static from(e){let t=new c(e.scheme,e.authority,e.path,e.query,e.fragment);return i(t,!0),t}toString(e=!1){return m(this,e)}toJSON(){return this}static revive(e){if(e){if(e instanceof l)return e;{let t=new c(e);return t._formatted=e.external,t._fsPath=e._sep===u?e.fsPath:null,t}}return e}}let u=e?1:void 0;class c extends l{_formatted=null;_fsPath=null;get fsPath(){return this._fsPath||(this._fsPath=p(this,!1)),this._fsPath}toString(e=!1){return e?m(this,!0):(this._formatted||(this._formatted=m(this,!1)),this._formatted)}toJSON(){let e={$mid:1};return this._fsPath&&(e.fsPath=this._fsPath,e._sep=u),this._formatted&&(e.external=this._formatted),this.path&&(e.path=this.path),this.scheme&&(e.scheme=this.scheme),this.authority&&(e.authority=this.authority),this.query&&(e.query=this.query),this.fragment&&(e.fragment=this.fragment),e}}let d={58:"%3A",47:"%2F",63:"%3F",35:"%23",91:"%5B",93:"%5D",64:"%40",33:"%21",36:"%24",38:"%26",39:"%27",40:"%28",41:"%29",42:"%2A",43:"%2B",44:"%2C",59:"%3B",61:"%3D",32:"%20"};function h(e,t,r){let n,i=-1;for(let a=0;a<e.length;a++){let s=e.charCodeAt(a);if(s>=97&&s<=122||s>=65&&s<=90||s>=48&&s<=57||45===s||46===s||95===s||126===s||t&&47===s||r&&91===s||r&&93===s||r&&58===s)-1!==i&&(n+=encodeURIComponent(e.substring(i,a)),i=-1),void 0!==n&&(n+=e.charAt(a));else{void 0===n&&(n=e.substr(0,a));let t=d[s];void 0!==t?(-1!==i&&(n+=encodeURIComponent(e.substring(i,a)),i=-1),n+=t):-1===i&&(i=a)}}return -1!==i&&(n+=encodeURIComponent(e.substring(i))),void 0!==n?n:e}function f(e){let t;for(let r=0;r<e.length;r++){let n=e.charCodeAt(r);35===n||63===n?(void 0===t&&(t=e.substr(0,r)),t+=d[n]):void 0!==t&&(t+=e[r])}return void 0!==t?t:e}function p(t,r){let n;return n=t.authority&&t.path.length>1&&"file"===t.scheme?`//${t.authority}${t.path}`:47===t.path.charCodeAt(0)&&(t.path.charCodeAt(1)>=65&&90>=t.path.charCodeAt(1)||t.path.charCodeAt(1)>=97&&122>=t.path.charCodeAt(1))&&58===t.path.charCodeAt(2)?r?t.path.substr(1):t.path[1].toLowerCase()+t.path.substr(2):t.path,e&&(n=n.replace(/\//g,"\\")),n}function m(e,t){let r=t?f:h,n="",{scheme:i,authority:a,path:s,query:o,fragment:l}=e;if(i&&(n+=i,n+=":"),(a||"file"===i)&&(n+="/",n+="/"),a){let e=a.indexOf("@");if(-1!==e){let t=a.substr(0,e);a=a.substr(e+1),-1===(e=t.lastIndexOf(":"))?n+=r(t,!1,!1):(n+=r(t.substr(0,e),!1,!1),n+=":",n+=r(t.substr(e+1),!1,!0)),n+="@"}-1===(e=(a=a.toLowerCase()).lastIndexOf(":"))?n+=r(a,!1,!0):(n+=r(a.substr(0,e),!1,!0),n+=a.substr(e))}if(s){if(s.length>=3&&47===s.charCodeAt(0)&&58===s.charCodeAt(2)){let e=s.charCodeAt(1);e>=65&&e<=90&&(s=`/${String.fromCharCode(e+32)}:${s.substr(3)}`)}else if(s.length>=2&&58===s.charCodeAt(1)){let e=s.charCodeAt(0);e>=65&&e<=90&&(s=`${String.fromCharCode(e+32)}:${s.substr(2)}`)}n+=r(s,!0,!1)}return o&&(n+="?",n+=r(o,!1,!1)),l&&(n+="#",n+=t?l:h(l,!1,!1)),n}let g=/(%[0-9A-Za-z][0-9A-Za-z])+/g;function y(e){return e.match(g)?e.replace(g,e=>(function e(t){try{return decodeURIComponent(t)}catch{return t.length>3?t.substr(0,3)+e(t.substr(3)):t}})(e)):e}var T,v,R=a(470);let E=R.posix||R;(T=v||(v={})).joinPath=function(e,...t){return e.with({path:E.join(e.path,...t)})},T.resolvePath=function(e,...t){let r=e.path,n=!1;"/"!==r[0]&&(r="/"+r,n=!0);let i=E.resolve(r,...t);return n&&"/"===i[0]&&!e.authority&&(i=i.substring(1)),e.with({path:i})},T.dirname=function(e){if(0===e.path.length||"/"===e.path)return e;let t=E.dirname(e.path);return 1===t.length&&46===t.charCodeAt(0)&&(t=""),e.with({path:t})},T.basename=function(e){return E.basename(e.path)},T.extname=function(e){return E.extname(e.path)}})();let{URI:o,Utils:l}=s}}]);