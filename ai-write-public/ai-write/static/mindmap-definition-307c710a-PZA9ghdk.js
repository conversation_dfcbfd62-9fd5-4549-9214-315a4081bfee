import{Q as hi,T as rl,l as Er,c as ci,X as al,u as nl,Z as ja,d as en,h as il,aI as sl,aJ as ol,aK as ul,$ as ll}from"./index-HbL7qtEs.js";import{a as fl}from"./createText-ca0c5216-CTWSuWxC.js";function Xe(t){"@babel/helpers - typeof";return Xe=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Xe(t)}function vi(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function hl(t,e){for(var r=0;r<e.length;r++){var a=e[r];a.enumerable=a.enumerable||!1,a.configurable=!0,"value"in a&&(a.writable=!0),Object.defineProperty(t,a.key,a)}}function di(t,e,r){return e&&hl(t.prototype,e),Object.defineProperty(t,"prototype",{writable:!1}),t}function ao(t,e,r){return e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function St(t,e){return cl(t)||vl(t,e)||no(t,e)||dl()}function cl(t){if(Array.isArray(t))return t}function vl(t,e){var r=t==null?null:typeof Symbol<"u"&&t[Symbol.iterator]||t["@@iterator"];if(r!=null){var a=[],n=!0,i=!1,s,o;try{for(r=r.call(t);!(n=(s=r.next()).done)&&(a.push(s.value),!(e&&a.length===e));n=!0);}catch(u){i=!0,o=u}finally{try{!n&&r.return!=null&&r.return()}finally{if(i)throw o}}return a}}function no(t,e){if(t){if(typeof t=="string")return $i(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if(r==="Object"&&t.constructor&&(r=t.constructor.name),r==="Map"||r==="Set")return Array.from(t);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return $i(t,e)}}function $i(t,e){(e==null||e>t.length)&&(e=t.length);for(var r=0,a=new Array(e);r<e;r++)a[r]=t[r];return a}function dl(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function io(t,e){var r=typeof Symbol<"u"&&t[Symbol.iterator]||t["@@iterator"];if(!r){if(Array.isArray(t)||(r=no(t))||e){r&&(t=r);var a=0,n=function(){};return{s:n,n:function(){return a>=t.length?{done:!0}:{done:!1,value:t[a++]}},e:function(u){throw u},f:n}}throw new TypeError(`Invalid attempt to iterate non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}var i=!0,s=!1,o;return{s:function(){r=r.call(t)},n:function(){var u=r.next();return i=u.done,u},e:function(u){s=!0,o=u},f:function(){try{!i&&r.return!=null&&r.return()}finally{if(s)throw o}}}}var Ye=typeof window>"u"?null:window,Yi=Ye?Ye.navigator:null;Ye&&Ye.document;var gl=Xe(""),so=Xe({}),pl=Xe(function(){}),yl=typeof HTMLElement>"u"?"undefined":Xe(HTMLElement),xa=function(e){return e&&e.instanceString&&Ge(e.instanceString)?e.instanceString():null},de=function(e){return e!=null&&Xe(e)==gl},Ge=function(e){return e!=null&&Xe(e)===pl},Re=function(e){return!pt(e)&&(Array.isArray?Array.isArray(e):e!=null&&e instanceof Array)},Ce=function(e){return e!=null&&Xe(e)===so&&!Re(e)&&e.constructor===Object},ml=function(e){return e!=null&&Xe(e)===so},ie=function(e){return e!=null&&Xe(e)===Xe(1)&&!isNaN(e)},bl=function(e){return ie(e)&&Math.floor(e)===e},tn=function(e){if(yl!=="undefined")return e!=null&&e instanceof HTMLElement},pt=function(e){return Ta(e)||oo(e)},Ta=function(e){return xa(e)==="collection"&&e._private.single},oo=function(e){return xa(e)==="collection"&&!e._private.single},gi=function(e){return xa(e)==="core"},uo=function(e){return xa(e)==="stylesheet"},El=function(e){return xa(e)==="event"},jt=function(e){return e==null?!0:!!(e===""||e.match(/^\s+$/))},wl=function(e){return typeof HTMLElement>"u"?!1:e instanceof HTMLElement},xl=function(e){return Ce(e)&&ie(e.x1)&&ie(e.x2)&&ie(e.y1)&&ie(e.y2)},Tl=function(e){return ml(e)&&Ge(e.then)},Cl=function(){return Yi&&Yi.userAgent.match(/msie|trident|edge/i)},ha=function(e,r){r||(r=function(){if(arguments.length===1)return arguments[0];if(arguments.length===0)return"undefined";for(var i=[],s=0;s<arguments.length;s++)i.push(arguments[s]);return i.join("$")});var a=function n(){var i=this,s=arguments,o,u=r.apply(i,s),l=n.cache;return(o=l[u])||(o=l[u]=e.apply(i,s)),o};return a.cache={},a},pi=ha(function(t){return t.replace(/([A-Z])/g,function(e){return"-"+e.toLowerCase()})}),vn=ha(function(t){return t.replace(/(-\w)/g,function(e){return e[1].toUpperCase()})}),lo=ha(function(t,e){return t+e[0].toUpperCase()+e.substring(1)},function(t,e){return t+"$"+e}),_i=function(e){return jt(e)?e:e.charAt(0).toUpperCase()+e.substring(1)},He="(?:[-+]?(?:(?:\\d+|\\d*\\.\\d+)(?:[Ee][+-]?\\d+)?))",Dl="rgb[a]?\\(("+He+"[%]?)\\s*,\\s*("+He+"[%]?)\\s*,\\s*("+He+"[%]?)(?:\\s*,\\s*("+He+"))?\\)",Sl="rgb[a]?\\((?:"+He+"[%]?)\\s*,\\s*(?:"+He+"[%]?)\\s*,\\s*(?:"+He+"[%]?)(?:\\s*,\\s*(?:"+He+"))?\\)",Ll="hsl[a]?\\(("+He+")\\s*,\\s*("+He+"[%])\\s*,\\s*("+He+"[%])(?:\\s*,\\s*("+He+"))?\\)",Al="hsl[a]?\\((?:"+He+")\\s*,\\s*(?:"+He+"[%])\\s*,\\s*(?:"+He+"[%])(?:\\s*,\\s*(?:"+He+"))?\\)",Ol="\\#[0-9a-fA-F]{3}",Nl="\\#[0-9a-fA-F]{6}",fo=function(e,r){return e<r?-1:e>r?1:0},Il=function(e,r){return-1*fo(e,r)},be=Object.assign!=null?Object.assign.bind(Object):function(t){for(var e=arguments,r=1;r<e.length;r++){var a=e[r];if(a!=null)for(var n=Object.keys(a),i=0;i<n.length;i++){var s=n[i];t[s]=a[s]}}return t},Ml=function(e){if(!(!(e.length===4||e.length===7)||e[0]!=="#")){var r=e.length===4,a,n,i,s=16;return r?(a=parseInt(e[1]+e[1],s),n=parseInt(e[2]+e[2],s),i=parseInt(e[3]+e[3],s)):(a=parseInt(e[1]+e[2],s),n=parseInt(e[3]+e[4],s),i=parseInt(e[5]+e[6],s)),[a,n,i]}},Rl=function(e){var r,a,n,i,s,o,u,l;function f(v,p,g){return g<0&&(g+=1),g>1&&(g-=1),g<1/6?v+(p-v)*6*g:g<1/2?p:g<2/3?v+(p-v)*(2/3-g)*6:v}var h=new RegExp("^"+Ll+"$").exec(e);if(h){if(a=parseInt(h[1]),a<0?a=(360- -1*a%360)%360:a>360&&(a=a%360),a/=360,n=parseFloat(h[2]),n<0||n>100||(n=n/100,i=parseFloat(h[3]),i<0||i>100)||(i=i/100,s=h[4],s!==void 0&&(s=parseFloat(s),s<0||s>1)))return;if(n===0)o=u=l=Math.round(i*255);else{var d=i<.5?i*(1+n):i+n-i*n,c=2*i-d;o=Math.round(255*f(c,d,a+1/3)),u=Math.round(255*f(c,d,a)),l=Math.round(255*f(c,d,a-1/3))}r=[o,u,l,s]}return r},kl=function(e){var r,a=new RegExp("^"+Dl+"$").exec(e);if(a){r=[];for(var n=[],i=1;i<=3;i++){var s=a[i];if(s[s.length-1]==="%"&&(n[i]=!0),s=parseFloat(s),n[i]&&(s=s/100*255),s<0||s>255)return;r.push(Math.floor(s))}var o=n[1]||n[2]||n[3],u=n[1]&&n[2]&&n[3];if(o&&!u)return;var l=a[4];if(l!==void 0){if(l=parseFloat(l),l<0||l>1)return;r.push(l)}}return r},Pl=function(e){return Fl[e.toLowerCase()]},Bl=function(e){return(Re(e)?e:null)||Pl(e)||Ml(e)||kl(e)||Rl(e)},Fl={transparent:[0,0,0,0],aliceblue:[240,248,255],antiquewhite:[250,235,215],aqua:[0,255,255],aquamarine:[127,255,212],azure:[240,255,255],beige:[245,245,220],bisque:[255,228,196],black:[0,0,0],blanchedalmond:[255,235,205],blue:[0,0,255],blueviolet:[138,43,226],brown:[165,42,42],burlywood:[222,184,135],cadetblue:[95,158,160],chartreuse:[127,255,0],chocolate:[210,105,30],coral:[255,127,80],cornflowerblue:[100,149,237],cornsilk:[255,248,220],crimson:[220,20,60],cyan:[0,255,255],darkblue:[0,0,139],darkcyan:[0,139,139],darkgoldenrod:[184,134,11],darkgray:[169,169,169],darkgreen:[0,100,0],darkgrey:[169,169,169],darkkhaki:[189,183,107],darkmagenta:[139,0,139],darkolivegreen:[85,107,47],darkorange:[255,140,0],darkorchid:[153,50,204],darkred:[139,0,0],darksalmon:[233,150,122],darkseagreen:[143,188,143],darkslateblue:[72,61,139],darkslategray:[47,79,79],darkslategrey:[47,79,79],darkturquoise:[0,206,209],darkviolet:[148,0,211],deeppink:[255,20,147],deepskyblue:[0,191,255],dimgray:[105,105,105],dimgrey:[105,105,105],dodgerblue:[30,144,255],firebrick:[178,34,34],floralwhite:[255,250,240],forestgreen:[34,139,34],fuchsia:[255,0,255],gainsboro:[220,220,220],ghostwhite:[248,248,255],gold:[255,215,0],goldenrod:[218,165,32],gray:[128,128,128],grey:[128,128,128],green:[0,128,0],greenyellow:[173,255,47],honeydew:[240,255,240],hotpink:[255,105,180],indianred:[205,92,92],indigo:[75,0,130],ivory:[255,255,240],khaki:[240,230,140],lavender:[230,230,250],lavenderblush:[255,240,245],lawngreen:[124,252,0],lemonchiffon:[255,250,205],lightblue:[173,216,230],lightcoral:[240,128,128],lightcyan:[224,255,255],lightgoldenrodyellow:[250,250,210],lightgray:[211,211,211],lightgreen:[144,238,144],lightgrey:[211,211,211],lightpink:[255,182,193],lightsalmon:[255,160,122],lightseagreen:[32,178,170],lightskyblue:[135,206,250],lightslategray:[119,136,153],lightslategrey:[119,136,153],lightsteelblue:[176,196,222],lightyellow:[255,255,224],lime:[0,255,0],limegreen:[50,205,50],linen:[250,240,230],magenta:[255,0,255],maroon:[128,0,0],mediumaquamarine:[102,205,170],mediumblue:[0,0,205],mediumorchid:[186,85,211],mediumpurple:[147,112,219],mediumseagreen:[60,179,113],mediumslateblue:[123,104,238],mediumspringgreen:[0,250,154],mediumturquoise:[72,209,204],mediumvioletred:[199,21,133],midnightblue:[25,25,112],mintcream:[245,255,250],mistyrose:[255,228,225],moccasin:[255,228,181],navajowhite:[255,222,173],navy:[0,0,128],oldlace:[253,245,230],olive:[128,128,0],olivedrab:[107,142,35],orange:[255,165,0],orangered:[255,69,0],orchid:[218,112,214],palegoldenrod:[238,232,170],palegreen:[152,251,152],paleturquoise:[175,238,238],palevioletred:[219,112,147],papayawhip:[255,239,213],peachpuff:[255,218,185],peru:[205,133,63],pink:[255,192,203],plum:[221,160,221],powderblue:[176,224,230],purple:[128,0,128],red:[255,0,0],rosybrown:[188,143,143],royalblue:[65,105,225],saddlebrown:[139,69,19],salmon:[250,128,114],sandybrown:[244,164,96],seagreen:[46,139,87],seashell:[255,245,238],sienna:[160,82,45],silver:[192,192,192],skyblue:[135,206,235],slateblue:[106,90,205],slategray:[112,128,144],slategrey:[112,128,144],snow:[255,250,250],springgreen:[0,255,127],steelblue:[70,130,180],tan:[210,180,140],teal:[0,128,128],thistle:[216,191,216],tomato:[255,99,71],turquoise:[64,224,208],violet:[238,130,238],wheat:[245,222,179],white:[255,255,255],whitesmoke:[245,245,245],yellow:[255,255,0],yellowgreen:[154,205,50]},ho=function(e){for(var r=e.map,a=e.keys,n=a.length,i=0;i<n;i++){var s=a[i];if(Ce(s))throw Error("Tried to set map with object key");i<a.length-1?(r[s]==null&&(r[s]={}),r=r[s]):r[s]=e.value}},co=function(e){for(var r=e.map,a=e.keys,n=a.length,i=0;i<n;i++){var s=a[i];if(Ce(s))throw Error("Tried to get map with object key");if(r=r[s],r==null)return r}return r};function Gl(t){var e=typeof t;return t!=null&&(e=="object"||e=="function")}var vr=Gl,na=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};function zl(t,e){return e={exports:{}},t(e,e.exports),e.exports}var Vl=typeof na=="object"&&na&&na.Object===Object&&na,Ul=Vl,$l=typeof self=="object"&&self&&self.Object===Object&&self,Yl=Ul||$l||Function("return this")(),dn=Yl,_l=function(){return dn.Date.now()},Rn=_l,Hl=/\s/;function Xl(t){for(var e=t.length;e--&&Hl.test(t.charAt(e)););return e}var ql=Xl,Wl=/^\s+/;function Kl(t){return t&&t.slice(0,ql(t)+1).replace(Wl,"")}var Zl=Kl,Ql=dn.Symbol,Fr=Ql,vo=Object.prototype,Jl=vo.hasOwnProperty,jl=vo.toString,jr=Fr?Fr.toStringTag:void 0;function ef(t){var e=Jl.call(t,jr),r=t[jr];try{t[jr]=void 0;var a=!0}catch{}var n=jl.call(t);return a&&(e?t[jr]=r:delete t[jr]),n}var tf=ef,rf=Object.prototype,af=rf.toString;function nf(t){return af.call(t)}var sf=nf,of="[object Null]",uf="[object Undefined]",Hi=Fr?Fr.toStringTag:void 0;function lf(t){return t==null?t===void 0?uf:of:Hi&&Hi in Object(t)?tf(t):sf(t)}var go=lf;function ff(t){return t!=null&&typeof t=="object"}var hf=ff,cf="[object Symbol]";function vf(t){return typeof t=="symbol"||hf(t)&&go(t)==cf}var Ca=vf,Xi=NaN,df=/^[-+]0x[0-9a-f]+$/i,gf=/^0b[01]+$/i,pf=/^0o[0-7]+$/i,yf=parseInt;function mf(t){if(typeof t=="number")return t;if(Ca(t))return Xi;if(vr(t)){var e=typeof t.valueOf=="function"?t.valueOf():t;t=vr(e)?e+"":e}if(typeof t!="string")return t===0?t:+t;t=Zl(t);var r=gf.test(t);return r||pf.test(t)?yf(t.slice(2),r?2:8):df.test(t)?Xi:+t}var qi=mf,bf="Expected a function",Ef=Math.max,wf=Math.min;function xf(t,e,r){var a,n,i,s,o,u,l=0,f=!1,h=!1,d=!0;if(typeof t!="function")throw new TypeError(bf);e=qi(e)||0,vr(r)&&(f=!!r.leading,h="maxWait"in r,i=h?Ef(qi(r.maxWait)||0,e):i,d="trailing"in r?!!r.trailing:d);function c(S){var E=a,w=n;return a=n=void 0,l=S,s=t.apply(w,E),s}function v(S){return l=S,o=setTimeout(y,e),f?c(S):s}function p(S){var E=S-u,w=S-l,x=e-E;return h?wf(x,i-w):x}function g(S){var E=S-u,w=S-l;return u===void 0||E>=e||E<0||h&&w>=i}function y(){var S=Rn();if(g(S))return b(S);o=setTimeout(y,p(S))}function b(S){return o=void 0,d&&a?c(S):(a=n=void 0,s)}function m(){o!==void 0&&clearTimeout(o),l=0,a=u=n=o=void 0}function T(){return o===void 0?s:b(Rn())}function C(){var S=Rn(),E=g(S);if(a=arguments,n=this,u=S,E){if(o===void 0)return v(u);if(h)return clearTimeout(o),o=setTimeout(y,e),c(u)}return o===void 0&&(o=setTimeout(y,e)),s}return C.cancel=m,C.flush=T,C}var gn=xf,kn=Ye?Ye.performance:null,po=kn&&kn.now?function(){return kn.now()}:function(){return Date.now()},Tf=function(){if(Ye){if(Ye.requestAnimationFrame)return function(t){Ye.requestAnimationFrame(t)};if(Ye.mozRequestAnimationFrame)return function(t){Ye.mozRequestAnimationFrame(t)};if(Ye.webkitRequestAnimationFrame)return function(t){Ye.webkitRequestAnimationFrame(t)};if(Ye.msRequestAnimationFrame)return function(t){Ye.msRequestAnimationFrame(t)}}return function(t){t&&setTimeout(function(){t(po())},1e3/60)}}(),rn=function(e){return Tf(e)},$t=po,Nr=9261,yo=65599,ia=5381,mo=function(e){for(var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:Nr,a=r,n;n=e.next(),!n.done;)a=a*yo+n.value|0;return a},ca=function(e){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:Nr;return r*yo+e|0},va=function(e){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:ia;return(r<<5)+r+e|0},Cf=function(e,r){return e*2097152+r},qt=function(e){return e[0]*2097152+e[1]},Ma=function(e,r){return[ca(e[0],r[0]),va(e[1],r[1])]},Df=function(e,r){var a={value:0,done:!1},n=0,i=e.length,s={next:function(){return n<i?a.value=e[n++]:a.done=!0,a}};return mo(s,r)},dr=function(e,r){var a={value:0,done:!1},n=0,i=e.length,s={next:function(){return n<i?a.value=e.charCodeAt(n++):a.done=!0,a}};return mo(s,r)},bo=function(){return Sf(arguments)},Sf=function(e){for(var r,a=0;a<e.length;a++){var n=e[a];a===0?r=dr(n):r=dr(n,r)}return r},Wi=!0,Lf=console.warn!=null,Af=console.trace!=null,yi=Number.MAX_SAFE_INTEGER||9007199254740991,Eo=function(){return!0},an=function(){return!1},Ki=function(){return 0},mi=function(){},ze=function(e){throw new Error(e)},wo=function(e){if(e!==void 0)Wi=!!e;else return Wi},Ne=function(e){wo()&&(Lf?console.warn(e):(console.log(e),Af&&console.trace()))},Of=function(e){return be({},e)},Pt=function(e){return e==null?e:Re(e)?e.slice():Ce(e)?Of(e):e},Nf=function(e){return e.slice()},xo=function(e,r){for(r=e="";e++<36;r+=e*51&52?(e^15?8^Math.random()*(e^20?16:4):4).toString(16):"-");return r},If={},To=function(){return If},tt=function(e){var r=Object.keys(e);return function(a){for(var n={},i=0;i<r.length;i++){var s=r[i],o=a==null?void 0:a[s];n[s]=o===void 0?e[s]:o}return n}},er=function(e,r,a){for(var n=e.length-1;n>=0;n--)e[n]===r&&e.splice(n,1)},bi=function(e){e.splice(0,e.length)},Mf=function(e,r){for(var a=0;a<r.length;a++){var n=r[a];e.push(n)}},At=function(e,r,a){return a&&(r=lo(a,r)),e[r]},Kt=function(e,r,a,n){a&&(r=lo(a,r)),e[r]=n},Rf=function(){function t(){vi(this,t),this._obj={}}return di(t,[{key:"set",value:function(r,a){return this._obj[r]=a,this}},{key:"delete",value:function(r){return this._obj[r]=void 0,this}},{key:"clear",value:function(){this._obj={}}},{key:"has",value:function(r){return this._obj[r]!==void 0}},{key:"get",value:function(r){return this._obj[r]}}]),t}(),Bt=typeof Map<"u"?Map:Rf,kf="undefined",Pf=function(){function t(e){if(vi(this,t),this._obj=Object.create(null),this.size=0,e!=null){var r;e.instanceString!=null&&e.instanceString()===this.instanceString()?r=e.toArray():r=e;for(var a=0;a<r.length;a++)this.add(r[a])}}return di(t,[{key:"instanceString",value:function(){return"set"}},{key:"add",value:function(r){var a=this._obj;a[r]!==1&&(a[r]=1,this.size++)}},{key:"delete",value:function(r){var a=this._obj;a[r]===1&&(a[r]=0,this.size--)}},{key:"clear",value:function(){this._obj=Object.create(null)}},{key:"has",value:function(r){return this._obj[r]===1}},{key:"toArray",value:function(){var r=this;return Object.keys(this._obj).filter(function(a){return r.has(a)})}},{key:"forEach",value:function(r,a){return this.toArray().forEach(r,a)}}]),t}(),Ur=(typeof Set>"u"?"undefined":Xe(Set))!==kf?Set:Pf,pn=function(e,r){var a=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!0;if(e===void 0||r===void 0||!gi(e)){ze("An element must have a core reference and parameters set");return}var n=r.group;if(n==null&&(r.data&&r.data.source!=null&&r.data.target!=null?n="edges":n="nodes"),n!=="nodes"&&n!=="edges"){ze("An element must be of type `nodes` or `edges`; you specified `"+n+"`");return}this.length=1,this[0]=this;var i=this._private={cy:e,single:!0,data:r.data||{},position:r.position||{x:0,y:0},autoWidth:void 0,autoHeight:void 0,autoPadding:void 0,compoundBoundsClean:!1,listeners:[],group:n,style:{},rstyle:{},styleCxts:[],styleKeys:{},removed:!0,selected:!!r.selected,selectable:r.selectable===void 0?!0:!!r.selectable,locked:!!r.locked,grabbed:!1,grabbable:r.grabbable===void 0?!0:!!r.grabbable,pannable:r.pannable===void 0?n==="edges":!!r.pannable,active:!1,classes:new Ur,animation:{current:[],queue:[]},rscratch:{},scratch:r.scratch||{},edges:[],children:[],parent:r.parent&&r.parent.isNode()?r.parent:null,traversalCache:{},backgrounding:!1,bbCache:null,bbCacheShift:{x:0,y:0},bodyBounds:null,overlayBounds:null,labelBounds:{all:null,source:null,target:null,main:null},arrowBounds:{source:null,target:null,"mid-source":null,"mid-target":null}};if(i.position.x==null&&(i.position.x=0),i.position.y==null&&(i.position.y=0),r.renderedPosition){var s=r.renderedPosition,o=e.pan(),u=e.zoom();i.position={x:(s.x-o.x)/u,y:(s.y-o.y)/u}}var l=[];Re(r.classes)?l=r.classes:de(r.classes)&&(l=r.classes.split(/\s+/));for(var f=0,h=l.length;f<h;f++){var d=l[f];!d||d===""||i.classes.add(d)}this.createEmitter();var c=r.style||r.css;c&&(Ne("Setting a `style` bypass at element creation should be done only when absolutely necessary.  Try to use the stylesheet instead."),this.style(c)),(a===void 0||a)&&this.restore()},Zi=function(e){return e={bfs:e.bfs||!e.dfs,dfs:e.dfs||!e.bfs},function(a,n,i){var s;Ce(a)&&!pt(a)&&(s=a,a=s.roots||s.root,n=s.visit,i=s.directed),i=arguments.length===2&&!Ge(n)?n:i,n=Ge(n)?n:function(){};for(var o=this._private.cy,u=a=de(a)?this.filter(a):a,l=[],f=[],h={},d={},c={},v=0,p,g=this.byGroup(),y=g.nodes,b=g.edges,m=0;m<u.length;m++){var T=u[m],C=T.id();T.isNode()&&(l.unshift(T),e.bfs&&(c[C]=!0,f.push(T)),d[C]=0)}for(var S=function(){var N=e.bfs?l.shift():l.pop(),O=N.id();if(e.dfs){if(c[O])return"continue";c[O]=!0,f.push(N)}var M=d[O],R=h[O],k=R!=null?R.source():null,P=R!=null?R.target():null,B=R==null?void 0:N.same(k)?P[0]:k[0],z=void 0;if(z=n(N,R,B,v++,M),z===!0)return p=N,"break";if(z===!1)return"break";for(var G=N.connectedEdges().filter(function(K){return(!i||K.source().same(N))&&b.has(K)}),F=0;F<G.length;F++){var U=G[F],Y=U.connectedNodes().filter(function(K){return!K.same(N)&&y.has(K)}),W=Y.id();Y.length!==0&&!c[W]&&(Y=Y[0],l.push(Y),e.bfs&&(c[W]=!0,f.push(Y)),h[W]=U,d[W]=d[O]+1)}};l.length!==0;){var E=S();if(E!=="continue"&&E==="break")break}for(var w=o.collection(),x=0;x<f.length;x++){var D=f[x],L=h[D.id()];L!=null&&w.push(L),w.push(D)}return{path:o.collection(w),found:o.collection(p)}}},da={breadthFirstSearch:Zi({bfs:!0}),depthFirstSearch:Zi({dfs:!0})};da.bfs=da.breadthFirstSearch;da.dfs=da.depthFirstSearch;var Bf=zl(function(t,e){(function(){var r,a,n,i,s,o,u,l,f,h,d,c,v,p,g;n=Math.floor,h=Math.min,a=function(y,b){return y<b?-1:y>b?1:0},f=function(y,b,m,T,C){var S;if(m==null&&(m=0),C==null&&(C=a),m<0)throw new Error("lo must be non-negative");for(T==null&&(T=y.length);m<T;)S=n((m+T)/2),C(b,y[S])<0?T=S:m=S+1;return[].splice.apply(y,[m,m-m].concat(b)),b},o=function(y,b,m){return m==null&&(m=a),y.push(b),p(y,0,y.length-1,m)},s=function(y,b){var m,T;return b==null&&(b=a),m=y.pop(),y.length?(T=y[0],y[0]=m,g(y,0,b)):T=m,T},l=function(y,b,m){var T;return m==null&&(m=a),T=y[0],y[0]=b,g(y,0,m),T},u=function(y,b,m){var T;return m==null&&(m=a),y.length&&m(y[0],b)<0&&(T=[y[0],b],b=T[0],y[0]=T[1],g(y,0,m)),b},i=function(y,b){var m,T,C,S,E,w;for(b==null&&(b=a),S=(function(){w=[];for(var x=0,D=n(y.length/2);0<=D?x<D:x>D;0<=D?x++:x--)w.push(x);return w}).apply(this).reverse(),E=[],T=0,C=S.length;T<C;T++)m=S[T],E.push(g(y,m,b));return E},v=function(y,b,m){var T;if(m==null&&(m=a),T=y.indexOf(b),T!==-1)return p(y,0,T,m),g(y,T,m)},d=function(y,b,m){var T,C,S,E,w;if(m==null&&(m=a),C=y.slice(0,b),!C.length)return C;for(i(C,m),w=y.slice(b),S=0,E=w.length;S<E;S++)T=w[S],u(C,T,m);return C.sort(m).reverse()},c=function(y,b,m){var T,C,S,E,w,x,D,L,A;if(m==null&&(m=a),b*10<=y.length){if(S=y.slice(0,b).sort(m),!S.length)return S;for(C=S[S.length-1],D=y.slice(b),E=0,x=D.length;E<x;E++)T=D[E],m(T,C)<0&&(f(S,T,0,null,m),S.pop(),C=S[S.length-1]);return S}for(i(y,m),A=[],w=0,L=h(b,y.length);0<=L?w<L:w>L;0<=L?++w:--w)A.push(s(y,m));return A},p=function(y,b,m,T){var C,S,E;for(T==null&&(T=a),C=y[m];m>b;){if(E=m-1>>1,S=y[E],T(C,S)<0){y[m]=S,m=E;continue}break}return y[m]=C},g=function(y,b,m){var T,C,S,E,w;for(m==null&&(m=a),C=y.length,w=b,S=y[b],T=2*b+1;T<C;)E=T+1,E<C&&!(m(y[T],y[E])<0)&&(T=E),y[b]=y[T],b=T,T=2*b+1;return y[b]=S,p(y,w,b,m)},r=function(){y.push=o,y.pop=s,y.replace=l,y.pushpop=u,y.heapify=i,y.updateItem=v,y.nlargest=d,y.nsmallest=c;function y(b){this.cmp=b??a,this.nodes=[]}return y.prototype.push=function(b){return o(this.nodes,b,this.cmp)},y.prototype.pop=function(){return s(this.nodes,this.cmp)},y.prototype.peek=function(){return this.nodes[0]},y.prototype.contains=function(b){return this.nodes.indexOf(b)!==-1},y.prototype.replace=function(b){return l(this.nodes,b,this.cmp)},y.prototype.pushpop=function(b){return u(this.nodes,b,this.cmp)},y.prototype.heapify=function(){return i(this.nodes,this.cmp)},y.prototype.updateItem=function(b){return v(this.nodes,b,this.cmp)},y.prototype.clear=function(){return this.nodes=[]},y.prototype.empty=function(){return this.nodes.length===0},y.prototype.size=function(){return this.nodes.length},y.prototype.clone=function(){var b;return b=new y,b.nodes=this.nodes.slice(0),b},y.prototype.toArray=function(){return this.nodes.slice(0)},y.prototype.insert=y.prototype.push,y.prototype.top=y.prototype.peek,y.prototype.front=y.prototype.peek,y.prototype.has=y.prototype.contains,y.prototype.copy=y.prototype.clone,y}(),function(y,b){return t.exports=b()}(this,function(){return r})}).call(na)}),Da=Bf,Ff=tt({root:null,weight:function(e){return 1},directed:!1}),Gf={dijkstra:function(e){if(!Ce(e)){var r=arguments;e={root:r[0],weight:r[1],directed:r[2]}}var a=Ff(e),n=a.root,i=a.weight,s=a.directed,o=this,u=i,l=de(n)?this.filter(n)[0]:n[0],f={},h={},d={},c=this.byGroup(),v=c.nodes,p=c.edges;p.unmergeBy(function(M){return M.isLoop()});for(var g=function(R){return f[R.id()]},y=function(R,k){f[R.id()]=k,b.updateItem(R)},b=new Da(function(M,R){return g(M)-g(R)}),m=0;m<v.length;m++){var T=v[m];f[T.id()]=T.same(l)?0:1/0,b.push(T)}for(var C=function(R,k){for(var P=(s?R.edgesTo(k):R.edgesWith(k)).intersect(p),B=1/0,z,G=0;G<P.length;G++){var F=P[G],U=u(F);(U<B||!z)&&(B=U,z=F)}return{edge:z,dist:B}};b.size()>0;){var S=b.pop(),E=g(S),w=S.id();if(d[w]=E,E!==1/0)for(var x=S.neighborhood().intersect(v),D=0;D<x.length;D++){var L=x[D],A=L.id(),N=C(S,L),O=E+N.dist;O<g(L)&&(y(L,O),h[A]={node:S,edge:N.edge})}}return{distanceTo:function(R){var k=de(R)?v.filter(R)[0]:R[0];return d[k.id()]},pathTo:function(R){var k=de(R)?v.filter(R)[0]:R[0],P=[],B=k,z=B.id();if(k.length>0)for(P.unshift(k);h[z];){var G=h[z];P.unshift(G.edge),P.unshift(G.node),B=G.node,z=B.id()}return o.spawn(P)}}}},zf={kruskal:function(e){e=e||function(m){return 1};for(var r=this.byGroup(),a=r.nodes,n=r.edges,i=a.length,s=new Array(i),o=a,u=function(T){for(var C=0;C<s.length;C++){var S=s[C];if(S.has(T))return C}},l=0;l<i;l++)s[l]=this.spawn(a[l]);for(var f=n.sort(function(m,T){return e(m)-e(T)}),h=0;h<f.length;h++){var d=f[h],c=d.source()[0],v=d.target()[0],p=u(c),g=u(v),y=s[p],b=s[g];p!==g&&(o.merge(d),y.merge(b),s.splice(g,1))}return o}},Vf=tt({root:null,goal:null,weight:function(e){return 1},heuristic:function(e){return 0},directed:!1}),Uf={aStar:function(e){var r=this.cy(),a=Vf(e),n=a.root,i=a.goal,s=a.heuristic,o=a.directed,u=a.weight;n=r.collection(n)[0],i=r.collection(i)[0];var l=n.id(),f=i.id(),h={},d={},c={},v=new Da(function(z,G){return d[z.id()]-d[G.id()]}),p=new Ur,g={},y={},b=function(G,F){v.push(G),p.add(F)},m,T,C=function(){m=v.pop(),T=m.id(),p.delete(T)},S=function(G){return p.has(G)};b(n,l),h[l]=0,d[l]=s(n);for(var E=0;v.size()>0;){if(C(),E++,T===f){for(var w=[],x=i,D=f,L=y[D];w.unshift(x),L!=null&&w.unshift(L),x=g[D],x!=null;)D=x.id(),L=y[D];return{found:!0,distance:h[T],path:this.spawn(w),steps:E}}c[T]=!0;for(var A=m._private.edges,N=0;N<A.length;N++){var O=A[N];if(this.hasElementWithId(O.id())&&!(o&&O.data("source")!==T)){var M=O.source(),R=O.target(),k=M.id()!==T?M:R,P=k.id();if(this.hasElementWithId(P)&&!c[P]){var B=h[T]+u(O);if(!S(P)){h[P]=B,d[P]=B+s(k),b(k,P),g[P]=m,y[P]=O;continue}B<h[P]&&(h[P]=B,d[P]=B+s(k),g[P]=m,y[P]=O)}}}}return{found:!1,distance:void 0,path:void 0,steps:E}}},$f=tt({weight:function(e){return 1},directed:!1}),Yf={floydWarshall:function(e){for(var r=this.cy(),a=$f(e),n=a.weight,i=a.directed,s=n,o=this.byGroup(),u=o.nodes,l=o.edges,f=u.length,h=f*f,d=function(U){return u.indexOf(U)},c=function(U){return u[U]},v=new Array(h),p=0;p<h;p++){var g=p%f,y=(p-g)/f;y===g?v[p]=0:v[p]=1/0}for(var b=new Array(h),m=new Array(h),T=0;T<l.length;T++){var C=l[T],S=C.source()[0],E=C.target()[0];if(S!==E){var w=d(S),x=d(E),D=w*f+x,L=s(C);if(v[D]>L&&(v[D]=L,b[D]=x,m[D]=C),!i){var A=x*f+w;!i&&v[A]>L&&(v[A]=L,b[A]=w,m[A]=C)}}}for(var N=0;N<f;N++)for(var O=0;O<f;O++)for(var M=O*f+N,R=0;R<f;R++){var k=O*f+R,P=N*f+R;v[M]+v[P]<v[k]&&(v[k]=v[M]+v[P],b[k]=b[M])}var B=function(U){return(de(U)?r.filter(U):U)[0]},z=function(U){return d(B(U))},G={distance:function(U,Y){var W=z(U),K=z(Y);return v[W*f+K]},path:function(U,Y){var W=z(U),K=z(Y),j=c(W);if(W===K)return j.collection();if(b[W*f+K]==null)return r.collection();var _=r.collection(),V=W,H;for(_.merge(j);W!==K;)V=W,W=b[W*f+K],H=m[V*f+W],_.merge(H),_.merge(c(W));return _}};return G}},_f=tt({weight:function(e){return 1},directed:!1,root:null}),Hf={bellmanFord:function(e){var r=this,a=_f(e),n=a.weight,i=a.directed,s=a.root,o=n,u=this,l=this.cy(),f=this.byGroup(),h=f.edges,d=f.nodes,c=d.length,v=new Bt,p=!1,g=[];s=l.collection(s)[0],h.unmergeBy(function(ve){return ve.isLoop()});for(var y=h.length,b=function(fe){var pe=v.get(fe.id());return pe||(pe={},v.set(fe.id(),pe)),pe},m=function(fe){return(de(fe)?l.$(fe):fe)[0]},T=function(fe){return b(m(fe)).dist},C=function(fe){for(var pe=arguments.length>1&&arguments[1]!==void 0?arguments[1]:s,Ae=m(fe),xe=[],we=Ae;;){if(we==null)return r.spawn();var De=b(we),ee=De.edge,I=De.pred;if(xe.unshift(we[0]),we.same(pe)&&xe.length>0)break;ee!=null&&xe.unshift(ee),we=I}return u.spawn(xe)},S=0;S<c;S++){var E=d[S],w=b(E);E.same(s)?w.dist=0:w.dist=1/0,w.pred=null,w.edge=null}for(var x=!1,D=function(fe,pe,Ae,xe,we,De){var ee=xe.dist+De;ee<we.dist&&!Ae.same(xe.edge)&&(we.dist=ee,we.pred=fe,we.edge=Ae,x=!0)},L=1;L<c;L++){x=!1;for(var A=0;A<y;A++){var N=h[A],O=N.source(),M=N.target(),R=o(N),k=b(O),P=b(M);D(O,M,N,k,P,R),i||D(M,O,N,P,k,R)}if(!x)break}if(x)for(var B=[],z=0;z<y;z++){var G=h[z],F=G.source(),U=G.target(),Y=o(G),W=b(F).dist,K=b(U).dist;if(W+Y<K||!i&&K+Y<W)if(p||(Ne("Graph contains a negative weight cycle for Bellman-Ford"),p=!0),e.findNegativeWeightCycles!==!1){var j=[];W+Y<K&&j.push(F),!i&&K+Y<W&&j.push(U);for(var _=j.length,V=0;V<_;V++){var H=j[V],Q=[H];Q.push(b(H).edge);for(var ne=b(H).pred;Q.indexOf(ne)===-1;)Q.push(ne),Q.push(b(ne).edge),ne=b(ne).pred;Q=Q.slice(Q.indexOf(ne));for(var ce=Q[0].id(),te=0,se=2;se<Q.length;se+=2)Q[se].id()<ce&&(ce=Q[se].id(),te=se);Q=Q.slice(te).concat(Q.slice(0,te)),Q.push(Q[0]);var ue=Q.map(function(ve){return ve.id()}).join(",");B.indexOf(ue)===-1&&(g.push(u.spawn(Q)),B.push(ue))}}else break}return{distanceTo:T,pathTo:C,hasNegativeWeightCycle:p,negativeWeightCycles:g}}},Xf=Math.sqrt(2),qf=function(e,r,a){a.length===0&&ze("Karger-Stein must be run on a connected (sub)graph");for(var n=a[e],i=n[1],s=n[2],o=r[i],u=r[s],l=a,f=l.length-1;f>=0;f--){var h=l[f],d=h[1],c=h[2];(r[d]===o&&r[c]===u||r[d]===u&&r[c]===o)&&l.splice(f,1)}for(var v=0;v<l.length;v++){var p=l[v];p[1]===u?(l[v]=p.slice(),l[v][1]=o):p[2]===u&&(l[v]=p.slice(),l[v][2]=o)}for(var g=0;g<r.length;g++)r[g]===u&&(r[g]=o);return l},Pn=function(e,r,a,n){for(;a>n;){var i=Math.floor(Math.random()*r.length);r=qf(i,e,r),a--}return r},Wf={kargerStein:function(){var e=this,r=this.byGroup(),a=r.nodes,n=r.edges;n.unmergeBy(function(P){return P.isLoop()});var i=a.length,s=n.length,o=Math.ceil(Math.pow(Math.log(i)/Math.LN2,2)),u=Math.floor(i/Xf);if(i<2){ze("At least 2 nodes are required for Karger-Stein algorithm");return}for(var l=[],f=0;f<s;f++){var h=n[f];l.push([f,a.indexOf(h.source()),a.indexOf(h.target())])}for(var d=1/0,c=[],v=new Array(i),p=new Array(i),g=new Array(i),y=function(B,z){for(var G=0;G<i;G++)z[G]=B[G]},b=0;b<=o;b++){for(var m=0;m<i;m++)p[m]=m;var T=Pn(p,l.slice(),i,u),C=T.slice();y(p,g);var S=Pn(p,T,u,2),E=Pn(g,C,u,2);S.length<=E.length&&S.length<d?(d=S.length,c=S,y(p,v)):E.length<=S.length&&E.length<d&&(d=E.length,c=E,y(g,v))}for(var w=this.spawn(c.map(function(P){return n[P[0]]})),x=this.spawn(),D=this.spawn(),L=v[0],A=0;A<v.length;A++){var N=v[A],O=a[A];N===L?x.merge(O):D.merge(O)}var M=function(B){var z=e.spawn();return B.forEach(function(G){z.merge(G),G.connectedEdges().forEach(function(F){e.contains(F)&&!w.contains(F)&&z.merge(F)})}),z},R=[M(x),M(D)],k={cut:w,components:R,partition1:x,partition2:D};return k}},Kf=function(e){return{x:e.x,y:e.y}},yn=function(e,r,a){return{x:e.x*r+a.x,y:e.y*r+a.y}},Co=function(e,r,a){return{x:(e.x-a.x)/r,y:(e.y-a.y)/r}},Ir=function(e){return{x:e[0],y:e[1]}},Zf=function(e){for(var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,a=arguments.length>2&&arguments[2]!==void 0?arguments[2]:e.length,n=1/0,i=r;i<a;i++){var s=e[i];isFinite(s)&&(n=Math.min(s,n))}return n},Qf=function(e){for(var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,a=arguments.length>2&&arguments[2]!==void 0?arguments[2]:e.length,n=-1/0,i=r;i<a;i++){var s=e[i];isFinite(s)&&(n=Math.max(s,n))}return n},Jf=function(e){for(var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,a=arguments.length>2&&arguments[2]!==void 0?arguments[2]:e.length,n=0,i=0,s=r;s<a;s++){var o=e[s];isFinite(o)&&(n+=o,i++)}return n/i},jf=function(e){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,a=arguments.length>2&&arguments[2]!==void 0?arguments[2]:e.length,n=arguments.length>3&&arguments[3]!==void 0?arguments[3]:!0,i=arguments.length>4&&arguments[4]!==void 0?arguments[4]:!0,s=arguments.length>5&&arguments[5]!==void 0?arguments[5]:!0;n?e=e.slice(r,a):(a<e.length&&e.splice(a,e.length-a),r>0&&e.splice(0,r));for(var o=0,u=e.length-1;u>=0;u--){var l=e[u];s?isFinite(l)||(e[u]=-1/0,o++):e.splice(u,1)}i&&e.sort(function(d,c){return d-c});var f=e.length,h=Math.floor(f/2);return f%2!==0?e[h+1+o]:(e[h-1+o]+e[h+o])/2},eh=function(e){return Math.PI*e/180},Ra=function(e,r){return Math.atan2(r,e)-Math.PI/2},Ei=Math.log2||function(t){return Math.log(t)/Math.log(2)},Do=function(e){return e>0?1:e<0?-1:0},gr=function(e,r){return Math.sqrt(ur(e,r))},ur=function(e,r){var a=r.x-e.x,n=r.y-e.y;return a*a+n*n},th=function(e){for(var r=e.length,a=0,n=0;n<r;n++)a+=e[n];for(var i=0;i<r;i++)e[i]=e[i]/a;return e},Ke=function(e,r,a,n){return(1-n)*(1-n)*e+2*(1-n)*n*r+n*n*a},Rr=function(e,r,a,n){return{x:Ke(e.x,r.x,a.x,n),y:Ke(e.y,r.y,a.y,n)}},rh=function(e,r,a,n){var i={x:r.x-e.x,y:r.y-e.y},s=gr(e,r),o={x:i.x/s,y:i.y/s};return a=a??0,n=n??a*s,{x:e.x+o.x*n,y:e.y+o.y*n}},ga=function(e,r,a){return Math.max(e,Math.min(a,r))},gt=function(e){if(e==null)return{x1:1/0,y1:1/0,x2:-1/0,y2:-1/0,w:0,h:0};if(e.x1!=null&&e.y1!=null){if(e.x2!=null&&e.y2!=null&&e.x2>=e.x1&&e.y2>=e.y1)return{x1:e.x1,y1:e.y1,x2:e.x2,y2:e.y2,w:e.x2-e.x1,h:e.y2-e.y1};if(e.w!=null&&e.h!=null&&e.w>=0&&e.h>=0)return{x1:e.x1,y1:e.y1,x2:e.x1+e.w,y2:e.y1+e.h,w:e.w,h:e.h}}},ah=function(e){return{x1:e.x1,x2:e.x2,w:e.w,y1:e.y1,y2:e.y2,h:e.h}},nh=function(e){e.x1=1/0,e.y1=1/0,e.x2=-1/0,e.y2=-1/0,e.w=0,e.h=0},ih=function(e,r,a){return{x1:e.x1+r,x2:e.x2+r,y1:e.y1+a,y2:e.y2+a,w:e.w,h:e.h}},So=function(e,r){e.x1=Math.min(e.x1,r.x1),e.x2=Math.max(e.x2,r.x2),e.w=e.x2-e.x1,e.y1=Math.min(e.y1,r.y1),e.y2=Math.max(e.y2,r.y2),e.h=e.y2-e.y1},sh=function(e,r,a){e.x1=Math.min(e.x1,r),e.x2=Math.max(e.x2,r),e.w=e.x2-e.x1,e.y1=Math.min(e.y1,a),e.y2=Math.max(e.y2,a),e.h=e.y2-e.y1},_a=function(e){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0;return e.x1-=r,e.x2+=r,e.y1-=r,e.y2+=r,e.w=e.x2-e.x1,e.h=e.y2-e.y1,e},Ha=function(e){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:[0],a,n,i,s;if(r.length===1)a=n=i=s=r[0];else if(r.length===2)a=i=r[0],s=n=r[1];else if(r.length===4){var o=St(r,4);a=o[0],n=o[1],i=o[2],s=o[3]}return e.x1-=s,e.x2+=n,e.y1-=a,e.y2+=i,e.w=e.x2-e.x1,e.h=e.y2-e.y1,e},Qi=function(e,r){e.x1=r.x1,e.y1=r.y1,e.x2=r.x2,e.y2=r.y2,e.w=e.x2-e.x1,e.h=e.y2-e.y1},wi=function(e,r){return!(e.x1>r.x2||r.x1>e.x2||e.x2<r.x1||r.x2<e.x1||e.y2<r.y1||r.y2<e.y1||e.y1>r.y2||r.y1>e.y2)},Gr=function(e,r,a){return e.x1<=r&&r<=e.x2&&e.y1<=a&&a<=e.y2},oh=function(e,r){return Gr(e,r.x,r.y)},Lo=function(e,r){return Gr(e,r.x1,r.y1)&&Gr(e,r.x2,r.y2)},Ao=function(e,r,a,n,i,s,o){var u=arguments.length>7&&arguments[7]!==void 0?arguments[7]:"auto",l=u==="auto"?pr(i,s):u,f=i/2,h=s/2;l=Math.min(l,f,h);var d=l!==f,c=l!==h,v;if(d){var p=a-f+l-o,g=n-h-o,y=a+f-l+o,b=g;if(v=Zt(e,r,a,n,p,g,y,b,!1),v.length>0)return v}if(c){var m=a+f+o,T=n-h+l-o,C=m,S=n+h-l+o;if(v=Zt(e,r,a,n,m,T,C,S,!1),v.length>0)return v}if(d){var E=a-f+l-o,w=n+h+o,x=a+f-l+o,D=w;if(v=Zt(e,r,a,n,E,w,x,D,!1),v.length>0)return v}if(c){var L=a-f-o,A=n-h+l-o,N=L,O=n+h-l+o;if(v=Zt(e,r,a,n,L,A,N,O,!1),v.length>0)return v}var M;{var R=a-f+l,k=n-h+l;if(M=sa(e,r,a,n,R,k,l+o),M.length>0&&M[0]<=R&&M[1]<=k)return[M[0],M[1]]}{var P=a+f-l,B=n-h+l;if(M=sa(e,r,a,n,P,B,l+o),M.length>0&&M[0]>=P&&M[1]<=B)return[M[0],M[1]]}{var z=a+f-l,G=n+h-l;if(M=sa(e,r,a,n,z,G,l+o),M.length>0&&M[0]>=z&&M[1]>=G)return[M[0],M[1]]}{var F=a-f+l,U=n+h-l;if(M=sa(e,r,a,n,F,U,l+o),M.length>0&&M[0]<=F&&M[1]>=U)return[M[0],M[1]]}return[]},uh=function(e,r,a,n,i,s,o){var u=o,l=Math.min(a,i),f=Math.max(a,i),h=Math.min(n,s),d=Math.max(n,s);return l-u<=e&&e<=f+u&&h-u<=r&&r<=d+u},lh=function(e,r,a,n,i,s,o,u,l){var f={x1:Math.min(a,o,i)-l,x2:Math.max(a,o,i)+l,y1:Math.min(n,u,s)-l,y2:Math.max(n,u,s)+l};return!(e<f.x1||e>f.x2||r<f.y1||r>f.y2)},fh=function(e,r,a,n){a-=n;var i=r*r-4*e*a;if(i<0)return[];var s=Math.sqrt(i),o=2*e,u=(-r+s)/o,l=(-r-s)/o;return[u,l]},hh=function(e,r,a,n,i){var s=1e-5;e===0&&(e=s),r/=e,a/=e,n/=e;var o,u,l,f,h,d,c,v;if(u=(3*a-r*r)/9,l=-(27*n)+r*(9*a-2*(r*r)),l/=54,o=u*u*u+l*l,i[1]=0,c=r/3,o>0){h=l+Math.sqrt(o),h=h<0?-Math.pow(-h,1/3):Math.pow(h,1/3),d=l-Math.sqrt(o),d=d<0?-Math.pow(-d,1/3):Math.pow(d,1/3),i[0]=-c+h+d,c+=(h+d)/2,i[4]=i[2]=-c,c=Math.sqrt(3)*(-d+h)/2,i[3]=c,i[5]=-c;return}if(i[5]=i[3]=0,o===0){v=l<0?-Math.pow(-l,1/3):Math.pow(l,1/3),i[0]=-c+2*v,i[4]=i[2]=-(v+c);return}u=-u,f=u*u*u,f=Math.acos(l/Math.sqrt(f)),v=2*Math.sqrt(u),i[0]=-c+v*Math.cos(f/3),i[2]=-c+v*Math.cos((f+2*Math.PI)/3),i[4]=-c+v*Math.cos((f+4*Math.PI)/3)},ch=function(e,r,a,n,i,s,o,u){var l=1*a*a-4*a*i+2*a*o+4*i*i-4*i*o+o*o+n*n-4*n*s+2*n*u+4*s*s-4*s*u+u*u,f=1*9*a*i-3*a*a-3*a*o-6*i*i+3*i*o+9*n*s-3*n*n-3*n*u-6*s*s+3*s*u,h=1*3*a*a-6*a*i+a*o-a*e+2*i*i+2*i*e-o*e+3*n*n-6*n*s+n*u-n*r+2*s*s+2*s*r-u*r,d=1*a*i-a*a+a*e-i*e+n*s-n*n+n*r-s*r,c=[];hh(l,f,h,d,c);for(var v=1e-7,p=[],g=0;g<6;g+=2)Math.abs(c[g+1])<v&&c[g]>=0&&c[g]<=1&&p.push(c[g]);p.push(1),p.push(0);for(var y=-1,b,m,T,C=0;C<p.length;C++)b=Math.pow(1-p[C],2)*a+2*(1-p[C])*p[C]*i+p[C]*p[C]*o,m=Math.pow(1-p[C],2)*n+2*(1-p[C])*p[C]*s+p[C]*p[C]*u,T=Math.pow(b-e,2)+Math.pow(m-r,2),y>=0?T<y&&(y=T):y=T;return y},vh=function(e,r,a,n,i,s){var o=[e-a,r-n],u=[i-a,s-n],l=u[0]*u[0]+u[1]*u[1],f=o[0]*o[0]+o[1]*o[1],h=o[0]*u[0]+o[1]*u[1],d=h*h/l;return h<0?f:d>l?(e-i)*(e-i)+(r-s)*(r-s):f-d},dt=function(e,r,a){for(var n,i,s,o,u,l=0,f=0;f<a.length/2;f++)if(n=a[f*2],i=a[f*2+1],f+1<a.length/2?(s=a[(f+1)*2],o=a[(f+1)*2+1]):(s=a[(f+1-a.length/2)*2],o=a[(f+1-a.length/2)*2+1]),!(n==e&&s==e))if(n>=e&&e>=s||n<=e&&e<=s)u=(e-n)/(s-n)*(o-i)+i,u>r&&l++;else continue;return l%2!==0},Yt=function(e,r,a,n,i,s,o,u,l){var f=new Array(a.length),h;u[0]!=null?(h=Math.atan(u[1]/u[0]),u[0]<0?h=h+Math.PI/2:h=-h-Math.PI/2):h=u;for(var d=Math.cos(-h),c=Math.sin(-h),v=0;v<f.length/2;v++)f[v*2]=s/2*(a[v*2]*d-a[v*2+1]*c),f[v*2+1]=o/2*(a[v*2+1]*d+a[v*2]*c),f[v*2]+=n,f[v*2+1]+=i;var p;if(l>0){var g=sn(f,-l);p=nn(g)}else p=f;return dt(e,r,p)},dh=function(e,r,a,n,i,s,o,u){for(var l=new Array(a.length*2),f=0;f<u.length;f++){var h=u[f];l[f*4+0]=h.startX,l[f*4+1]=h.startY,l[f*4+2]=h.stopX,l[f*4+3]=h.stopY;var d=Math.pow(h.cx-e,2)+Math.pow(h.cy-r,2);if(d<=Math.pow(h.radius,2))return!0}return dt(e,r,l)},nn=function(e){for(var r=new Array(e.length/2),a,n,i,s,o,u,l,f,h=0;h<e.length/4;h++){a=e[h*4],n=e[h*4+1],i=e[h*4+2],s=e[h*4+3],h<e.length/4-1?(o=e[(h+1)*4],u=e[(h+1)*4+1],l=e[(h+1)*4+2],f=e[(h+1)*4+3]):(o=e[0],u=e[1],l=e[2],f=e[3]);var d=Zt(a,n,i,s,o,u,l,f,!0);r[h*2]=d[0],r[h*2+1]=d[1]}return r},sn=function(e,r){for(var a=new Array(e.length*2),n,i,s,o,u=0;u<e.length/2;u++){n=e[u*2],i=e[u*2+1],u<e.length/2-1?(s=e[(u+1)*2],o=e[(u+1)*2+1]):(s=e[0],o=e[1]);var l=o-i,f=-(s-n),h=Math.sqrt(l*l+f*f),d=l/h,c=f/h;a[u*4]=n+d*r,a[u*4+1]=i+c*r,a[u*4+2]=s+d*r,a[u*4+3]=o+c*r}return a},gh=function(e,r,a,n,i,s){var o=a-e,u=n-r;o/=i,u/=s;var l=Math.sqrt(o*o+u*u),f=l-1;if(f<0)return[];var h=f/l;return[(a-e)*h+e,(n-r)*h+r]},cr=function(e,r,a,n,i,s,o){return e-=i,r-=s,e/=a/2+o,r/=n/2+o,e*e+r*r<=1},sa=function(e,r,a,n,i,s,o){var u=[a-e,n-r],l=[e-i,r-s],f=u[0]*u[0]+u[1]*u[1],h=2*(l[0]*u[0]+l[1]*u[1]),d=l[0]*l[0]+l[1]*l[1]-o*o,c=h*h-4*f*d;if(c<0)return[];var v=(-h+Math.sqrt(c))/(2*f),p=(-h-Math.sqrt(c))/(2*f),g=Math.min(v,p),y=Math.max(v,p),b=[];if(g>=0&&g<=1&&b.push(g),y>=0&&y<=1&&b.push(y),b.length===0)return[];var m=b[0]*u[0]+e,T=b[0]*u[1]+r;if(b.length>1){if(b[0]==b[1])return[m,T];var C=b[1]*u[0]+e,S=b[1]*u[1]+r;return[m,T,C,S]}else return[m,T]},Bn=function(e,r,a){return r<=e&&e<=a||a<=e&&e<=r?e:e<=r&&r<=a||a<=r&&r<=e?r:a},Zt=function(e,r,a,n,i,s,o,u,l){var f=e-i,h=a-e,d=o-i,c=r-s,v=n-r,p=u-s,g=d*c-p*f,y=h*c-v*f,b=p*h-d*v;if(b!==0){var m=g/b,T=y/b,C=.001,S=0-C,E=1+C;return S<=m&&m<=E&&S<=T&&T<=E?[e+m*h,r+m*v]:l?[e+m*h,r+m*v]:[]}else return g===0||y===0?Bn(e,a,o)===o?[o,u]:Bn(e,a,i)===i?[i,s]:Bn(i,o,a)===a?[a,n]:[]:[]},pa=function(e,r,a,n,i,s,o,u){var l=[],f,h=new Array(a.length),d=!0;s==null&&(d=!1);var c;if(d){for(var v=0;v<h.length/2;v++)h[v*2]=a[v*2]*s+n,h[v*2+1]=a[v*2+1]*o+i;if(u>0){var p=sn(h,-u);c=nn(p)}else c=h}else c=a;for(var g,y,b,m,T=0;T<c.length/2;T++)g=c[T*2],y=c[T*2+1],T<c.length/2-1?(b=c[(T+1)*2],m=c[(T+1)*2+1]):(b=c[0],m=c[1]),f=Zt(e,r,n,i,g,y,b,m),f.length!==0&&l.push(f[0],f[1]);return l},ph=function(e,r,a,n,i,s,o,u,l){var f=[],h,d=new Array(a.length*2);l.forEach(function(b,m){m===0?(d[d.length-2]=b.startX,d[d.length-1]=b.startY):(d[m*4-2]=b.startX,d[m*4-1]=b.startY),d[m*4]=b.stopX,d[m*4+1]=b.stopY,h=sa(e,r,n,i,b.cx,b.cy,b.radius),h.length!==0&&f.push(h[0],h[1])});for(var c=0;c<d.length/4;c++)h=Zt(e,r,n,i,d[c*4],d[c*4+1],d[c*4+2],d[c*4+3],!1),h.length!==0&&f.push(h[0],h[1]);if(f.length>2){for(var v=[f[0],f[1]],p=Math.pow(v[0]-e,2)+Math.pow(v[1]-r,2),g=1;g<f.length/2;g++){var y=Math.pow(f[g*2]-e,2)+Math.pow(f[g*2+1]-r,2);y<=p&&(v[0]=f[g*2],v[1]=f[g*2+1],p=y)}return v}return f},ka=function(e,r,a){var n=[e[0]-r[0],e[1]-r[1]],i=Math.sqrt(n[0]*n[0]+n[1]*n[1]),s=(i-a)/i;return s<0&&(s=1e-5),[r[0]+s*n[0],r[1]+s*n[1]]},ht=function(e,r){var a=Wn(e,r);return a=Oo(a),a},Oo=function(e){for(var r,a,n=e.length/2,i=1/0,s=1/0,o=-1/0,u=-1/0,l=0;l<n;l++)r=e[2*l],a=e[2*l+1],i=Math.min(i,r),o=Math.max(o,r),s=Math.min(s,a),u=Math.max(u,a);for(var f=2/(o-i),h=2/(u-s),d=0;d<n;d++)r=e[2*d]=e[2*d]*f,a=e[2*d+1]=e[2*d+1]*h,i=Math.min(i,r),o=Math.max(o,r),s=Math.min(s,a),u=Math.max(u,a);if(s<-1)for(var c=0;c<n;c++)a=e[2*c+1]=e[2*c+1]+(-1-s);return e},Wn=function(e,r){var a=1/e*2*Math.PI,n=e%2===0?Math.PI/2+a/2:Math.PI/2;n+=r;for(var i=new Array(e*2),s,o=0;o<e;o++)s=o*a+n,i[2*o]=Math.cos(s),i[2*o+1]=Math.sin(-s);return i},pr=function(e,r){return Math.min(e/4,r/4,8)},No=function(e,r){return Math.min(e/10,r/10,8)},xi=function(){return 8},yh=function(e,r,a){return[e-2*r+a,2*(r-e),e]},Kn=function(e,r){return{heightOffset:Math.min(15,.05*r),widthOffset:Math.min(100,.25*e),ctrlPtOffsetPct:.05}},mh=tt({dampingFactor:.8,precision:1e-6,iterations:200,weight:function(e){return 1}}),bh={pageRank:function(e){for(var r=mh(e),a=r.dampingFactor,n=r.precision,i=r.iterations,s=r.weight,o=this._private.cy,u=this.byGroup(),l=u.nodes,f=u.edges,h=l.length,d=h*h,c=f.length,v=new Array(d),p=new Array(h),g=(1-a)/h,y=0;y<h;y++){for(var b=0;b<h;b++){var m=y*h+b;v[m]=0}p[y]=0}for(var T=0;T<c;T++){var C=f[T],S=C.data("source"),E=C.data("target");if(S!==E){var w=l.indexOfId(S),x=l.indexOfId(E),D=s(C),L=x*h+w;v[L]+=D,p[w]+=D}}for(var A=1/h+g,N=0;N<h;N++)if(p[N]===0)for(var O=0;O<h;O++){var M=O*h+N;v[M]=A}else for(var R=0;R<h;R++){var k=R*h+N;v[k]=v[k]/p[N]+g}for(var P=new Array(h),B=new Array(h),z,G=0;G<h;G++)P[G]=1;for(var F=0;F<i;F++){for(var U=0;U<h;U++)B[U]=0;for(var Y=0;Y<h;Y++)for(var W=0;W<h;W++){var K=Y*h+W;B[Y]+=v[K]*P[W]}th(B),z=P,P=B,B=z;for(var j=0,_=0;_<h;_++){var V=z[_]-P[_];j+=V*V}if(j<n)break}var H={rank:function(ne){return ne=o.collection(ne)[0],P[l.indexOf(ne)]}};return H}},Ji=tt({root:null,weight:function(e){return 1},directed:!1,alpha:0}),kr={degreeCentralityNormalized:function(e){e=Ji(e);var r=this.cy(),a=this.nodes(),n=a.length;if(e.directed){for(var f={},h={},d=0,c=0,v=0;v<n;v++){var p=a[v],g=p.id();e.root=p;var y=this.degreeCentrality(e);d<y.indegree&&(d=y.indegree),c<y.outdegree&&(c=y.outdegree),f[g]=y.indegree,h[g]=y.outdegree}return{indegree:function(m){return d==0?0:(de(m)&&(m=r.filter(m)),f[m.id()]/d)},outdegree:function(m){return c===0?0:(de(m)&&(m=r.filter(m)),h[m.id()]/c)}}}else{for(var i={},s=0,o=0;o<n;o++){var u=a[o];e.root=u;var l=this.degreeCentrality(e);s<l.degree&&(s=l.degree),i[u.id()]=l.degree}return{degree:function(m){return s===0?0:(de(m)&&(m=r.filter(m)),i[m.id()]/s)}}}},degreeCentrality:function(e){e=Ji(e);var r=this.cy(),a=this,n=e,i=n.root,s=n.weight,o=n.directed,u=n.alpha;if(i=r.collection(i)[0],o){for(var c=i.connectedEdges(),v=c.filter(function(S){return S.target().same(i)&&a.has(S)}),p=c.filter(function(S){return S.source().same(i)&&a.has(S)}),g=v.length,y=p.length,b=0,m=0,T=0;T<v.length;T++)b+=s(v[T]);for(var C=0;C<p.length;C++)m+=s(p[C]);return{indegree:Math.pow(g,1-u)*Math.pow(b,u),outdegree:Math.pow(y,1-u)*Math.pow(m,u)}}else{for(var l=i.connectedEdges().intersection(a),f=l.length,h=0,d=0;d<l.length;d++)h+=s(l[d]);return{degree:Math.pow(f,1-u)*Math.pow(h,u)}}}};kr.dc=kr.degreeCentrality;kr.dcn=kr.degreeCentralityNormalised=kr.degreeCentralityNormalized;var ji=tt({harmonic:!0,weight:function(){return 1},directed:!1,root:null}),Pr={closenessCentralityNormalized:function(e){for(var r=ji(e),a=r.harmonic,n=r.weight,i=r.directed,s=this.cy(),o={},u=0,l=this.nodes(),f=this.floydWarshall({weight:n,directed:i}),h=0;h<l.length;h++){for(var d=0,c=l[h],v=0;v<l.length;v++)if(h!==v){var p=f.distance(c,l[v]);a?d+=1/p:d+=p}a||(d=1/d),u<d&&(u=d),o[c.id()]=d}return{closeness:function(y){return u==0?0:(de(y)?y=s.filter(y)[0].id():y=y.id(),o[y]/u)}}},closenessCentrality:function(e){var r=ji(e),a=r.root,n=r.weight,i=r.directed,s=r.harmonic;a=this.filter(a)[0];for(var o=this.dijkstra({root:a,weight:n,directed:i}),u=0,l=this.nodes(),f=0;f<l.length;f++){var h=l[f];if(!h.same(a)){var d=o.distanceTo(h);s?u+=1/d:u+=d}}return s?u:1/u}};Pr.cc=Pr.closenessCentrality;Pr.ccn=Pr.closenessCentralityNormalised=Pr.closenessCentralityNormalized;var Eh=tt({weight:null,directed:!1}),Zn={betweennessCentrality:function(e){for(var r=Eh(e),a=r.directed,n=r.weight,i=n!=null,s=this.cy(),o=this.nodes(),u={},l={},f=0,h={set:function(m,T){l[m]=T,T>f&&(f=T)},get:function(m){return l[m]}},d=0;d<o.length;d++){var c=o[d],v=c.id();a?u[v]=c.outgoers().nodes():u[v]=c.openNeighborhood().nodes(),h.set(v,0)}for(var p=function(m){for(var T=o[m].id(),C=[],S={},E={},w={},x=new Da(function(W,K){return w[W]-w[K]}),D=0;D<o.length;D++){var L=o[D].id();S[L]=[],E[L]=0,w[L]=1/0}for(E[T]=1,w[T]=0,x.push(T);!x.empty();){var A=x.pop();if(C.push(A),i)for(var N=0;N<u[A].length;N++){var O=u[A][N],M=s.getElementById(A),R=void 0;M.edgesTo(O).length>0?R=M.edgesTo(O)[0]:R=O.edgesTo(M)[0];var k=n(R);O=O.id(),w[O]>w[A]+k&&(w[O]=w[A]+k,x.nodes.indexOf(O)<0?x.push(O):x.updateItem(O),E[O]=0,S[O]=[]),w[O]==w[A]+k&&(E[O]=E[O]+E[A],S[O].push(A))}else for(var P=0;P<u[A].length;P++){var B=u[A][P].id();w[B]==1/0&&(x.push(B),w[B]=w[A]+1),w[B]==w[A]+1&&(E[B]=E[B]+E[A],S[B].push(A))}}for(var z={},G=0;G<o.length;G++)z[o[G].id()]=0;for(;C.length>0;){for(var F=C.pop(),U=0;U<S[F].length;U++){var Y=S[F][U];z[Y]=z[Y]+E[Y]/E[F]*(1+z[F])}F!=o[m].id()&&h.set(F,h.get(F)+z[F])}},g=0;g<o.length;g++)p(g);var y={betweenness:function(m){var T=s.collection(m).id();return h.get(T)},betweennessNormalized:function(m){if(f==0)return 0;var T=s.collection(m).id();return h.get(T)/f}};return y.betweennessNormalised=y.betweennessNormalized,y}};Zn.bc=Zn.betweennessCentrality;var wh=tt({expandFactor:2,inflateFactor:2,multFactor:1,maxIterations:20,attributes:[function(t){return 1}]}),xh=function(e){return wh(e)},Th=function(e,r){for(var a=0,n=0;n<r.length;n++)a+=r[n](e);return a},Ch=function(e,r,a){for(var n=0;n<r;n++)e[n*r+n]=a},Io=function(e,r){for(var a,n=0;n<r;n++){a=0;for(var i=0;i<r;i++)a+=e[i*r+n];for(var s=0;s<r;s++)e[s*r+n]=e[s*r+n]/a}},Dh=function(e,r,a){for(var n=new Array(a*a),i=0;i<a;i++){for(var s=0;s<a;s++)n[i*a+s]=0;for(var o=0;o<a;o++)for(var u=0;u<a;u++)n[i*a+u]+=e[i*a+o]*r[o*a+u]}return n},Sh=function(e,r,a){for(var n=e.slice(0),i=1;i<a;i++)e=Dh(e,n,r);return e},Lh=function(e,r,a){for(var n=new Array(r*r),i=0;i<r*r;i++)n[i]=Math.pow(e[i],a);return Io(n,r),n},Ah=function(e,r,a,n){for(var i=0;i<a;i++){var s=Math.round(e[i]*Math.pow(10,n))/Math.pow(10,n),o=Math.round(r[i]*Math.pow(10,n))/Math.pow(10,n);if(s!==o)return!1}return!0},Oh=function(e,r,a,n){for(var i=[],s=0;s<r;s++){for(var o=[],u=0;u<r;u++)Math.round(e[s*r+u]*1e3)/1e3>0&&o.push(a[u]);o.length!==0&&i.push(n.collection(o))}return i},Nh=function(e,r){for(var a=0;a<e.length;a++)if(!r[a]||e[a].id()!==r[a].id())return!1;return!0},Ih=function(e){for(var r=0;r<e.length;r++)for(var a=0;a<e.length;a++)r!=a&&Nh(e[r],e[a])&&e.splice(a,1);return e},es=function(e){for(var r=this.nodes(),a=this.edges(),n=this.cy(),i=xh(e),s={},o=0;o<r.length;o++)s[r[o].id()]=o;for(var u=r.length,l=u*u,f=new Array(l),h,d=0;d<l;d++)f[d]=0;for(var c=0;c<a.length;c++){var v=a[c],p=s[v.source().id()],g=s[v.target().id()],y=Th(v,i.attributes);f[p*u+g]+=y,f[g*u+p]+=y}Ch(f,u,i.multFactor),Io(f,u);for(var b=!0,m=0;b&&m<i.maxIterations;)b=!1,h=Sh(f,u,i.expandFactor),f=Lh(h,u,i.inflateFactor),Ah(f,h,l,4)||(b=!0),m++;var T=Oh(f,u,r,n);return T=Ih(T),T},Mh={markovClustering:es,mcl:es},Rh=function(e){return e},Mo=function(e,r){return Math.abs(r-e)},ts=function(e,r,a){return e+Mo(r,a)},rs=function(e,r,a){return e+Math.pow(a-r,2)},kh=function(e){return Math.sqrt(e)},Ph=function(e,r,a){return Math.max(e,Mo(r,a))},ea=function(e,r,a,n,i){for(var s=arguments.length>5&&arguments[5]!==void 0?arguments[5]:Rh,o=n,u,l,f=0;f<e;f++)u=r(f),l=a(f),o=i(o,u,l);return s(o)},zr={euclidean:function(e,r,a){return e>=2?ea(e,r,a,0,rs,kh):ea(e,r,a,0,ts)},squaredEuclidean:function(e,r,a){return ea(e,r,a,0,rs)},manhattan:function(e,r,a){return ea(e,r,a,0,ts)},max:function(e,r,a){return ea(e,r,a,-1/0,Ph)}};zr["squared-euclidean"]=zr.squaredEuclidean;zr.squaredeuclidean=zr.squaredEuclidean;function mn(t,e,r,a,n,i){var s;return Ge(t)?s=t:s=zr[t]||zr.euclidean,e===0&&Ge(t)?s(n,i):s(e,r,a,n,i)}var Bh=tt({k:2,m:2,sensitivityThreshold:1e-4,distance:"euclidean",maxIterations:10,attributes:[],testMode:!1,testCentroids:null}),Ti=function(e){return Bh(e)},on=function(e,r,a,n,i){var s=i!=="kMedoids",o=s?function(h){return a[h]}:function(h){return n[h](a)},u=function(d){return n[d](r)},l=a,f=r;return mn(e,n.length,o,u,l,f)},Fn=function(e,r,a){for(var n=a.length,i=new Array(n),s=new Array(n),o=new Array(r),u=null,l=0;l<n;l++)i[l]=e.min(a[l]).value,s[l]=e.max(a[l]).value;for(var f=0;f<r;f++){u=[];for(var h=0;h<n;h++)u[h]=Math.random()*(s[h]-i[h])+i[h];o[f]=u}return o},Ro=function(e,r,a,n,i){for(var s=1/0,o=0,u=0;u<r.length;u++){var l=on(a,e,r[u],n,i);l<s&&(s=l,o=u)}return o},ko=function(e,r,a){for(var n=[],i=null,s=0;s<r.length;s++)i=r[s],a[i.id()]===e&&n.push(i);return n},Fh=function(e,r,a){return Math.abs(r-e)<=a},Gh=function(e,r,a){for(var n=0;n<e.length;n++)for(var i=0;i<e[n].length;i++){var s=Math.abs(e[n][i]-r[n][i]);if(s>a)return!1}return!0},zh=function(e,r,a){for(var n=0;n<a;n++)if(e===r[n])return!0;return!1},as=function(e,r){var a=new Array(r);if(e.length<50)for(var n=0;n<r;n++){for(var i=e[Math.floor(Math.random()*e.length)];zh(i,a,n);)i=e[Math.floor(Math.random()*e.length)];a[n]=i}else for(var s=0;s<r;s++)a[s]=e[Math.floor(Math.random()*e.length)];return a},ns=function(e,r,a){for(var n=0,i=0;i<r.length;i++)n+=on("manhattan",r[i],e,a,"kMedoids");return n},Vh=function(e){var r=this.cy(),a=this.nodes(),n=null,i=Ti(e),s=new Array(i.k),o={},u;i.testMode?typeof i.testCentroids=="number"?(i.testCentroids,u=Fn(a,i.k,i.attributes)):Xe(i.testCentroids)==="object"?u=i.testCentroids:u=Fn(a,i.k,i.attributes):u=Fn(a,i.k,i.attributes);for(var l=!0,f=0;l&&f<i.maxIterations;){for(var h=0;h<a.length;h++)n=a[h],o[n.id()]=Ro(n,u,i.distance,i.attributes,"kMeans");l=!1;for(var d=0;d<i.k;d++){var c=ko(d,a,o);if(c.length!==0){for(var v=i.attributes.length,p=u[d],g=new Array(v),y=new Array(v),b=0;b<v;b++){y[b]=0;for(var m=0;m<c.length;m++)n=c[m],y[b]+=i.attributes[b](n);g[b]=y[b]/c.length,Fh(g[b],p[b],i.sensitivityThreshold)||(l=!0)}u[d]=g,s[d]=r.collection(c)}}f++}return s},Uh=function(e){var r=this.cy(),a=this.nodes(),n=null,i=Ti(e),s=new Array(i.k),o,u={},l,f=new Array(i.k);i.testMode?typeof i.testCentroids=="number"||(Xe(i.testCentroids)==="object"?o=i.testCentroids:o=as(a,i.k)):o=as(a,i.k);for(var h=!0,d=0;h&&d<i.maxIterations;){for(var c=0;c<a.length;c++)n=a[c],u[n.id()]=Ro(n,o,i.distance,i.attributes,"kMedoids");h=!1;for(var v=0;v<o.length;v++){var p=ko(v,a,u);if(p.length!==0){f[v]=ns(o[v],p,i.attributes);for(var g=0;g<p.length;g++)l=ns(p[g],p,i.attributes),l<f[v]&&(f[v]=l,o[v]=p[g],h=!0);s[v]=r.collection(p)}}d++}return s},$h=function(e,r,a,n,i){for(var s,o,u=0;u<r.length;u++)for(var l=0;l<e.length;l++)n[u][l]=Math.pow(a[u][l],i.m);for(var f=0;f<e.length;f++)for(var h=0;h<i.attributes.length;h++){s=0,o=0;for(var d=0;d<r.length;d++)s+=n[d][f]*i.attributes[h](r[d]),o+=n[d][f];e[f][h]=s/o}},Yh=function(e,r,a,n,i){for(var s=0;s<e.length;s++)r[s]=e[s].slice();for(var o,u,l,f=2/(i.m-1),h=0;h<a.length;h++)for(var d=0;d<n.length;d++){o=0;for(var c=0;c<a.length;c++)u=on(i.distance,n[d],a[h],i.attributes,"cmeans"),l=on(i.distance,n[d],a[c],i.attributes,"cmeans"),o+=Math.pow(u/l,f);e[d][h]=1/o}},_h=function(e,r,a,n){for(var i=new Array(a.k),s=0;s<i.length;s++)i[s]=[];for(var o,u,l=0;l<r.length;l++){o=-1/0,u=-1;for(var f=0;f<r[0].length;f++)r[l][f]>o&&(o=r[l][f],u=f);i[u].push(e[l])}for(var h=0;h<i.length;h++)i[h]=n.collection(i[h]);return i},is=function(e){var r=this.cy(),a=this.nodes(),n=Ti(e),i,s,o,u,l;u=new Array(a.length);for(var f=0;f<a.length;f++)u[f]=new Array(n.k);o=new Array(a.length);for(var h=0;h<a.length;h++)o[h]=new Array(n.k);for(var d=0;d<a.length;d++){for(var c=0,v=0;v<n.k;v++)o[d][v]=Math.random(),c+=o[d][v];for(var p=0;p<n.k;p++)o[d][p]=o[d][p]/c}s=new Array(n.k);for(var g=0;g<n.k;g++)s[g]=new Array(n.attributes.length);l=new Array(a.length);for(var y=0;y<a.length;y++)l[y]=new Array(n.k);for(var b=!0,m=0;b&&m<n.maxIterations;)b=!1,$h(s,a,o,l,n),Yh(o,u,s,a,n),Gh(o,u,n.sensitivityThreshold)||(b=!0),m++;return i=_h(a,o,n,r),{clusters:i,degreeOfMembership:o}},Hh={kMeans:Vh,kMedoids:Uh,fuzzyCMeans:is,fcm:is},Xh=tt({distance:"euclidean",linkage:"min",mode:"threshold",threshold:1/0,addDendrogram:!1,dendrogramDepth:0,attributes:[]}),qh={single:"min",complete:"max"},Wh=function(e){var r=Xh(e),a=qh[r.linkage];return a!=null&&(r.linkage=a),r},ss=function(e,r,a,n,i){for(var s=0,o=1/0,u,l=i.attributes,f=function(x,D){return mn(i.distance,l.length,function(L){return l[L](x)},function(L){return l[L](D)},x,D)},h=0;h<e.length;h++){var d=e[h].key,c=a[d][n[d]];c<o&&(s=d,o=c)}if(i.mode==="threshold"&&o>=i.threshold||i.mode==="dendrogram"&&e.length===1)return!1;var v=r[s],p=r[n[s]],g;i.mode==="dendrogram"?g={left:v,right:p,key:v.key}:g={value:v.value.concat(p.value),key:v.key},e[v.index]=g,e.splice(p.index,1),r[v.key]=g;for(var y=0;y<e.length;y++){var b=e[y];v.key===b.key?u=1/0:i.linkage==="min"?(u=a[v.key][b.key],a[v.key][b.key]>a[p.key][b.key]&&(u=a[p.key][b.key])):i.linkage==="max"?(u=a[v.key][b.key],a[v.key][b.key]<a[p.key][b.key]&&(u=a[p.key][b.key])):i.linkage==="mean"?u=(a[v.key][b.key]*v.size+a[p.key][b.key]*p.size)/(v.size+p.size):i.mode==="dendrogram"?u=f(b.value,v.value):u=f(b.value[0],v.value[0]),a[v.key][b.key]=a[b.key][v.key]=u}for(var m=0;m<e.length;m++){var T=e[m].key;if(n[T]===v.key||n[T]===p.key){for(var C=T,S=0;S<e.length;S++){var E=e[S].key;a[T][E]<a[T][C]&&(C=E)}n[T]=C}e[m].index=m}return v.key=p.key=v.index=p.index=null,!0},Pa=function t(e,r,a){e&&(e.value?r.push(e.value):(e.left&&t(e.left,r),e.right&&t(e.right,r)))},Kh=function t(e,r){if(!e)return"";if(e.left&&e.right){var a=t(e.left,r),n=t(e.right,r),i=r.add({group:"nodes",data:{id:a+","+n}});return r.add({group:"edges",data:{source:a,target:i.id()}}),r.add({group:"edges",data:{source:n,target:i.id()}}),i.id()}else if(e.value)return e.value.id()},Zh=function t(e,r,a){if(!e)return[];var n=[],i=[],s=[];return r===0?(e.left&&Pa(e.left,n),e.right&&Pa(e.right,i),s=n.concat(i),[a.collection(s)]):r===1?e.value?[a.collection(e.value)]:(e.left&&Pa(e.left,n),e.right&&Pa(e.right,i),[a.collection(n),a.collection(i)]):e.value?[a.collection(e.value)]:(e.left&&(n=t(e.left,r-1,a)),e.right&&(i=t(e.right,r-1,a)),n.concat(i))},os=function(e){for(var r=this.cy(),a=this.nodes(),n=Wh(e),i=n.attributes,s=function(m,T){return mn(n.distance,i.length,function(C){return i[C](m)},function(C){return i[C](T)},m,T)},o=[],u=[],l=[],f=[],h=0;h<a.length;h++){var d={value:n.mode==="dendrogram"?a[h]:[a[h]],key:h,index:h};o[h]=d,f[h]=d,u[h]=[],l[h]=0}for(var c=0;c<o.length;c++)for(var v=0;v<=c;v++){var p=void 0;n.mode==="dendrogram"?p=c===v?1/0:s(o[c].value,o[v].value):p=c===v?1/0:s(o[c].value[0],o[v].value[0]),u[c][v]=p,u[v][c]=p,p<u[c][l[c]]&&(l[c]=v)}for(var g=ss(o,f,u,l,n);g;)g=ss(o,f,u,l,n);var y;return n.mode==="dendrogram"?(y=Zh(o[0],n.dendrogramDepth,r),n.addDendrogram&&Kh(o[0],r)):(y=new Array(o.length),o.forEach(function(b,m){b.key=b.index=null,y[m]=r.collection(b.value)})),y},Qh={hierarchicalClustering:os,hca:os},Jh=tt({distance:"euclidean",preference:"median",damping:.8,maxIterations:1e3,minIterations:100,attributes:[]}),jh=function(e){var r=e.damping,a=e.preference;.5<=r&&r<1||ze("Damping must range on [0.5, 1).  Got: ".concat(r));var n=["median","mean","min","max"];return n.some(function(i){return i===a})||ie(a)||ze("Preference must be one of [".concat(n.map(function(i){return"'".concat(i,"'")}).join(", "),"] or a number.  Got: ").concat(a)),Jh(e)},ec=function(e,r,a,n){var i=function(o,u){return n[u](o)};return-mn(e,n.length,function(s){return i(r,s)},function(s){return i(a,s)},r,a)},tc=function(e,r){var a=null;return r==="median"?a=jf(e):r==="mean"?a=Jf(e):r==="min"?a=Zf(e):r==="max"?a=Qf(e):a=r,a},rc=function(e,r,a){for(var n=[],i=0;i<e;i++)r[i*e+i]+a[i*e+i]>0&&n.push(i);return n},us=function(e,r,a){for(var n=[],i=0;i<e;i++){for(var s=-1,o=-1/0,u=0;u<a.length;u++){var l=a[u];r[i*e+l]>o&&(s=l,o=r[i*e+l])}s>0&&n.push(s)}for(var f=0;f<a.length;f++)n[a[f]]=a[f];return n},ac=function(e,r,a){for(var n=us(e,r,a),i=0;i<a.length;i++){for(var s=[],o=0;o<n.length;o++)n[o]===a[i]&&s.push(o);for(var u=-1,l=-1/0,f=0;f<s.length;f++){for(var h=0,d=0;d<s.length;d++)h+=r[s[d]*e+s[f]];h>l&&(u=f,l=h)}a[i]=s[u]}return n=us(e,r,a),n},ls=function(e){for(var r=this.cy(),a=this.nodes(),n=jh(e),i={},s=0;s<a.length;s++)i[a[s].id()]=s;var o,u,l,f,h,d;o=a.length,u=o*o,l=new Array(u);for(var c=0;c<u;c++)l[c]=-1/0;for(var v=0;v<o;v++)for(var p=0;p<o;p++)v!==p&&(l[v*o+p]=ec(n.distance,a[v],a[p],n.attributes));f=tc(l,n.preference);for(var g=0;g<o;g++)l[g*o+g]=f;h=new Array(u);for(var y=0;y<u;y++)h[y]=0;d=new Array(u);for(var b=0;b<u;b++)d[b]=0;for(var m=new Array(o),T=new Array(o),C=new Array(o),S=0;S<o;S++)m[S]=0,T[S]=0,C[S]=0;for(var E=new Array(o*n.minIterations),w=0;w<E.length;w++)E[w]=0;var x;for(x=0;x<n.maxIterations;x++){for(var D=0;D<o;D++){for(var L=-1/0,A=-1/0,N=-1,O=0,M=0;M<o;M++)m[M]=h[D*o+M],O=d[D*o+M]+l[D*o+M],O>=L?(A=L,L=O,N=M):O>A&&(A=O);for(var R=0;R<o;R++)h[D*o+R]=(1-n.damping)*(l[D*o+R]-L)+n.damping*m[R];h[D*o+N]=(1-n.damping)*(l[D*o+N]-A)+n.damping*m[N]}for(var k=0;k<o;k++){for(var P=0,B=0;B<o;B++)m[B]=d[B*o+k],T[B]=Math.max(0,h[B*o+k]),P+=T[B];P-=T[k],T[k]=h[k*o+k],P+=T[k];for(var z=0;z<o;z++)d[z*o+k]=(1-n.damping)*Math.min(0,P-T[z])+n.damping*m[z];d[k*o+k]=(1-n.damping)*(P-T[k])+n.damping*m[k]}for(var G=0,F=0;F<o;F++){var U=d[F*o+F]+h[F*o+F]>0?1:0;E[x%n.minIterations*o+F]=U,G+=U}if(G>0&&(x>=n.minIterations-1||x==n.maxIterations-1)){for(var Y=0,W=0;W<o;W++){C[W]=0;for(var K=0;K<n.minIterations;K++)C[W]+=E[K*o+W];(C[W]===0||C[W]===n.minIterations)&&Y++}if(Y===o)break}}for(var j=rc(o,h,d),_=ac(o,l,j),V={},H=0;H<j.length;H++)V[j[H]]=[];for(var Q=0;Q<a.length;Q++){var ne=i[a[Q].id()],ce=_[ne];ce!=null&&V[ce].push(a[Q])}for(var te=new Array(j.length),se=0;se<j.length;se++)te[se]=r.collection(V[j[se]]);return te},nc={affinityPropagation:ls,ap:ls},ic=tt({root:void 0,directed:!1}),sc={hierholzer:function(e){if(!Ce(e)){var r=arguments;e={root:r[0],directed:r[1]}}var a=ic(e),n=a.root,i=a.directed,s=this,o=!1,u,l,f;n&&(f=de(n)?this.filter(n)[0].id():n[0].id());var h={},d={};i?s.forEach(function(b){var m=b.id();if(b.isNode()){var T=b.indegree(!0),C=b.outdegree(!0),S=T-C,E=C-T;S==1?u?o=!0:u=m:E==1?l?o=!0:l=m:(E>1||S>1)&&(o=!0),h[m]=[],b.outgoers().forEach(function(w){w.isEdge()&&h[m].push(w.id())})}else d[m]=[void 0,b.target().id()]}):s.forEach(function(b){var m=b.id();if(b.isNode()){var T=b.degree(!0);T%2&&(u?l?o=!0:l=m:u=m),h[m]=[],b.connectedEdges().forEach(function(C){return h[m].push(C.id())})}else d[m]=[b.source().id(),b.target().id()]});var c={found:!1,trail:void 0};if(o)return c;if(l&&u)if(i){if(f&&l!=f)return c;f=l}else{if(f&&l!=f&&u!=f)return c;f||(f=l)}else f||(f=s[0].id());var v=function(m){for(var T=m,C=[m],S,E,w;h[T].length;)S=h[T].shift(),E=d[S][0],w=d[S][1],T!=w?(h[w]=h[w].filter(function(x){return x!=S}),T=w):!i&&T!=E&&(h[E]=h[E].filter(function(x){return x!=S}),T=E),C.unshift(S),C.unshift(T);return C},p=[],g=[];for(g=v(f);g.length!=1;)h[g[0]].length==0?(p.unshift(s.getElementById(g.shift())),p.unshift(s.getElementById(g.shift()))):g=v(g.shift()).concat(g);p.unshift(s.getElementById(g.shift()));for(var y in h)if(h[y].length)return c;return c.found=!0,c.trail=this.spawn(p,!0),c}},Ba=function(){var e=this,r={},a=0,n=0,i=[],s=[],o={},u=function(d,c){for(var v=s.length-1,p=[],g=e.spawn();s[v].x!=d||s[v].y!=c;)p.push(s.pop().edge),v--;p.push(s.pop().edge),p.forEach(function(y){var b=y.connectedNodes().intersection(e);g.merge(y),b.forEach(function(m){var T=m.id(),C=m.connectedEdges().intersection(e);g.merge(m),r[T].cutVertex?g.merge(C.filter(function(S){return S.isLoop()})):g.merge(C)})}),i.push(g)},l=function h(d,c,v){d===v&&(n+=1),r[c]={id:a,low:a++,cutVertex:!1};var p=e.getElementById(c).connectedEdges().intersection(e);if(p.size()===0)i.push(e.spawn(e.getElementById(c)));else{var g,y,b,m;p.forEach(function(T){g=T.source().id(),y=T.target().id(),b=g===c?y:g,b!==v&&(m=T.id(),o[m]||(o[m]=!0,s.push({x:c,y:b,edge:T})),b in r?r[c].low=Math.min(r[c].low,r[b].id):(h(d,b,c),r[c].low=Math.min(r[c].low,r[b].low),r[c].id<=r[b].low&&(r[c].cutVertex=!0,u(c,b))))})}};e.forEach(function(h){if(h.isNode()){var d=h.id();d in r||(n=0,l(d,d),r[d].cutVertex=n>1)}});var f=Object.keys(r).filter(function(h){return r[h].cutVertex}).map(function(h){return e.getElementById(h)});return{cut:e.spawn(f),components:i}},oc={hopcroftTarjanBiconnected:Ba,htbc:Ba,htb:Ba,hopcroftTarjanBiconnectedComponents:Ba},Fa=function(){var e=this,r={},a=0,n=[],i=[],s=e.spawn(e),o=function u(l){i.push(l),r[l]={index:a,low:a++,explored:!1};var f=e.getElementById(l).connectedEdges().intersection(e);if(f.forEach(function(p){var g=p.target().id();g!==l&&(g in r||u(g),r[g].explored||(r[l].low=Math.min(r[l].low,r[g].low)))}),r[l].index===r[l].low){for(var h=e.spawn();;){var d=i.pop();if(h.merge(e.getElementById(d)),r[d].low=r[l].index,r[d].explored=!0,d===l)break}var c=h.edgesWith(h),v=h.merge(c);n.push(v),s=s.difference(v)}};return e.forEach(function(u){if(u.isNode()){var l=u.id();l in r||o(l)}}),{cut:s,components:n}},uc={tarjanStronglyConnected:Fa,tsc:Fa,tscc:Fa,tarjanStronglyConnectedComponents:Fa},Po={};[da,Gf,zf,Uf,Yf,Hf,Wf,bh,kr,Pr,Zn,Mh,Hh,Qh,nc,sc,oc,uc].forEach(function(t){be(Po,t)});/*!
Embeddable Minimum Strictly-Compliant Promises/A+ 1.1.1 Thenable
Copyright (c) 2013-2014 Ralf S. Engelschall (http://engelschall.com)
Licensed under The MIT License (http://opensource.org/licenses/MIT)
*/var Bo=0,Fo=1,Go=2,_t=function t(e){if(!(this instanceof t))return new t(e);this.id="Thenable/1.0.7",this.state=Bo,this.fulfillValue=void 0,this.rejectReason=void 0,this.onFulfilled=[],this.onRejected=[],this.proxy={then:this.then.bind(this)},typeof e=="function"&&e.call(this,this.fulfill.bind(this),this.reject.bind(this))};_t.prototype={fulfill:function(e){return fs(this,Fo,"fulfillValue",e)},reject:function(e){return fs(this,Go,"rejectReason",e)},then:function(e,r){var a=this,n=new _t;return a.onFulfilled.push(cs(e,n,"fulfill")),a.onRejected.push(cs(r,n,"reject")),zo(a),n.proxy}};var fs=function(e,r,a,n){return e.state===Bo&&(e.state=r,e[a]=n,zo(e)),e},zo=function(e){e.state===Fo?hs(e,"onFulfilled",e.fulfillValue):e.state===Go&&hs(e,"onRejected",e.rejectReason)},hs=function(e,r,a){if(e[r].length!==0){var n=e[r];e[r]=[];var i=function(){for(var o=0;o<n.length;o++)n[o](a)};typeof setImmediate=="function"?setImmediate(i):setTimeout(i,0)}},cs=function(e,r,a){return function(n){if(typeof e!="function")r[a].call(r,n);else{var i;try{i=e(n)}catch(s){r.reject(s);return}lc(r,i)}}},lc=function t(e,r){if(e===r||e.proxy===r){e.reject(new TypeError("cannot resolve promise with itself"));return}var a;if(Xe(r)==="object"&&r!==null||typeof r=="function")try{a=r.then}catch(i){e.reject(i);return}if(typeof a=="function"){var n=!1;try{a.call(r,function(i){n||(n=!0,i===r?e.reject(new TypeError("circular thenable chain")):t(e,i))},function(i){n||(n=!0,e.reject(i))})}catch(i){n||e.reject(i)}return}e.fulfill(r)};_t.all=function(t){return new _t(function(e,r){for(var a=new Array(t.length),n=0,i=function(u,l){a[u]=l,n++,n===t.length&&e(a)},s=0;s<t.length;s++)(function(o){var u=t[o],l=u!=null&&u.then!=null;if(l)u.then(function(h){i(o,h)},function(h){r(h)});else{var f=u;i(o,f)}})(s)})};_t.resolve=function(t){return new _t(function(e,r){e(t)})};_t.reject=function(t){return new _t(function(e,r){r(t)})};var $r=typeof Promise<"u"?Promise:_t,Qn=function(e,r,a){var n=gi(e),i=!n,s=this._private=be({duration:1e3},r,a);if(s.target=e,s.style=s.style||s.css,s.started=!1,s.playing=!1,s.hooked=!1,s.applying=!1,s.progress=0,s.completes=[],s.frames=[],s.complete&&Ge(s.complete)&&s.completes.push(s.complete),i){var o=e.position();s.startPosition=s.startPosition||{x:o.x,y:o.y},s.startStyle=s.startStyle||e.cy().style().getAnimationStartStyle(e,s.style)}if(n){var u=e.pan();s.startPan={x:u.x,y:u.y},s.startZoom=e.zoom()}this.length=1,this[0]=this},yr=Qn.prototype;be(yr,{instanceString:function(){return"animation"},hook:function(){var e=this._private;if(!e.hooked){var r,a=e.target._private.animation;e.queue?r=a.queue:r=a.current,r.push(this),pt(e.target)&&e.target.cy().addToAnimationPool(e.target),e.hooked=!0}return this},play:function(){var e=this._private;return e.progress===1&&(e.progress=0),e.playing=!0,e.started=!1,e.stopped=!1,this.hook(),this},playing:function(){return this._private.playing},apply:function(){var e=this._private;return e.applying=!0,e.started=!1,e.stopped=!1,this.hook(),this},applying:function(){return this._private.applying},pause:function(){var e=this._private;return e.playing=!1,e.started=!1,this},stop:function(){var e=this._private;return e.playing=!1,e.started=!1,e.stopped=!0,this},rewind:function(){return this.progress(0)},fastforward:function(){return this.progress(1)},time:function(e){var r=this._private;return e===void 0?r.progress*r.duration:this.progress(e/r.duration)},progress:function(e){var r=this._private,a=r.playing;return e===void 0?r.progress:(a&&this.pause(),r.progress=e,r.started=!1,a&&this.play(),this)},completed:function(){return this._private.progress===1},reverse:function(){var e=this._private,r=e.playing;r&&this.pause(),e.progress=1-e.progress,e.started=!1;var a=function(l,f){var h=e[l];h!=null&&(e[l]=e[f],e[f]=h)};if(a("zoom","startZoom"),a("pan","startPan"),a("position","startPosition"),e.style)for(var n=0;n<e.style.length;n++){var i=e.style[n],s=i.name,o=e.startStyle[s];e.startStyle[s]=i,e.style[n]=o}return r&&this.play(),this},promise:function(e){var r=this._private,a;switch(e){case"frame":a=r.frames;break;default:case"complete":case"completed":a=r.completes}return new $r(function(n,i){a.push(function(){n()})})}});yr.complete=yr.completed;yr.run=yr.play;yr.running=yr.playing;var fc={animated:function(){return function(){var r=this,a=r.length!==void 0,n=a?r:[r],i=this._private.cy||this;if(!i.styleEnabled())return!1;var s=n[0];if(s)return s._private.animation.current.length>0}},clearQueue:function(){return function(){var r=this,a=r.length!==void 0,n=a?r:[r],i=this._private.cy||this;if(!i.styleEnabled())return this;for(var s=0;s<n.length;s++){var o=n[s];o._private.animation.queue=[]}return this}},delay:function(){return function(r,a){var n=this._private.cy||this;return n.styleEnabled()?this.animate({delay:r,duration:r,complete:a}):this}},delayAnimation:function(){return function(r,a){var n=this._private.cy||this;return n.styleEnabled()?this.animation({delay:r,duration:r,complete:a}):this}},animation:function(){return function(r,a){var n=this,i=n.length!==void 0,s=i?n:[n],o=this._private.cy||this,u=!i,l=!u;if(!o.styleEnabled())return this;var f=o.style();r=be({},r,a);var h=Object.keys(r).length===0;if(h)return new Qn(s[0],r);switch(r.duration===void 0&&(r.duration=400),r.duration){case"slow":r.duration=600;break;case"fast":r.duration=200;break}if(l&&(r.style=f.getPropsList(r.style||r.css),r.css=void 0),l&&r.renderedPosition!=null){var d=r.renderedPosition,c=o.pan(),v=o.zoom();r.position=Co(d,v,c)}if(u&&r.panBy!=null){var p=r.panBy,g=o.pan();r.pan={x:g.x+p.x,y:g.y+p.y}}var y=r.center||r.centre;if(u&&y!=null){var b=o.getCenterPan(y.eles,r.zoom);b!=null&&(r.pan=b)}if(u&&r.fit!=null){var m=r.fit,T=o.getFitViewport(m.eles||m.boundingBox,m.padding);T!=null&&(r.pan=T.pan,r.zoom=T.zoom)}if(u&&Ce(r.zoom)){var C=o.getZoomedViewport(r.zoom);C!=null?(C.zoomed&&(r.zoom=C.zoom),C.panned&&(r.pan=C.pan)):r.zoom=null}return new Qn(s[0],r)}},animate:function(){return function(r,a){var n=this,i=n.length!==void 0,s=i?n:[n],o=this._private.cy||this;if(!o.styleEnabled())return this;a&&(r=be({},r,a));for(var u=0;u<s.length;u++){var l=s[u],f=l.animated()&&(r.queue===void 0||r.queue),h=l.animation(r,f?{queue:!0}:void 0);h.play()}return this}},stop:function(){return function(r,a){var n=this,i=n.length!==void 0,s=i?n:[n],o=this._private.cy||this;if(!o.styleEnabled())return this;for(var u=0;u<s.length;u++){for(var l=s[u],f=l._private,h=f.animation.current,d=0;d<h.length;d++){var c=h[d],v=c._private;a&&(v.duration=0)}r&&(f.animation.queue=[]),a||(f.animation.current=[])}return o.notify("draw"),this}}},hc=Array.isArray,bn=hc,cc=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,vc=/^\w*$/;function dc(t,e){if(bn(t))return!1;var r=typeof t;return r=="number"||r=="symbol"||r=="boolean"||t==null||Ca(t)?!0:vc.test(t)||!cc.test(t)||e!=null&&t in Object(e)}var gc=dc,pc="[object AsyncFunction]",yc="[object Function]",mc="[object GeneratorFunction]",bc="[object Proxy]";function Ec(t){if(!vr(t))return!1;var e=go(t);return e==yc||e==mc||e==pc||e==bc}var wc=Ec,xc=dn["__core-js_shared__"],Gn=xc,vs=function(){var t=/[^.]+$/.exec(Gn&&Gn.keys&&Gn.keys.IE_PROTO||"");return t?"Symbol(src)_1."+t:""}();function Tc(t){return!!vs&&vs in t}var Cc=Tc,Dc=Function.prototype,Sc=Dc.toString;function Lc(t){if(t!=null){try{return Sc.call(t)}catch{}try{return t+""}catch{}}return""}var Ac=Lc,Oc=/[\\^$.*+?()[\]{}|]/g,Nc=/^\[object .+?Constructor\]$/,Ic=Function.prototype,Mc=Object.prototype,Rc=Ic.toString,kc=Mc.hasOwnProperty,Pc=RegExp("^"+Rc.call(kc).replace(Oc,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");function Bc(t){if(!vr(t)||Cc(t))return!1;var e=wc(t)?Pc:Nc;return e.test(Ac(t))}var Fc=Bc;function Gc(t,e){return t==null?void 0:t[e]}var zc=Gc;function Vc(t,e){var r=zc(t,e);return Fc(r)?r:void 0}var Ci=Vc,Uc=Ci(Object,"create"),ya=Uc;function $c(){this.__data__=ya?ya(null):{},this.size=0}var Yc=$c;function _c(t){var e=this.has(t)&&delete this.__data__[t];return this.size-=e?1:0,e}var Hc=_c,Xc="__lodash_hash_undefined__",qc=Object.prototype,Wc=qc.hasOwnProperty;function Kc(t){var e=this.__data__;if(ya){var r=e[t];return r===Xc?void 0:r}return Wc.call(e,t)?e[t]:void 0}var Zc=Kc,Qc=Object.prototype,Jc=Qc.hasOwnProperty;function jc(t){var e=this.__data__;return ya?e[t]!==void 0:Jc.call(e,t)}var ev=jc,tv="__lodash_hash_undefined__";function rv(t,e){var r=this.__data__;return this.size+=this.has(t)?0:1,r[t]=ya&&e===void 0?tv:e,this}var av=rv;function Yr(t){var e=-1,r=t==null?0:t.length;for(this.clear();++e<r;){var a=t[e];this.set(a[0],a[1])}}Yr.prototype.clear=Yc;Yr.prototype.delete=Hc;Yr.prototype.get=Zc;Yr.prototype.has=ev;Yr.prototype.set=av;var ds=Yr;function nv(){this.__data__=[],this.size=0}var iv=nv;function sv(t,e){return t===e||t!==t&&e!==e}var Vo=sv;function ov(t,e){for(var r=t.length;r--;)if(Vo(t[r][0],e))return r;return-1}var En=ov,uv=Array.prototype,lv=uv.splice;function fv(t){var e=this.__data__,r=En(e,t);if(r<0)return!1;var a=e.length-1;return r==a?e.pop():lv.call(e,r,1),--this.size,!0}var hv=fv;function cv(t){var e=this.__data__,r=En(e,t);return r<0?void 0:e[r][1]}var vv=cv;function dv(t){return En(this.__data__,t)>-1}var gv=dv;function pv(t,e){var r=this.__data__,a=En(r,t);return a<0?(++this.size,r.push([t,e])):r[a][1]=e,this}var yv=pv;function _r(t){var e=-1,r=t==null?0:t.length;for(this.clear();++e<r;){var a=t[e];this.set(a[0],a[1])}}_r.prototype.clear=iv;_r.prototype.delete=hv;_r.prototype.get=vv;_r.prototype.has=gv;_r.prototype.set=yv;var mv=_r,bv=Ci(dn,"Map"),Ev=bv;function wv(){this.size=0,this.__data__={hash:new ds,map:new(Ev||mv),string:new ds}}var xv=wv;function Tv(t){var e=typeof t;return e=="string"||e=="number"||e=="symbol"||e=="boolean"?t!=="__proto__":t===null}var Cv=Tv;function Dv(t,e){var r=t.__data__;return Cv(e)?r[typeof e=="string"?"string":"hash"]:r.map}var wn=Dv;function Sv(t){var e=wn(this,t).delete(t);return this.size-=e?1:0,e}var Lv=Sv;function Av(t){return wn(this,t).get(t)}var Ov=Av;function Nv(t){return wn(this,t).has(t)}var Iv=Nv;function Mv(t,e){var r=wn(this,t),a=r.size;return r.set(t,e),this.size+=r.size==a?0:1,this}var Rv=Mv;function Hr(t){var e=-1,r=t==null?0:t.length;for(this.clear();++e<r;){var a=t[e];this.set(a[0],a[1])}}Hr.prototype.clear=xv;Hr.prototype.delete=Lv;Hr.prototype.get=Ov;Hr.prototype.has=Iv;Hr.prototype.set=Rv;var Uo=Hr,kv="Expected a function";function Di(t,e){if(typeof t!="function"||e!=null&&typeof e!="function")throw new TypeError(kv);var r=function(){var a=arguments,n=e?e.apply(this,a):a[0],i=r.cache;if(i.has(n))return i.get(n);var s=t.apply(this,a);return r.cache=i.set(n,s)||i,s};return r.cache=new(Di.Cache||Uo),r}Di.Cache=Uo;var Pv=Di,Bv=500;function Fv(t){var e=Pv(t,function(a){return r.size===Bv&&r.clear(),a}),r=e.cache;return e}var Gv=Fv,zv=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,Vv=/\\(\\)?/g,Uv=Gv(function(t){var e=[];return t.charCodeAt(0)===46&&e.push(""),t.replace(zv,function(r,a,n,i){e.push(n?i.replace(Vv,"$1"):a||r)}),e}),$o=Uv;function $v(t,e){for(var r=-1,a=t==null?0:t.length,n=Array(a);++r<a;)n[r]=e(t[r],r,t);return n}var Yo=$v,Yv=1/0,gs=Fr?Fr.prototype:void 0,ps=gs?gs.toString:void 0;function _o(t){if(typeof t=="string")return t;if(bn(t))return Yo(t,_o)+"";if(Ca(t))return ps?ps.call(t):"";var e=t+"";return e=="0"&&1/t==-Yv?"-0":e}var _v=_o;function Hv(t){return t==null?"":_v(t)}var Ho=Hv;function Xv(t,e){return bn(t)?t:gc(t,e)?[t]:$o(Ho(t))}var Xo=Xv,qv=1/0;function Wv(t){if(typeof t=="string"||Ca(t))return t;var e=t+"";return e=="0"&&1/t==-qv?"-0":e}var Si=Wv;function Kv(t,e){e=Xo(e,t);for(var r=0,a=e.length;t!=null&&r<a;)t=t[Si(e[r++])];return r&&r==a?t:void 0}var Zv=Kv;function Qv(t,e,r){var a=t==null?void 0:Zv(t,e);return a===void 0?r:a}var Jv=Qv,jv=function(){try{var t=Ci(Object,"defineProperty");return t({},"",{}),t}catch{}}(),ys=jv;function ed(t,e,r){e=="__proto__"&&ys?ys(t,e,{configurable:!0,enumerable:!0,value:r,writable:!0}):t[e]=r}var td=ed,rd=Object.prototype,ad=rd.hasOwnProperty;function nd(t,e,r){var a=t[e];(!(ad.call(t,e)&&Vo(a,r))||r===void 0&&!(e in t))&&td(t,e,r)}var id=nd,sd=9007199254740991,od=/^(?:0|[1-9]\d*)$/;function ud(t,e){var r=typeof t;return e=e??sd,!!e&&(r=="number"||r!="symbol"&&od.test(t))&&t>-1&&t%1==0&&t<e}var ld=ud;function fd(t,e,r,a){if(!vr(t))return t;e=Xo(e,t);for(var n=-1,i=e.length,s=i-1,o=t;o!=null&&++n<i;){var u=Si(e[n]),l=r;if(u==="__proto__"||u==="constructor"||u==="prototype")return t;if(n!=s){var f=o[u];l=a?a(f,u,o):void 0,l===void 0&&(l=vr(f)?f:ld(e[n+1])?[]:{})}id(o,u,l),o=o[u]}return t}var hd=fd;function cd(t,e,r){return t==null?t:hd(t,e,r)}var vd=cd;function dd(t,e){var r=-1,a=t.length;for(e||(e=Array(a));++r<a;)e[r]=t[r];return e}var gd=dd;function pd(t){return bn(t)?Yo(t,Si):Ca(t)?[t]:gd($o(Ho(t)))}var yd=pd,md={data:function(e){var r={field:"data",bindingEvent:"data",allowBinding:!1,allowSetting:!1,allowGetting:!1,settingEvent:"data",settingTriggersEvent:!1,triggerFnName:"trigger",immutableKeys:{},updateStyle:!1,beforeGet:function(n){},beforeSet:function(n,i){},onSet:function(n){},canSet:function(n){return!0}};return e=be({},r,e),function(n,i){var s=e,o=this,u=o.length!==void 0,l=u?o:[o],f=u?o[0]:o;if(de(n)){var h=n.indexOf(".")!==-1,d=h&&yd(n);if(s.allowGetting&&i===void 0){var c;return f&&(s.beforeGet(f),d&&f._private[s.field][n]===void 0?c=Jv(f._private[s.field],d):c=f._private[s.field][n]),c}else if(s.allowSetting&&i!==void 0){var v=!s.immutableKeys[n];if(v){var p=ao({},n,i);s.beforeSet(o,p);for(var g=0,y=l.length;g<y;g++){var b=l[g];s.canSet(b)&&(d&&f._private[s.field][n]===void 0?vd(b._private[s.field],d,i):b._private[s.field][n]=i)}s.updateStyle&&o.updateStyle(),s.onSet(o),s.settingTriggersEvent&&o[s.triggerFnName](s.settingEvent)}}}else if(s.allowSetting&&Ce(n)){var m=n,T,C,S=Object.keys(m);s.beforeSet(o,m);for(var E=0;E<S.length;E++){T=S[E],C=m[T];var w=!s.immutableKeys[T];if(w)for(var x=0;x<l.length;x++){var D=l[x];s.canSet(D)&&(D._private[s.field][T]=C)}}s.updateStyle&&o.updateStyle(),s.onSet(o),s.settingTriggersEvent&&o[s.triggerFnName](s.settingEvent)}else if(s.allowBinding&&Ge(n)){var L=n;o.on(s.bindingEvent,L)}else if(s.allowGetting&&n===void 0){var A;return f&&(s.beforeGet(f),A=f._private[s.field]),A}return o}},removeData:function(e){var r={field:"data",event:"data",triggerFnName:"trigger",triggerEvent:!1,immutableKeys:{}};return e=be({},r,e),function(n){var i=e,s=this,o=s.length!==void 0,u=o?s:[s];if(de(n)){for(var l=n.split(/\s+/),f=l.length,h=0;h<f;h++){var d=l[h];if(!jt(d)){var c=!i.immutableKeys[d];if(c)for(var v=0,p=u.length;v<p;v++)u[v]._private[i.field][d]=void 0}}i.triggerEvent&&s[i.triggerFnName](i.event)}else if(n===void 0){for(var g=0,y=u.length;g<y;g++)for(var b=u[g]._private[i.field],m=Object.keys(b),T=0;T<m.length;T++){var C=m[T],S=!i.immutableKeys[C];S&&(b[C]=void 0)}i.triggerEvent&&s[i.triggerFnName](i.event)}return s}}},bd={eventAliasesOn:function(e){var r=e;r.addListener=r.listen=r.bind=r.on,r.unlisten=r.unbind=r.off=r.removeListener,r.trigger=r.emit,r.pon=r.promiseOn=function(a,n){var i=this,s=Array.prototype.slice.call(arguments,0);return new $r(function(o,u){var l=function(c){i.off.apply(i,h),o(c)},f=s.concat([l]),h=f.concat([]);i.on.apply(i,f)})}}},Oe={};[fc,md,bd].forEach(function(t){be(Oe,t)});var Ed={animate:Oe.animate(),animation:Oe.animation(),animated:Oe.animated(),clearQueue:Oe.clearQueue(),delay:Oe.delay(),delayAnimation:Oe.delayAnimation(),stop:Oe.stop()},Xa={classes:function(e){var r=this;if(e===void 0){var a=[];return r[0]._private.classes.forEach(function(v){return a.push(v)}),a}else Re(e)||(e=(e||"").match(/\S+/g)||[]);for(var n=[],i=new Ur(e),s=0;s<r.length;s++){for(var o=r[s],u=o._private,l=u.classes,f=!1,h=0;h<e.length;h++){var d=e[h],c=l.has(d);if(!c){f=!0;break}}f||(f=l.size!==e.length),f&&(u.classes=i,n.push(o))}return n.length>0&&this.spawn(n).updateStyle().emit("class"),r},addClass:function(e){return this.toggleClass(e,!0)},hasClass:function(e){var r=this[0];return r!=null&&r._private.classes.has(e)},toggleClass:function(e,r){Re(e)||(e=e.match(/\S+/g)||[]);for(var a=this,n=r===void 0,i=[],s=0,o=a.length;s<o;s++)for(var u=a[s],l=u._private.classes,f=!1,h=0;h<e.length;h++){var d=e[h],c=l.has(d),v=!1;r||n&&!c?(l.add(d),v=!0):(!r||n&&c)&&(l.delete(d),v=!0),!f&&v&&(i.push(u),f=!0)}return i.length>0&&this.spawn(i).updateStyle().emit("class"),a},removeClass:function(e){return this.toggleClass(e,!1)},flashClass:function(e,r){var a=this;if(r==null)r=250;else if(r===0)return a;return a.addClass(e),setTimeout(function(){a.removeClass(e)},r),a}};Xa.className=Xa.classNames=Xa.classes;var Te={metaChar:"[\\!\\\"\\#\\$\\%\\&\\'\\(\\)\\*\\+\\,\\.\\/\\:\\;\\<\\=\\>\\?\\@\\[\\]\\^\\`\\{\\|\\}\\~]",comparatorOp:"=|\\!=|>|>=|<|<=|\\$=|\\^=|\\*=",boolOp:"\\?|\\!|\\^",string:`"(?:\\\\"|[^"])*"|'(?:\\\\'|[^'])*'`,number:He,meta:"degree|indegree|outdegree",separator:"\\s*,\\s*",descendant:"\\s+",child:"\\s+>\\s+",subject:"\\$",group:"node|edge|\\*",directedEdge:"\\s+->\\s+",undirectedEdge:"\\s+<->\\s+"};Te.variable="(?:[\\w-.]|(?:\\\\"+Te.metaChar+"))+";Te.className="(?:[\\w-]|(?:\\\\"+Te.metaChar+"))+";Te.value=Te.string+"|"+Te.number;Te.id=Te.variable;(function(){var t,e,r;for(t=Te.comparatorOp.split("|"),r=0;r<t.length;r++)e=t[r],Te.comparatorOp+="|@"+e;for(t=Te.comparatorOp.split("|"),r=0;r<t.length;r++)e=t[r],!(e.indexOf("!")>=0)&&e!=="="&&(Te.comparatorOp+="|\\!"+e)})();var Ie=function(){return{checks:[]}},oe={GROUP:0,COLLECTION:1,FILTER:2,DATA_COMPARE:3,DATA_EXIST:4,DATA_BOOL:5,META_COMPARE:6,STATE:7,ID:8,CLASS:9,UNDIRECTED_EDGE:10,DIRECTED_EDGE:11,NODE_SOURCE:12,NODE_TARGET:13,NODE_NEIGHBOR:14,CHILD:15,DESCENDANT:16,PARENT:17,ANCESTOR:18,COMPOUND_SPLIT:19,TRUE:20},Jn=[{selector:":selected",matches:function(e){return e.selected()}},{selector:":unselected",matches:function(e){return!e.selected()}},{selector:":selectable",matches:function(e){return e.selectable()}},{selector:":unselectable",matches:function(e){return!e.selectable()}},{selector:":locked",matches:function(e){return e.locked()}},{selector:":unlocked",matches:function(e){return!e.locked()}},{selector:":visible",matches:function(e){return e.visible()}},{selector:":hidden",matches:function(e){return!e.visible()}},{selector:":transparent",matches:function(e){return e.transparent()}},{selector:":grabbed",matches:function(e){return e.grabbed()}},{selector:":free",matches:function(e){return!e.grabbed()}},{selector:":removed",matches:function(e){return e.removed()}},{selector:":inside",matches:function(e){return!e.removed()}},{selector:":grabbable",matches:function(e){return e.grabbable()}},{selector:":ungrabbable",matches:function(e){return!e.grabbable()}},{selector:":animated",matches:function(e){return e.animated()}},{selector:":unanimated",matches:function(e){return!e.animated()}},{selector:":parent",matches:function(e){return e.isParent()}},{selector:":childless",matches:function(e){return e.isChildless()}},{selector:":child",matches:function(e){return e.isChild()}},{selector:":orphan",matches:function(e){return e.isOrphan()}},{selector:":nonorphan",matches:function(e){return e.isChild()}},{selector:":compound",matches:function(e){return e.isNode()?e.isParent():e.source().isParent()||e.target().isParent()}},{selector:":loop",matches:function(e){return e.isLoop()}},{selector:":simple",matches:function(e){return e.isSimple()}},{selector:":active",matches:function(e){return e.active()}},{selector:":inactive",matches:function(e){return!e.active()}},{selector:":backgrounding",matches:function(e){return e.backgrounding()}},{selector:":nonbackgrounding",matches:function(e){return!e.backgrounding()}}].sort(function(t,e){return Il(t.selector,e.selector)}),wd=function(){for(var t={},e,r=0;r<Jn.length;r++)e=Jn[r],t[e.selector]=e.matches;return t}(),xd=function(e,r){return wd[e](r)},Td="("+Jn.map(function(t){return t.selector}).join("|")+")",Cr=function(e){return e.replace(new RegExp("\\\\("+Te.metaChar+")","g"),function(r,a){return a})},Wt=function(e,r,a){e[e.length-1]=a},jn=[{name:"group",query:!0,regex:"("+Te.group+")",populate:function(e,r,a){var n=St(a,1),i=n[0];r.checks.push({type:oe.GROUP,value:i==="*"?i:i+"s"})}},{name:"state",query:!0,regex:Td,populate:function(e,r,a){var n=St(a,1),i=n[0];r.checks.push({type:oe.STATE,value:i})}},{name:"id",query:!0,regex:"\\#("+Te.id+")",populate:function(e,r,a){var n=St(a,1),i=n[0];r.checks.push({type:oe.ID,value:Cr(i)})}},{name:"className",query:!0,regex:"\\.("+Te.className+")",populate:function(e,r,a){var n=St(a,1),i=n[0];r.checks.push({type:oe.CLASS,value:Cr(i)})}},{name:"dataExists",query:!0,regex:"\\[\\s*("+Te.variable+")\\s*\\]",populate:function(e,r,a){var n=St(a,1),i=n[0];r.checks.push({type:oe.DATA_EXIST,field:Cr(i)})}},{name:"dataCompare",query:!0,regex:"\\[\\s*("+Te.variable+")\\s*("+Te.comparatorOp+")\\s*("+Te.value+")\\s*\\]",populate:function(e,r,a){var n=St(a,3),i=n[0],s=n[1],o=n[2],u=new RegExp("^"+Te.string+"$").exec(o)!=null;u?o=o.substring(1,o.length-1):o=parseFloat(o),r.checks.push({type:oe.DATA_COMPARE,field:Cr(i),operator:s,value:o})}},{name:"dataBool",query:!0,regex:"\\[\\s*("+Te.boolOp+")\\s*("+Te.variable+")\\s*\\]",populate:function(e,r,a){var n=St(a,2),i=n[0],s=n[1];r.checks.push({type:oe.DATA_BOOL,field:Cr(s),operator:i})}},{name:"metaCompare",query:!0,regex:"\\[\\[\\s*("+Te.meta+")\\s*("+Te.comparatorOp+")\\s*("+Te.number+")\\s*\\]\\]",populate:function(e,r,a){var n=St(a,3),i=n[0],s=n[1],o=n[2];r.checks.push({type:oe.META_COMPARE,field:Cr(i),operator:s,value:parseFloat(o)})}},{name:"nextQuery",separator:!0,regex:Te.separator,populate:function(e,r){var a=e.currentSubject,n=e.edgeCount,i=e.compoundCount,s=e[e.length-1];a!=null&&(s.subject=a,e.currentSubject=null),s.edgeCount=n,s.compoundCount=i,e.edgeCount=0,e.compoundCount=0;var o=e[e.length++]=Ie();return o}},{name:"directedEdge",separator:!0,regex:Te.directedEdge,populate:function(e,r){if(e.currentSubject==null){var a=Ie(),n=r,i=Ie();return a.checks.push({type:oe.DIRECTED_EDGE,source:n,target:i}),Wt(e,r,a),e.edgeCount++,i}else{var s=Ie(),o=r,u=Ie();return s.checks.push({type:oe.NODE_SOURCE,source:o,target:u}),Wt(e,r,s),e.edgeCount++,u}}},{name:"undirectedEdge",separator:!0,regex:Te.undirectedEdge,populate:function(e,r){if(e.currentSubject==null){var a=Ie(),n=r,i=Ie();return a.checks.push({type:oe.UNDIRECTED_EDGE,nodes:[n,i]}),Wt(e,r,a),e.edgeCount++,i}else{var s=Ie(),o=r,u=Ie();return s.checks.push({type:oe.NODE_NEIGHBOR,node:o,neighbor:u}),Wt(e,r,s),u}}},{name:"child",separator:!0,regex:Te.child,populate:function(e,r){if(e.currentSubject==null){var a=Ie(),n=Ie(),i=e[e.length-1];return a.checks.push({type:oe.CHILD,parent:i,child:n}),Wt(e,r,a),e.compoundCount++,n}else if(e.currentSubject===r){var s=Ie(),o=e[e.length-1],u=Ie(),l=Ie(),f=Ie(),h=Ie();return s.checks.push({type:oe.COMPOUND_SPLIT,left:o,right:u,subject:l}),l.checks=r.checks,r.checks=[{type:oe.TRUE}],h.checks.push({type:oe.TRUE}),u.checks.push({type:oe.PARENT,parent:h,child:f}),Wt(e,o,s),e.currentSubject=l,e.compoundCount++,f}else{var d=Ie(),c=Ie(),v=[{type:oe.PARENT,parent:d,child:c}];return d.checks=r.checks,r.checks=v,e.compoundCount++,c}}},{name:"descendant",separator:!0,regex:Te.descendant,populate:function(e,r){if(e.currentSubject==null){var a=Ie(),n=Ie(),i=e[e.length-1];return a.checks.push({type:oe.DESCENDANT,ancestor:i,descendant:n}),Wt(e,r,a),e.compoundCount++,n}else if(e.currentSubject===r){var s=Ie(),o=e[e.length-1],u=Ie(),l=Ie(),f=Ie(),h=Ie();return s.checks.push({type:oe.COMPOUND_SPLIT,left:o,right:u,subject:l}),l.checks=r.checks,r.checks=[{type:oe.TRUE}],h.checks.push({type:oe.TRUE}),u.checks.push({type:oe.ANCESTOR,ancestor:h,descendant:f}),Wt(e,o,s),e.currentSubject=l,e.compoundCount++,f}else{var d=Ie(),c=Ie(),v=[{type:oe.ANCESTOR,ancestor:d,descendant:c}];return d.checks=r.checks,r.checks=v,e.compoundCount++,c}}},{name:"subject",modifier:!0,regex:Te.subject,populate:function(e,r){if(e.currentSubject!=null&&e.currentSubject!==r)return Ne("Redefinition of subject in selector `"+e.toString()+"`"),!1;e.currentSubject=r;var a=e[e.length-1],n=a.checks[0],i=n==null?null:n.type;i===oe.DIRECTED_EDGE?n.type=oe.NODE_TARGET:i===oe.UNDIRECTED_EDGE&&(n.type=oe.NODE_NEIGHBOR,n.node=n.nodes[1],n.neighbor=n.nodes[0],n.nodes=null)}}];jn.forEach(function(t){return t.regexObj=new RegExp("^"+t.regex)});var Cd=function(e){for(var r,a,n,i=0;i<jn.length;i++){var s=jn[i],o=s.name,u=e.match(s.regexObj);if(u!=null){a=u,r=s,n=o;var l=u[0];e=e.substring(l.length);break}}return{expr:r,match:a,name:n,remaining:e}},Dd=function(e){var r=e.match(/^\s+/);if(r){var a=r[0];e=e.substring(a.length)}return e},Sd=function(e){var r=this,a=r.inputText=e,n=r[0]=Ie();for(r.length=1,a=Dd(a);;){var i=Cd(a);if(i.expr==null)return Ne("The selector `"+e+"`is invalid"),!1;var s=i.match.slice(1),o=i.expr.populate(r,n,s);if(o===!1)return!1;if(o!=null&&(n=o),a=i.remaining,a.match(/^\s*$/))break}var u=r[r.length-1];r.currentSubject!=null&&(u.subject=r.currentSubject),u.edgeCount=r.edgeCount,u.compoundCount=r.compoundCount;for(var l=0;l<r.length;l++){var f=r[l];if(f.compoundCount>0&&f.edgeCount>0)return Ne("The selector `"+e+"` is invalid because it uses both a compound selector and an edge selector"),!1;if(f.edgeCount>1)return Ne("The selector `"+e+"` is invalid because it uses multiple edge selectors"),!1;f.edgeCount===1&&Ne("The selector `"+e+"` is deprecated.  Edge selectors do not take effect on changes to source and target nodes after an edge is added, for performance reasons.  Use a class or data selector on edges instead, updating the class or data of an edge when your app detects a change in source or target nodes.")}return!0},Ld=function(){if(this.toStringCache!=null)return this.toStringCache;for(var e=function(f){return f??""},r=function(f){return de(f)?'"'+f+'"':e(f)},a=function(f){return" "+f+" "},n=function(f,h){var d=f.type,c=f.value;switch(d){case oe.GROUP:{var v=e(c);return v.substring(0,v.length-1)}case oe.DATA_COMPARE:{var p=f.field,g=f.operator;return"["+p+a(e(g))+r(c)+"]"}case oe.DATA_BOOL:{var y=f.operator,b=f.field;return"["+e(y)+b+"]"}case oe.DATA_EXIST:{var m=f.field;return"["+m+"]"}case oe.META_COMPARE:{var T=f.operator,C=f.field;return"[["+C+a(e(T))+r(c)+"]]"}case oe.STATE:return c;case oe.ID:return"#"+c;case oe.CLASS:return"."+c;case oe.PARENT:case oe.CHILD:return i(f.parent,h)+a(">")+i(f.child,h);case oe.ANCESTOR:case oe.DESCENDANT:return i(f.ancestor,h)+" "+i(f.descendant,h);case oe.COMPOUND_SPLIT:{var S=i(f.left,h),E=i(f.subject,h),w=i(f.right,h);return S+(S.length>0?" ":"")+E+w}case oe.TRUE:return""}},i=function(f,h){return f.checks.reduce(function(d,c,v){return d+(h===f&&v===0?"$":"")+n(c,h)},"")},s="",o=0;o<this.length;o++){var u=this[o];s+=i(u,u.subject),this.length>1&&o<this.length-1&&(s+=", ")}return this.toStringCache=s,s},Ad={parse:Sd,toString:Ld},qo=function(e,r,a){var n,i=de(e),s=ie(e),o=de(a),u,l,f=!1,h=!1,d=!1;switch(r.indexOf("!")>=0&&(r=r.replace("!",""),h=!0),r.indexOf("@")>=0&&(r=r.replace("@",""),f=!0),(i||o||f)&&(u=!i&&!s?"":""+e,l=""+a),f&&(e=u=u.toLowerCase(),a=l=l.toLowerCase()),r){case"*=":n=u.indexOf(l)>=0;break;case"$=":n=u.indexOf(l,u.length-l.length)>=0;break;case"^=":n=u.indexOf(l)===0;break;case"=":n=e===a;break;case">":d=!0,n=e>a;break;case">=":d=!0,n=e>=a;break;case"<":d=!0,n=e<a;break;case"<=":d=!0,n=e<=a;break;default:n=!1;break}return h&&(e!=null||!d)&&(n=!n),n},Od=function(e,r){switch(r){case"?":return!!e;case"!":return!e;case"^":return e===void 0}},Nd=function(e){return e!==void 0},Li=function(e,r){return e.data(r)},Id=function(e,r){return e[r]()},Ve=[],Be=function(e,r){return e.checks.every(function(a){return Ve[a.type](a,r)})};Ve[oe.GROUP]=function(t,e){var r=t.value;return r==="*"||r===e.group()};Ve[oe.STATE]=function(t,e){var r=t.value;return xd(r,e)};Ve[oe.ID]=function(t,e){var r=t.value;return e.id()===r};Ve[oe.CLASS]=function(t,e){var r=t.value;return e.hasClass(r)};Ve[oe.META_COMPARE]=function(t,e){var r=t.field,a=t.operator,n=t.value;return qo(Id(e,r),a,n)};Ve[oe.DATA_COMPARE]=function(t,e){var r=t.field,a=t.operator,n=t.value;return qo(Li(e,r),a,n)};Ve[oe.DATA_BOOL]=function(t,e){var r=t.field,a=t.operator;return Od(Li(e,r),a)};Ve[oe.DATA_EXIST]=function(t,e){var r=t.field;return t.operator,Nd(Li(e,r))};Ve[oe.UNDIRECTED_EDGE]=function(t,e){var r=t.nodes[0],a=t.nodes[1],n=e.source(),i=e.target();return Be(r,n)&&Be(a,i)||Be(a,n)&&Be(r,i)};Ve[oe.NODE_NEIGHBOR]=function(t,e){return Be(t.node,e)&&e.neighborhood().some(function(r){return r.isNode()&&Be(t.neighbor,r)})};Ve[oe.DIRECTED_EDGE]=function(t,e){return Be(t.source,e.source())&&Be(t.target,e.target())};Ve[oe.NODE_SOURCE]=function(t,e){return Be(t.source,e)&&e.outgoers().some(function(r){return r.isNode()&&Be(t.target,r)})};Ve[oe.NODE_TARGET]=function(t,e){return Be(t.target,e)&&e.incomers().some(function(r){return r.isNode()&&Be(t.source,r)})};Ve[oe.CHILD]=function(t,e){return Be(t.child,e)&&Be(t.parent,e.parent())};Ve[oe.PARENT]=function(t,e){return Be(t.parent,e)&&e.children().some(function(r){return Be(t.child,r)})};Ve[oe.DESCENDANT]=function(t,e){return Be(t.descendant,e)&&e.ancestors().some(function(r){return Be(t.ancestor,r)})};Ve[oe.ANCESTOR]=function(t,e){return Be(t.ancestor,e)&&e.descendants().some(function(r){return Be(t.descendant,r)})};Ve[oe.COMPOUND_SPLIT]=function(t,e){return Be(t.subject,e)&&Be(t.left,e)&&Be(t.right,e)};Ve[oe.TRUE]=function(){return!0};Ve[oe.COLLECTION]=function(t,e){var r=t.value;return r.has(e)};Ve[oe.FILTER]=function(t,e){var r=t.value;return r(e)};var Md=function(e){var r=this;if(r.length===1&&r[0].checks.length===1&&r[0].checks[0].type===oe.ID)return e.getElementById(r[0].checks[0].value).collection();var a=function(i){for(var s=0;s<r.length;s++){var o=r[s];if(Be(o,i))return!0}return!1};return r.text()==null&&(a=function(){return!0}),e.filter(a)},Rd=function(e){for(var r=this,a=0;a<r.length;a++){var n=r[a];if(Be(n,e))return!0}return!1},kd={matches:Rd,filter:Md},tr=function(e){this.inputText=e,this.currentSubject=null,this.compoundCount=0,this.edgeCount=0,this.length=0,e==null||de(e)&&e.match(/^\s*$/)||(pt(e)?this.addQuery({checks:[{type:oe.COLLECTION,value:e.collection()}]}):Ge(e)?this.addQuery({checks:[{type:oe.FILTER,value:e}]}):de(e)?this.parse(e)||(this.invalid=!0):ze("A selector must be created from a string; found "))},rr=tr.prototype;[Ad,kd].forEach(function(t){return be(rr,t)});rr.text=function(){return this.inputText};rr.size=function(){return this.length};rr.eq=function(t){return this[t]};rr.sameText=function(t){return!this.invalid&&!t.invalid&&this.text()===t.text()};rr.addQuery=function(t){this[this.length++]=t};rr.selector=rr.toString;var Qt={allAre:function(e){var r=new tr(e);return this.every(function(a){return r.matches(a)})},is:function(e){var r=new tr(e);return this.some(function(a){return r.matches(a)})},some:function(e,r){for(var a=0;a<this.length;a++){var n=r?e.apply(r,[this[a],a,this]):e(this[a],a,this);if(n)return!0}return!1},every:function(e,r){for(var a=0;a<this.length;a++){var n=r?e.apply(r,[this[a],a,this]):e(this[a],a,this);if(!n)return!1}return!0},same:function(e){if(this===e)return!0;e=this.cy().collection(e);var r=this.length,a=e.length;return r!==a?!1:r===1?this[0]===e[0]:this.every(function(n){return e.hasElementWithId(n.id())})},anySame:function(e){return e=this.cy().collection(e),this.some(function(r){return e.hasElementWithId(r.id())})},allAreNeighbors:function(e){e=this.cy().collection(e);var r=this.neighborhood();return e.every(function(a){return r.hasElementWithId(a.id())})},contains:function(e){e=this.cy().collection(e);var r=this;return e.every(function(a){return r.hasElementWithId(a.id())})}};Qt.allAreNeighbours=Qt.allAreNeighbors;Qt.has=Qt.contains;Qt.equal=Qt.equals=Qt.same;var wt=function(e,r){return function(n,i,s,o){var u=n,l=this,f;if(u==null?f="":pt(u)&&u.length===1&&(f=u.id()),l.length===1&&f){var h=l[0]._private,d=h.traversalCache=h.traversalCache||{},c=d[r]=d[r]||[],v=dr(f),p=c[v];return p||(c[v]=e.call(l,n,i,s,o))}else return e.call(l,n,i,s,o)}},Vr={parent:function(e){var r=[];if(this.length===1){var a=this[0]._private.parent;if(a)return a}for(var n=0;n<this.length;n++){var i=this[n],s=i._private.parent;s&&r.push(s)}return this.spawn(r,!0).filter(e)},parents:function(e){for(var r=[],a=this.parent();a.nonempty();){for(var n=0;n<a.length;n++){var i=a[n];r.push(i)}a=a.parent()}return this.spawn(r,!0).filter(e)},commonAncestors:function(e){for(var r,a=0;a<this.length;a++){var n=this[a],i=n.parents();r=r||i,r=r.intersect(i)}return r.filter(e)},orphans:function(e){return this.stdFilter(function(r){return r.isOrphan()}).filter(e)},nonorphans:function(e){return this.stdFilter(function(r){return r.isChild()}).filter(e)},children:wt(function(t){for(var e=[],r=0;r<this.length;r++)for(var a=this[r],n=a._private.children,i=0;i<n.length;i++)e.push(n[i]);return this.spawn(e,!0).filter(t)},"children"),siblings:function(e){return this.parent().children().not(this).filter(e)},isParent:function(){var e=this[0];if(e)return e.isNode()&&e._private.children.length!==0},isChildless:function(){var e=this[0];if(e)return e.isNode()&&e._private.children.length===0},isChild:function(){var e=this[0];if(e)return e.isNode()&&e._private.parent!=null},isOrphan:function(){var e=this[0];if(e)return e.isNode()&&e._private.parent==null},descendants:function(e){var r=[];function a(n){for(var i=0;i<n.length;i++){var s=n[i];r.push(s),s.children().nonempty()&&a(s.children())}}return a(this.children()),this.spawn(r,!0).filter(e)}};function Ai(t,e,r,a){for(var n=[],i=new Ur,s=t.cy(),o=s.hasCompoundNodes(),u=0;u<t.length;u++){var l=t[u];r?n.push(l):o&&a(n,i,l)}for(;n.length>0;){var f=n.shift();e(f),i.add(f.id()),o&&a(n,i,f)}return t}function Wo(t,e,r){if(r.isParent())for(var a=r._private.children,n=0;n<a.length;n++){var i=a[n];e.has(i.id())||t.push(i)}}Vr.forEachDown=function(t){var e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!0;return Ai(this,t,e,Wo)};function Ko(t,e,r){if(r.isChild()){var a=r._private.parent;e.has(a.id())||t.push(a)}}Vr.forEachUp=function(t){var e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!0;return Ai(this,t,e,Ko)};function Pd(t,e,r){Ko(t,e,r),Wo(t,e,r)}Vr.forEachUpAndDown=function(t){var e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!0;return Ai(this,t,e,Pd)};Vr.ancestors=Vr.parents;var ma,Zo;ma=Zo={data:Oe.data({field:"data",bindingEvent:"data",allowBinding:!0,allowSetting:!0,settingEvent:"data",settingTriggersEvent:!0,triggerFnName:"trigger",allowGetting:!0,immutableKeys:{id:!0,source:!0,target:!0,parent:!0},updateStyle:!0}),removeData:Oe.removeData({field:"data",event:"data",triggerFnName:"trigger",triggerEvent:!0,immutableKeys:{id:!0,source:!0,target:!0,parent:!0},updateStyle:!0}),scratch:Oe.data({field:"scratch",bindingEvent:"scratch",allowBinding:!0,allowSetting:!0,settingEvent:"scratch",settingTriggersEvent:!0,triggerFnName:"trigger",allowGetting:!0,updateStyle:!0}),removeScratch:Oe.removeData({field:"scratch",event:"scratch",triggerFnName:"trigger",triggerEvent:!0,updateStyle:!0}),rscratch:Oe.data({field:"rscratch",allowBinding:!1,allowSetting:!0,settingTriggersEvent:!1,allowGetting:!0}),removeRscratch:Oe.removeData({field:"rscratch",triggerEvent:!1}),id:function(){var e=this[0];if(e)return e._private.data.id}};ma.attr=ma.data;ma.removeAttr=ma.removeData;var Bd=Zo,xn={};function zn(t){return function(e){var r=this;if(e===void 0&&(e=!0),r.length!==0)if(r.isNode()&&!r.removed()){for(var a=0,n=r[0],i=n._private.edges,s=0;s<i.length;s++){var o=i[s];!e&&o.isLoop()||(a+=t(n,o))}return a}else return}}be(xn,{degree:zn(function(t,e){return e.source().same(e.target())?2:1}),indegree:zn(function(t,e){return e.target().same(t)?1:0}),outdegree:zn(function(t,e){return e.source().same(t)?1:0})});function Dr(t,e){return function(r){for(var a,n=this.nodes(),i=0;i<n.length;i++){var s=n[i],o=s[t](r);o!==void 0&&(a===void 0||e(o,a))&&(a=o)}return a}}be(xn,{minDegree:Dr("degree",function(t,e){return t<e}),maxDegree:Dr("degree",function(t,e){return t>e}),minIndegree:Dr("indegree",function(t,e){return t<e}),maxIndegree:Dr("indegree",function(t,e){return t>e}),minOutdegree:Dr("outdegree",function(t,e){return t<e}),maxOutdegree:Dr("outdegree",function(t,e){return t>e})});be(xn,{totalDegree:function(e){for(var r=0,a=this.nodes(),n=0;n<a.length;n++)r+=a[n].degree(e);return r}});var Ot,Qo,Jo=function(e,r,a){for(var n=0;n<e.length;n++){var i=e[n];if(!i.locked()){var s=i._private.position,o={x:r.x!=null?r.x-s.x:0,y:r.y!=null?r.y-s.y:0};i.isParent()&&!(o.x===0&&o.y===0)&&i.children().shift(o,a),i.dirtyBoundingBoxCache()}}},ms={field:"position",bindingEvent:"position",allowBinding:!0,allowSetting:!0,settingEvent:"position",settingTriggersEvent:!0,triggerFnName:"emitAndNotify",allowGetting:!0,validKeys:["x","y"],beforeGet:function(e){e.updateCompoundBounds()},beforeSet:function(e,r){Jo(e,r,!1)},onSet:function(e){e.dirtyCompoundBoundsCache()},canSet:function(e){return!e.locked()}};Ot=Qo={position:Oe.data(ms),silentPosition:Oe.data(be({},ms,{allowBinding:!1,allowSetting:!0,settingTriggersEvent:!1,allowGetting:!1,beforeSet:function(e,r){Jo(e,r,!0)},onSet:function(e){e.dirtyCompoundBoundsCache()}})),positions:function(e,r){if(Ce(e))r?this.silentPosition(e):this.position(e);else if(Ge(e)){var a=e,n=this.cy();n.startBatch();for(var i=0;i<this.length;i++){var s=this[i],o=void 0;(o=a(s,i))&&(r?s.silentPosition(o):s.position(o))}n.endBatch()}return this},silentPositions:function(e){return this.positions(e,!0)},shift:function(e,r,a){var n;if(Ce(e)?(n={x:ie(e.x)?e.x:0,y:ie(e.y)?e.y:0},a=r):de(e)&&ie(r)&&(n={x:0,y:0},n[e]=r),n!=null){var i=this.cy();i.startBatch();for(var s=0;s<this.length;s++){var o=this[s];if(!(i.hasCompoundNodes()&&o.isChild()&&o.ancestors().anySame(this))){var u=o.position(),l={x:u.x+n.x,y:u.y+n.y};a?o.silentPosition(l):o.position(l)}}i.endBatch()}return this},silentShift:function(e,r){return Ce(e)?this.shift(e,!0):de(e)&&ie(r)&&this.shift(e,r,!0),this},renderedPosition:function(e,r){var a=this[0],n=this.cy(),i=n.zoom(),s=n.pan(),o=Ce(e)?e:void 0,u=o!==void 0||r!==void 0&&de(e);if(a&&a.isNode())if(u)for(var l=0;l<this.length;l++){var f=this[l];r!==void 0?f.position(e,(r-s[e])/i):o!==void 0&&f.position(Co(o,i,s))}else{var h=a.position();return o=yn(h,i,s),e===void 0?o:o[e]}else if(!u)return;return this},relativePosition:function(e,r){var a=this[0],n=this.cy(),i=Ce(e)?e:void 0,s=i!==void 0||r!==void 0&&de(e),o=n.hasCompoundNodes();if(a&&a.isNode())if(s)for(var u=0;u<this.length;u++){var l=this[u],f=o?l.parent():null,h=f&&f.length>0,d=h;h&&(f=f[0]);var c=d?f.position():{x:0,y:0};r!==void 0?l.position(e,r+c[e]):i!==void 0&&l.position({x:i.x+c.x,y:i.y+c.y})}else{var v=a.position(),p=o?a.parent():null,g=p&&p.length>0,y=g;g&&(p=p[0]);var b=y?p.position():{x:0,y:0};return i={x:v.x-b.x,y:v.y-b.y},e===void 0?i:i[e]}else if(!s)return;return this}};Ot.modelPosition=Ot.point=Ot.position;Ot.modelPositions=Ot.points=Ot.positions;Ot.renderedPoint=Ot.renderedPosition;Ot.relativePoint=Ot.relativePosition;var Fd=Qo,Br,ir;Br=ir={};ir.renderedBoundingBox=function(t){var e=this.boundingBox(t),r=this.cy(),a=r.zoom(),n=r.pan(),i=e.x1*a+n.x,s=e.x2*a+n.x,o=e.y1*a+n.y,u=e.y2*a+n.y;return{x1:i,x2:s,y1:o,y2:u,w:s-i,h:u-o}};ir.dirtyCompoundBoundsCache=function(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!1,e=this.cy();return!e.styleEnabled()||!e.hasCompoundNodes()?this:(this.forEachUp(function(r){if(r.isParent()){var a=r._private;a.compoundBoundsClean=!1,a.bbCache=null,t||r.emitAndNotify("bounds")}}),this)};ir.updateCompoundBounds=function(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!1,e=this.cy();if(!e.styleEnabled()||!e.hasCompoundNodes())return this;if(!t&&e.batching())return this;function r(s){if(!s.isParent())return;var o=s._private,u=s.children(),l=s.pstyle("compound-sizing-wrt-labels").value==="include",f={width:{val:s.pstyle("min-width").pfValue,left:s.pstyle("min-width-bias-left"),right:s.pstyle("min-width-bias-right")},height:{val:s.pstyle("min-height").pfValue,top:s.pstyle("min-height-bias-top"),bottom:s.pstyle("min-height-bias-bottom")}},h=u.boundingBox({includeLabels:l,includeOverlays:!1,useCache:!1}),d=o.position;(h.w===0||h.h===0)&&(h={w:s.pstyle("width").pfValue,h:s.pstyle("height").pfValue},h.x1=d.x-h.w/2,h.x2=d.x+h.w/2,h.y1=d.y-h.h/2,h.y2=d.y+h.h/2);function c(x,D,L){var A=0,N=0,O=D+L;return x>0&&O>0&&(A=D/O*x,N=L/O*x),{biasDiff:A,biasComplementDiff:N}}function v(x,D,L,A){if(L.units==="%")switch(A){case"width":return x>0?L.pfValue*x:0;case"height":return D>0?L.pfValue*D:0;case"average":return x>0&&D>0?L.pfValue*(x+D)/2:0;case"min":return x>0&&D>0?x>D?L.pfValue*D:L.pfValue*x:0;case"max":return x>0&&D>0?x>D?L.pfValue*x:L.pfValue*D:0;default:return 0}else return L.units==="px"?L.pfValue:0}var p=f.width.left.value;f.width.left.units==="px"&&f.width.val>0&&(p=p*100/f.width.val);var g=f.width.right.value;f.width.right.units==="px"&&f.width.val>0&&(g=g*100/f.width.val);var y=f.height.top.value;f.height.top.units==="px"&&f.height.val>0&&(y=y*100/f.height.val);var b=f.height.bottom.value;f.height.bottom.units==="px"&&f.height.val>0&&(b=b*100/f.height.val);var m=c(f.width.val-h.w,p,g),T=m.biasDiff,C=m.biasComplementDiff,S=c(f.height.val-h.h,y,b),E=S.biasDiff,w=S.biasComplementDiff;o.autoPadding=v(h.w,h.h,s.pstyle("padding"),s.pstyle("padding-relative-to").value),o.autoWidth=Math.max(h.w,f.width.val),d.x=(-T+h.x1+h.x2+C)/2,o.autoHeight=Math.max(h.h,f.height.val),d.y=(-E+h.y1+h.y2+w)/2}for(var a=0;a<this.length;a++){var n=this[a],i=n._private;(!i.compoundBoundsClean||t)&&(r(n),e.batching()||(i.compoundBoundsClean=!0))}return this};var Et=function(e){return e===1/0||e===-1/0?0:e},Lt=function(e,r,a,n,i){n-r===0||i-a===0||r==null||a==null||n==null||i==null||(e.x1=r<e.x1?r:e.x1,e.x2=n>e.x2?n:e.x2,e.y1=a<e.y1?a:e.y1,e.y2=i>e.y2?i:e.y2,e.w=e.x2-e.x1,e.h=e.y2-e.y1)},lr=function(e,r){return r==null?e:Lt(e,r.x1,r.y1,r.x2,r.y2)},ta=function(e,r,a){return At(e,r,a)},Ga=function(e,r,a){if(!r.cy().headless()){var n=r._private,i=n.rstyle,s=i.arrowWidth/2,o=r.pstyle(a+"-arrow-shape").value,u,l;if(o!=="none"){a==="source"?(u=i.srcX,l=i.srcY):a==="target"?(u=i.tgtX,l=i.tgtY):(u=i.midX,l=i.midY);var f=n.arrowBounds=n.arrowBounds||{},h=f[a]=f[a]||{};h.x1=u-s,h.y1=l-s,h.x2=u+s,h.y2=l+s,h.w=h.x2-h.x1,h.h=h.y2-h.y1,_a(h,1),Lt(e,h.x1,h.y1,h.x2,h.y2)}}},Vn=function(e,r,a){if(!r.cy().headless()){var n;a?n=a+"-":n="";var i=r._private,s=i.rstyle,o=r.pstyle(n+"label").strValue;if(o){var u=r.pstyle("text-halign"),l=r.pstyle("text-valign"),f=ta(s,"labelWidth",a),h=ta(s,"labelHeight",a),d=ta(s,"labelX",a),c=ta(s,"labelY",a),v=r.pstyle(n+"text-margin-x").pfValue,p=r.pstyle(n+"text-margin-y").pfValue,g=r.isEdge(),y=r.pstyle(n+"text-rotation"),b=r.pstyle("text-outline-width").pfValue,m=r.pstyle("text-border-width").pfValue,T=m/2,C=r.pstyle("text-background-padding").pfValue,S=2,E=h,w=f,x=w/2,D=E/2,L,A,N,O;if(g)L=d-x,A=d+x,N=c-D,O=c+D;else{switch(u.value){case"left":L=d-w,A=d;break;case"center":L=d-x,A=d+x;break;case"right":L=d,A=d+w;break}switch(l.value){case"top":N=c-E,O=c;break;case"center":N=c-D,O=c+D;break;case"bottom":N=c,O=c+E;break}}L+=v-Math.max(b,T)-C-S,A+=v+Math.max(b,T)+C+S,N+=p-Math.max(b,T)-C-S,O+=p+Math.max(b,T)+C+S;var M=a||"main",R=i.labelBounds,k=R[M]=R[M]||{};k.x1=L,k.y1=N,k.x2=A,k.y2=O,k.w=A-L,k.h=O-N;var P=g&&y.strValue==="autorotate",B=y.pfValue!=null&&y.pfValue!==0;if(P||B){var z=P?ta(i.rstyle,"labelAngle",a):y.pfValue,G=Math.cos(z),F=Math.sin(z),U=(L+A)/2,Y=(N+O)/2;if(!g){switch(u.value){case"left":U=A;break;case"right":U=L;break}switch(l.value){case"top":Y=O;break;case"bottom":Y=N;break}}var W=function(ce,te){return ce=ce-U,te=te-Y,{x:ce*G-te*F+U,y:ce*F+te*G+Y}},K=W(L,N),j=W(L,O),_=W(A,N),V=W(A,O);L=Math.min(K.x,j.x,_.x,V.x),A=Math.max(K.x,j.x,_.x,V.x),N=Math.min(K.y,j.y,_.y,V.y),O=Math.max(K.y,j.y,_.y,V.y)}var H=M+"Rot",Q=R[H]=R[H]||{};Q.x1=L,Q.y1=N,Q.x2=A,Q.y2=O,Q.w=A-L,Q.h=O-N,Lt(e,L,N,A,O),Lt(i.labelBounds.all,L,N,A,O)}return e}},Gd=function(e,r){if(!r.cy().headless()){var a=r.pstyle("outline-opacity").value,n=r.pstyle("outline-width").value;if(a>0&&n>0){var i=r.pstyle("outline-offset").value,s=r.pstyle("shape").value,o=n+i,u=(e.w+o*2)/e.w,l=(e.h+o*2)/e.h,f=0,h=0;["diamond","pentagon","round-triangle"].includes(s)?(u=(e.w+o*2.4)/e.w,h=-o/3.6):["concave-hexagon","rhomboid","right-rhomboid"].includes(s)?u=(e.w+o*2.4)/e.w:s==="star"?(u=(e.w+o*2.8)/e.w,l=(e.h+o*2.6)/e.h,h=-o/3.8):s==="triangle"?(u=(e.w+o*2.8)/e.w,l=(e.h+o*2.4)/e.h,h=-o/1.4):s==="vee"&&(u=(e.w+o*4.4)/e.w,l=(e.h+o*3.8)/e.h,h=-o*.5);var d=e.h*l-e.h,c=e.w*u-e.w;if(Ha(e,[Math.ceil(d/2),Math.ceil(c/2)]),f!=0||h!==0){var v=ih(e,f,h);So(e,v)}}}},zd=function(e,r){var a=e._private.cy,n=a.styleEnabled(),i=a.headless(),s=gt(),o=e._private,u=e.isNode(),l=e.isEdge(),f,h,d,c,v,p,g=o.rstyle,y=u&&n?e.pstyle("bounds-expansion").pfValue:[0],b=function(ue){return ue.pstyle("display").value!=="none"},m=!n||b(e)&&(!l||b(e.source())&&b(e.target()));if(m){var T=0,C=0;n&&r.includeOverlays&&(T=e.pstyle("overlay-opacity").value,T!==0&&(C=e.pstyle("overlay-padding").value));var S=0,E=0;n&&r.includeUnderlays&&(S=e.pstyle("underlay-opacity").value,S!==0&&(E=e.pstyle("underlay-padding").value));var w=Math.max(C,E),x=0,D=0;if(n&&(x=e.pstyle("width").pfValue,D=x/2),u&&r.includeNodes){var L=e.position();v=L.x,p=L.y;var A=e.outerWidth(),N=A/2,O=e.outerHeight(),M=O/2;f=v-N,h=v+N,d=p-M,c=p+M,Lt(s,f,d,h,c),n&&r.includeOutlines&&Gd(s,e)}else if(l&&r.includeEdges)if(n&&!i){var R=e.pstyle("curve-style").strValue;if(f=Math.min(g.srcX,g.midX,g.tgtX),h=Math.max(g.srcX,g.midX,g.tgtX),d=Math.min(g.srcY,g.midY,g.tgtY),c=Math.max(g.srcY,g.midY,g.tgtY),f-=D,h+=D,d-=D,c+=D,Lt(s,f,d,h,c),R==="haystack"){var k=g.haystackPts;if(k&&k.length===2){if(f=k[0].x,d=k[0].y,h=k[1].x,c=k[1].y,f>h){var P=f;f=h,h=P}if(d>c){var B=d;d=c,c=B}Lt(s,f-D,d-D,h+D,c+D)}}else if(R==="bezier"||R==="unbundled-bezier"||R.endsWith("segments")||R.endsWith("taxi")){var z;switch(R){case"bezier":case"unbundled-bezier":z=g.bezierPts;break;case"segments":case"taxi":case"round-segments":case"round-taxi":z=g.linePts;break}if(z!=null)for(var G=0;G<z.length;G++){var F=z[G];f=F.x-D,h=F.x+D,d=F.y-D,c=F.y+D,Lt(s,f,d,h,c)}}}else{var U=e.source(),Y=U.position(),W=e.target(),K=W.position();if(f=Y.x,h=K.x,d=Y.y,c=K.y,f>h){var j=f;f=h,h=j}if(d>c){var _=d;d=c,c=_}f-=D,h+=D,d-=D,c+=D,Lt(s,f,d,h,c)}if(n&&r.includeEdges&&l&&(Ga(s,e,"mid-source"),Ga(s,e,"mid-target"),Ga(s,e,"source"),Ga(s,e,"target")),n){var V=e.pstyle("ghost").value==="yes";if(V){var H=e.pstyle("ghost-offset-x").pfValue,Q=e.pstyle("ghost-offset-y").pfValue;Lt(s,s.x1+H,s.y1+Q,s.x2+H,s.y2+Q)}}var ne=o.bodyBounds=o.bodyBounds||{};Qi(ne,s),Ha(ne,y),_a(ne,1),n&&(f=s.x1,h=s.x2,d=s.y1,c=s.y2,Lt(s,f-w,d-w,h+w,c+w));var ce=o.overlayBounds=o.overlayBounds||{};Qi(ce,s),Ha(ce,y),_a(ce,1);var te=o.labelBounds=o.labelBounds||{};te.all!=null?nh(te.all):te.all=gt(),n&&r.includeLabels&&(r.includeMainLabels&&Vn(s,e,null),l&&(r.includeSourceLabels&&Vn(s,e,"source"),r.includeTargetLabels&&Vn(s,e,"target")))}return s.x1=Et(s.x1),s.y1=Et(s.y1),s.x2=Et(s.x2),s.y2=Et(s.y2),s.w=Et(s.x2-s.x1),s.h=Et(s.y2-s.y1),s.w>0&&s.h>0&&m&&(Ha(s,y),_a(s,1)),s},jo=function(e){var r=0,a=function(s){return(s?1:0)<<r++},n=0;return n+=a(e.incudeNodes),n+=a(e.includeEdges),n+=a(e.includeLabels),n+=a(e.includeMainLabels),n+=a(e.includeSourceLabels),n+=a(e.includeTargetLabels),n+=a(e.includeOverlays),n+=a(e.includeOutlines),n},eu=function(e){if(e.isEdge()){var r=e.source().position(),a=e.target().position(),n=function(s){return Math.round(s)};return Df([n(r.x),n(r.y),n(a.x),n(a.y)])}else return 0},bs=function(e,r){var a=e._private,n,i=e.isEdge(),s=r==null?Es:jo(r),o=s===Es,u=eu(e),l=a.bbCachePosKey===u,f=r.useCache&&l,h=function(p){return p._private.bbCache==null||p._private.styleDirty},d=!f||h(e)||i&&h(e.source())||h(e.target());if(d?(l||e.recalculateRenderedStyle(f),n=zd(e,ba),a.bbCache=n,a.bbCachePosKey=u):n=a.bbCache,!o){var c=e.isNode();n=gt(),(r.includeNodes&&c||r.includeEdges&&!c)&&(r.includeOverlays?lr(n,a.overlayBounds):lr(n,a.bodyBounds)),r.includeLabels&&(r.includeMainLabels&&(!i||r.includeSourceLabels&&r.includeTargetLabels)?lr(n,a.labelBounds.all):(r.includeMainLabels&&lr(n,a.labelBounds.mainRot),r.includeSourceLabels&&lr(n,a.labelBounds.sourceRot),r.includeTargetLabels&&lr(n,a.labelBounds.targetRot))),n.w=n.x2-n.x1,n.h=n.y2-n.y1}return n},ba={includeNodes:!0,includeEdges:!0,includeLabels:!0,includeMainLabels:!0,includeSourceLabels:!0,includeTargetLabels:!0,includeOverlays:!0,includeUnderlays:!0,includeOutlines:!0,useCache:!0},Es=jo(ba),ws=tt(ba);ir.boundingBox=function(t){var e;if(this.length===1&&this[0]._private.bbCache!=null&&!this[0]._private.styleDirty&&(t===void 0||t.useCache===void 0||t.useCache===!0))t===void 0?t=ba:t=ws(t),e=bs(this[0],t);else{e=gt(),t=t||ba;var r=ws(t),a=this,n=a.cy(),i=n.styleEnabled();if(i)for(var s=0;s<a.length;s++){var o=a[s],u=o._private,l=eu(o),f=u.bbCachePosKey===l,h=r.useCache&&f&&!u.styleDirty;o.recalculateRenderedStyle(h)}this.updateCompoundBounds(!t.useCache);for(var d=0;d<a.length;d++){var c=a[d];lr(e,bs(c,r))}}return e.x1=Et(e.x1),e.y1=Et(e.y1),e.x2=Et(e.x2),e.y2=Et(e.y2),e.w=Et(e.x2-e.x1),e.h=Et(e.y2-e.y1),e};ir.dirtyBoundingBoxCache=function(){for(var t=0;t<this.length;t++){var e=this[t]._private;e.bbCache=null,e.bbCachePosKey=null,e.bodyBounds=null,e.overlayBounds=null,e.labelBounds.all=null,e.labelBounds.source=null,e.labelBounds.target=null,e.labelBounds.main=null,e.labelBounds.sourceRot=null,e.labelBounds.targetRot=null,e.labelBounds.mainRot=null,e.arrowBounds.source=null,e.arrowBounds.target=null,e.arrowBounds["mid-source"]=null,e.arrowBounds["mid-target"]=null}return this.emitAndNotify("bounds"),this};ir.boundingBoxAt=function(t){var e=this.nodes(),r=this.cy(),a=r.hasCompoundNodes(),n=r.collection();if(a&&(n=e.filter(function(l){return l.isParent()}),e=e.not(n)),Ce(t)){var i=t;t=function(){return i}}var s=function(f,h){return f._private.bbAtOldPos=t(f,h)},o=function(f){return f._private.bbAtOldPos};r.startBatch(),e.forEach(s).silentPositions(t),a&&(n.dirtyCompoundBoundsCache(),n.dirtyBoundingBoxCache(),n.updateCompoundBounds(!0));var u=ah(this.boundingBox({useCache:!1}));return e.silentPositions(o),a&&(n.dirtyCompoundBoundsCache(),n.dirtyBoundingBoxCache(),n.updateCompoundBounds(!0)),r.endBatch(),u};Br.boundingbox=Br.bb=Br.boundingBox;Br.renderedBoundingbox=Br.renderedBoundingBox;var Vd=ir,oa,Sa;oa=Sa={};var tu=function(e){e.uppercaseName=_i(e.name),e.autoName="auto"+e.uppercaseName,e.labelName="label"+e.uppercaseName,e.outerName="outer"+e.uppercaseName,e.uppercaseOuterName=_i(e.outerName),oa[e.name]=function(){var a=this[0],n=a._private,i=n.cy,s=i._private.styleEnabled;if(a)if(s){if(a.isParent())return a.updateCompoundBounds(),n[e.autoName]||0;var o=a.pstyle(e.name);switch(o.strValue){case"label":return a.recalculateRenderedStyle(),n.rstyle[e.labelName]||0;default:return o.pfValue}}else return 1},oa["outer"+e.uppercaseName]=function(){var a=this[0],n=a._private,i=n.cy,s=i._private.styleEnabled;if(a)if(s){var o=a[e.name](),u=a.pstyle("border-width").pfValue,l=2*a.padding();return o+u+l}else return 1},oa["rendered"+e.uppercaseName]=function(){var a=this[0];if(a){var n=a[e.name]();return n*this.cy().zoom()}},oa["rendered"+e.uppercaseOuterName]=function(){var a=this[0];if(a){var n=a[e.outerName]();return n*this.cy().zoom()}}};tu({name:"width"});tu({name:"height"});Sa.padding=function(){var t=this[0],e=t._private;return t.isParent()?(t.updateCompoundBounds(),e.autoPadding!==void 0?e.autoPadding:t.pstyle("padding").pfValue):t.pstyle("padding").pfValue};Sa.paddedHeight=function(){var t=this[0];return t.height()+2*t.padding()};Sa.paddedWidth=function(){var t=this[0];return t.width()+2*t.padding()};var Ud=Sa,$d=function(e,r){if(e.isEdge())return r(e)},Yd=function(e,r){if(e.isEdge()){var a=e.cy();return yn(r(e),a.zoom(),a.pan())}},_d=function(e,r){if(e.isEdge()){var a=e.cy(),n=a.pan(),i=a.zoom();return r(e).map(function(s){return yn(s,i,n)})}},Hd=function(e){return e.renderer().getControlPoints(e)},Xd=function(e){return e.renderer().getSegmentPoints(e)},qd=function(e){return e.renderer().getSourceEndpoint(e)},Wd=function(e){return e.renderer().getTargetEndpoint(e)},Kd=function(e){return e.renderer().getEdgeMidpoint(e)},xs={controlPoints:{get:Hd,mult:!0},segmentPoints:{get:Xd,mult:!0},sourceEndpoint:{get:qd},targetEndpoint:{get:Wd},midpoint:{get:Kd}},Zd=function(e){return"rendered"+e[0].toUpperCase()+e.substr(1)},Qd=Object.keys(xs).reduce(function(t,e){var r=xs[e],a=Zd(e);return t[e]=function(){return $d(this,r.get)},r.mult?t[a]=function(){return _d(this,r.get)}:t[a]=function(){return Yd(this,r.get)},t},{}),Jd=be({},Fd,Vd,Ud,Qd);/*!
Event object based on jQuery events, MIT license

https://jquery.org/license/
https://tldrlegal.com/license/mit-license
https://github.com/jquery/jquery/blob/master/src/event.js
*/var ru=function(e,r){this.recycle(e,r)};function ra(){return!1}function za(){return!0}ru.prototype={instanceString:function(){return"event"},recycle:function(e,r){if(this.isImmediatePropagationStopped=this.isPropagationStopped=this.isDefaultPrevented=ra,e!=null&&e.preventDefault?(this.type=e.type,this.isDefaultPrevented=e.defaultPrevented?za:ra):e!=null&&e.type?r=e:this.type=e,r!=null&&(this.originalEvent=r.originalEvent,this.type=r.type!=null?r.type:this.type,this.cy=r.cy,this.target=r.target,this.position=r.position,this.renderedPosition=r.renderedPosition,this.namespace=r.namespace,this.layout=r.layout),this.cy!=null&&this.position!=null&&this.renderedPosition==null){var a=this.position,n=this.cy.zoom(),i=this.cy.pan();this.renderedPosition={x:a.x*n+i.x,y:a.y*n+i.y}}this.timeStamp=e&&e.timeStamp||Date.now()},preventDefault:function(){this.isDefaultPrevented=za;var e=this.originalEvent;e&&e.preventDefault&&e.preventDefault()},stopPropagation:function(){this.isPropagationStopped=za;var e=this.originalEvent;e&&e.stopPropagation&&e.stopPropagation()},stopImmediatePropagation:function(){this.isImmediatePropagationStopped=za,this.stopPropagation()},isDefaultPrevented:ra,isPropagationStopped:ra,isImmediatePropagationStopped:ra};var au=/^([^.]+)(\.(?:[^.]+))?$/,jd=".*",nu={qualifierCompare:function(e,r){return e===r},eventMatches:function(){return!0},addEventFields:function(){},callbackContext:function(e){return e},beforeEmit:function(){},afterEmit:function(){},bubble:function(){return!1},parent:function(){return null},context:null},Ts=Object.keys(nu),eg={};function Tn(){for(var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:eg,e=arguments.length>1?arguments[1]:void 0,r=0;r<Ts.length;r++){var a=Ts[r];this[a]=t[a]||nu[a]}this.context=e||this.context,this.listeners=[],this.emitting=0}var ar=Tn.prototype,iu=function(e,r,a,n,i,s,o){Ge(n)&&(i=n,n=null),o&&(s==null?s=o:s=be({},s,o));for(var u=Re(a)?a:a.split(/\s+/),l=0;l<u.length;l++){var f=u[l];if(!jt(f)){var h=f.match(au);if(h){var d=h[1],c=h[2]?h[2]:null,v=r(e,f,d,c,n,i,s);if(v===!1)break}}}},Cs=function(e,r){return e.addEventFields(e.context,r),new ru(r.type,r)},tg=function(e,r,a){if(El(a)){r(e,a);return}else if(Ce(a)){r(e,Cs(e,a));return}for(var n=Re(a)?a:a.split(/\s+/),i=0;i<n.length;i++){var s=n[i];if(!jt(s)){var o=s.match(au);if(o){var u=o[1],l=o[2]?o[2]:null,f=Cs(e,{type:u,namespace:l,target:e.context});r(e,f)}}}};ar.on=ar.addListener=function(t,e,r,a,n){return iu(this,function(i,s,o,u,l,f,h){Ge(f)&&i.listeners.push({event:s,callback:f,type:o,namespace:u,qualifier:l,conf:h})},t,e,r,a,n),this};ar.one=function(t,e,r,a){return this.on(t,e,r,a,{one:!0})};ar.removeListener=ar.off=function(t,e,r,a){var n=this;this.emitting!==0&&(this.listeners=Nf(this.listeners));for(var i=this.listeners,s=function(l){var f=i[l];iu(n,function(h,d,c,v,p,g){if((f.type===c||t==="*")&&(!v&&f.namespace!==".*"||f.namespace===v)&&(!p||h.qualifierCompare(f.qualifier,p))&&(!g||f.callback===g))return i.splice(l,1),!1},t,e,r,a)},o=i.length-1;o>=0;o--)s(o);return this};ar.removeAllListeners=function(){return this.removeListener("*")};ar.emit=ar.trigger=function(t,e,r){var a=this.listeners,n=a.length;return this.emitting++,Re(e)||(e=[e]),tg(this,function(i,s){r!=null&&(a=[{event:s.event,type:s.type,namespace:s.namespace,callback:r}],n=a.length);for(var o=function(f){var h=a[f];if(h.type===s.type&&(!h.namespace||h.namespace===s.namespace||h.namespace===jd)&&i.eventMatches(i.context,h,s)){var d=[s];e!=null&&Mf(d,e),i.beforeEmit(i.context,h,s),h.conf&&h.conf.one&&(i.listeners=i.listeners.filter(function(p){return p!==h}));var c=i.callbackContext(i.context,h,s),v=h.callback.apply(c,d);i.afterEmit(i.context,h,s),v===!1&&(s.stopPropagation(),s.preventDefault())}},u=0;u<n;u++)o(u);i.bubble(i.context)&&!s.isPropagationStopped()&&i.parent(i.context).emit(s,e)},t),this.emitting--,this};var rg={qualifierCompare:function(e,r){return e==null||r==null?e==null&&r==null:e.sameText(r)},eventMatches:function(e,r,a){var n=r.qualifier;return n!=null?e!==a.target&&Ta(a.target)&&n.matches(a.target):!0},addEventFields:function(e,r){r.cy=e.cy(),r.target=e},callbackContext:function(e,r,a){return r.qualifier!=null?a.target:e},beforeEmit:function(e,r){r.conf&&r.conf.once&&r.conf.onceCollection.removeListener(r.event,r.qualifier,r.callback)},bubble:function(){return!0},parent:function(e){return e.isChild()?e.parent():e.cy()}},Va=function(e){return de(e)?new tr(e):e},su={createEmitter:function(){for(var e=0;e<this.length;e++){var r=this[e],a=r._private;a.emitter||(a.emitter=new Tn(rg,r))}return this},emitter:function(){return this._private.emitter},on:function(e,r,a){for(var n=Va(r),i=0;i<this.length;i++){var s=this[i];s.emitter().on(e,n,a)}return this},removeListener:function(e,r,a){for(var n=Va(r),i=0;i<this.length;i++){var s=this[i];s.emitter().removeListener(e,n,a)}return this},removeAllListeners:function(){for(var e=0;e<this.length;e++){var r=this[e];r.emitter().removeAllListeners()}return this},one:function(e,r,a){for(var n=Va(r),i=0;i<this.length;i++){var s=this[i];s.emitter().one(e,n,a)}return this},once:function(e,r,a){for(var n=Va(r),i=0;i<this.length;i++){var s=this[i];s.emitter().on(e,n,a,{once:!0,onceCollection:this})}},emit:function(e,r){for(var a=0;a<this.length;a++){var n=this[a];n.emitter().emit(e,r)}return this},emitAndNotify:function(e,r){if(this.length!==0)return this.cy().notify(e,this),this.emit(e,r),this}};Oe.eventAliasesOn(su);var ou={nodes:function(e){return this.filter(function(r){return r.isNode()}).filter(e)},edges:function(e){return this.filter(function(r){return r.isEdge()}).filter(e)},byGroup:function(){for(var e=this.spawn(),r=this.spawn(),a=0;a<this.length;a++){var n=this[a];n.isNode()?e.push(n):r.push(n)}return{nodes:e,edges:r}},filter:function(e,r){if(e===void 0)return this;if(de(e)||pt(e))return new tr(e).filter(this);if(Ge(e)){for(var a=this.spawn(),n=this,i=0;i<n.length;i++){var s=n[i],o=r?e.apply(r,[s,i,n]):e(s,i,n);o&&a.push(s)}return a}return this.spawn()},not:function(e){if(e){de(e)&&(e=this.filter(e));for(var r=this.spawn(),a=0;a<this.length;a++){var n=this[a],i=e.has(n);i||r.push(n)}return r}else return this},absoluteComplement:function(){var e=this.cy();return e.mutableElements().not(this)},intersect:function(e){if(de(e)){var r=e;return this.filter(r)}for(var a=this.spawn(),n=this,i=e,s=this.length<e.length,o=s?n:i,u=s?i:n,l=0;l<o.length;l++){var f=o[l];u.has(f)&&a.push(f)}return a},xor:function(e){var r=this._private.cy;de(e)&&(e=r.$(e));var a=this.spawn(),n=this,i=e,s=function(u,l){for(var f=0;f<u.length;f++){var h=u[f],d=h._private.data.id,c=l.hasElementWithId(d);c||a.push(h)}};return s(n,i),s(i,n),a},diff:function(e){var r=this._private.cy;de(e)&&(e=r.$(e));var a=this.spawn(),n=this.spawn(),i=this.spawn(),s=this,o=e,u=function(f,h,d){for(var c=0;c<f.length;c++){var v=f[c],p=v._private.data.id,g=h.hasElementWithId(p);g?i.merge(v):d.push(v)}};return u(s,o,a),u(o,s,n),{left:a,right:n,both:i}},add:function(e){var r=this._private.cy;if(!e)return this;if(de(e)){var a=e;e=r.mutableElements().filter(a)}for(var n=this.spawnSelf(),i=0;i<e.length;i++){var s=e[i],o=!this.has(s);o&&n.push(s)}return n},merge:function(e){var r=this._private,a=r.cy;if(!e)return this;if(e&&de(e)){var n=e;e=a.mutableElements().filter(n)}for(var i=r.map,s=0;s<e.length;s++){var o=e[s],u=o._private.data.id,l=!i.has(u);if(l){var f=this.length++;this[f]=o,i.set(u,{ele:o,index:f})}}return this},unmergeAt:function(e){var r=this[e],a=r.id(),n=this._private,i=n.map;this[e]=void 0,i.delete(a);var s=e===this.length-1;if(this.length>1&&!s){var o=this.length-1,u=this[o],l=u._private.data.id;this[o]=void 0,this[e]=u,i.set(l,{ele:u,index:e})}return this.length--,this},unmergeOne:function(e){e=e[0];var r=this._private,a=e._private.data.id,n=r.map,i=n.get(a);if(!i)return this;var s=i.index;return this.unmergeAt(s),this},unmerge:function(e){var r=this._private.cy;if(!e)return this;if(e&&de(e)){var a=e;e=r.mutableElements().filter(a)}for(var n=0;n<e.length;n++)this.unmergeOne(e[n]);return this},unmergeBy:function(e){for(var r=this.length-1;r>=0;r--){var a=this[r];e(a)&&this.unmergeAt(r)}return this},map:function(e,r){for(var a=[],n=this,i=0;i<n.length;i++){var s=n[i],o=r?e.apply(r,[s,i,n]):e(s,i,n);a.push(o)}return a},reduce:function(e,r){for(var a=r,n=this,i=0;i<n.length;i++)a=e(a,n[i],i,n);return a},max:function(e,r){for(var a=-1/0,n,i=this,s=0;s<i.length;s++){var o=i[s],u=r?e.apply(r,[o,s,i]):e(o,s,i);u>a&&(a=u,n=o)}return{value:a,ele:n}},min:function(e,r){for(var a=1/0,n,i=this,s=0;s<i.length;s++){var o=i[s],u=r?e.apply(r,[o,s,i]):e(o,s,i);u<a&&(a=u,n=o)}return{value:a,ele:n}}},Le=ou;Le.u=Le["|"]=Le["+"]=Le.union=Le.or=Le.add;Le["\\"]=Le["!"]=Le["-"]=Le.difference=Le.relativeComplement=Le.subtract=Le.not;Le.n=Le["&"]=Le["."]=Le.and=Le.intersection=Le.intersect;Le["^"]=Le["(+)"]=Le["(-)"]=Le.symmetricDifference=Le.symdiff=Le.xor;Le.fnFilter=Le.filterFn=Le.stdFilter=Le.filter;Le.complement=Le.abscomp=Le.absoluteComplement;var ag={isNode:function(){return this.group()==="nodes"},isEdge:function(){return this.group()==="edges"},isLoop:function(){return this.isEdge()&&this.source()[0]===this.target()[0]},isSimple:function(){return this.isEdge()&&this.source()[0]!==this.target()[0]},group:function(){var e=this[0];if(e)return e._private.group}},uu=function(e,r){var a=e.cy(),n=a.hasCompoundNodes();function i(f){var h=f.pstyle("z-compound-depth");return h.value==="auto"?n?f.zDepth():0:h.value==="bottom"?-1:h.value==="top"?yi:0}var s=i(e)-i(r);if(s!==0)return s;function o(f){var h=f.pstyle("z-index-compare");return h.value==="auto"&&f.isNode()?1:0}var u=o(e)-o(r);if(u!==0)return u;var l=e.pstyle("z-index").value-r.pstyle("z-index").value;return l!==0?l:e.poolIndex()-r.poolIndex()},un={forEach:function(e,r){if(Ge(e))for(var a=this.length,n=0;n<a;n++){var i=this[n],s=r?e.apply(r,[i,n,this]):e(i,n,this);if(s===!1)break}return this},toArray:function(){for(var e=[],r=0;r<this.length;r++)e.push(this[r]);return e},slice:function(e,r){var a=[],n=this.length;r==null&&(r=n),e==null&&(e=0),e<0&&(e=n+e),r<0&&(r=n+r);for(var i=e;i>=0&&i<r&&i<n;i++)a.push(this[i]);return this.spawn(a)},size:function(){return this.length},eq:function(e){return this[e]||this.spawn()},first:function(){return this[0]||this.spawn()},last:function(){return this[this.length-1]||this.spawn()},empty:function(){return this.length===0},nonempty:function(){return!this.empty()},sort:function(e){if(!Ge(e))return this;var r=this.toArray().sort(e);return this.spawn(r)},sortByZIndex:function(){return this.sort(uu)},zDepth:function(){var e=this[0];if(e){var r=e._private,a=r.group;if(a==="nodes"){var n=r.data.parent?e.parents().size():0;return e.isParent()?n:yi-1}else{var i=r.source,s=r.target,o=i.zDepth(),u=s.zDepth();return Math.max(o,u,0)}}}};un.each=un.forEach;var ng=function(){var e="undefined",r=(typeof Symbol>"u"?"undefined":Xe(Symbol))!=e&&Xe(Symbol.iterator)!=e;r&&(un[Symbol.iterator]=function(){var a=this,n={value:void 0,done:!1},i=0,s=this.length;return ao({next:function(){return i<s?n.value=a[i++]:(n.value=void 0,n.done=!0),n}},Symbol.iterator,function(){return this})})};ng();var ig=tt({nodeDimensionsIncludeLabels:!1}),qa={layoutDimensions:function(e){e=ig(e);var r;if(!this.takesUpSpace())r={w:0,h:0};else if(e.nodeDimensionsIncludeLabels){var a=this.boundingBox();r={w:a.w,h:a.h}}else r={w:this.outerWidth(),h:this.outerHeight()};return(r.w===0||r.h===0)&&(r.w=r.h=1),r},layoutPositions:function(e,r,a){var n=this.nodes().filter(function(C){return!C.isParent()}),i=this.cy(),s=r.eles,o=function(S){return S.id()},u=ha(a,o);e.emit({type:"layoutstart",layout:e}),e.animations=[];var l=function(S,E,w){var x={x:E.x1+E.w/2,y:E.y1+E.h/2},D={x:(w.x-x.x)*S,y:(w.y-x.y)*S};return{x:x.x+D.x,y:x.y+D.y}},f=r.spacingFactor&&r.spacingFactor!==1,h=function(){if(!f)return null;for(var S=gt(),E=0;E<n.length;E++){var w=n[E],x=u(w,E);sh(S,x.x,x.y)}return S},d=h(),c=ha(function(C,S){var E=u(C,S);if(f){var w=Math.abs(r.spacingFactor);E=l(w,d,E)}return r.transform!=null&&(E=r.transform(C,E)),E},o);if(r.animate){for(var v=0;v<n.length;v++){var p=n[v],g=c(p,v),y=r.animateFilter==null||r.animateFilter(p,v);if(y){var b=p.animation({position:g,duration:r.animationDuration,easing:r.animationEasing});e.animations.push(b)}else p.position(g)}if(r.fit){var m=i.animation({fit:{boundingBox:s.boundingBoxAt(c),padding:r.padding},duration:r.animationDuration,easing:r.animationEasing});e.animations.push(m)}else if(r.zoom!==void 0&&r.pan!==void 0){var T=i.animation({zoom:r.zoom,pan:r.pan,duration:r.animationDuration,easing:r.animationEasing});e.animations.push(T)}e.animations.forEach(function(C){return C.play()}),e.one("layoutready",r.ready),e.emit({type:"layoutready",layout:e}),$r.all(e.animations.map(function(C){return C.promise()})).then(function(){e.one("layoutstop",r.stop),e.emit({type:"layoutstop",layout:e})})}else n.positions(c),r.fit&&i.fit(r.eles,r.padding),r.zoom!=null&&i.zoom(r.zoom),r.pan&&i.pan(r.pan),e.one("layoutready",r.ready),e.emit({type:"layoutready",layout:e}),e.one("layoutstop",r.stop),e.emit({type:"layoutstop",layout:e});return this},layout:function(e){var r=this.cy();return r.makeLayout(be({},e,{eles:this}))}};qa.createLayout=qa.makeLayout=qa.layout;function lu(t,e,r){var a=r._private,n=a.styleCache=a.styleCache||[],i;return(i=n[t])!=null||(i=n[t]=e(r)),i}function Cn(t,e){return t=dr(t),function(a){return lu(t,e,a)}}function Dn(t,e){t=dr(t);var r=function(n){return e.call(n)};return function(){var n=this[0];if(n)return lu(t,r,n)}}var je={recalculateRenderedStyle:function(e){var r=this.cy(),a=r.renderer(),n=r.styleEnabled();return a&&n&&a.recalculateRenderedStyle(this,e),this},dirtyStyleCache:function(){var e=this.cy(),r=function(i){return i._private.styleCache=null};if(e.hasCompoundNodes()){var a;a=this.spawnSelf().merge(this.descendants()).merge(this.parents()),a.merge(a.connectedEdges()),a.forEach(r)}else this.forEach(function(n){r(n),n.connectedEdges().forEach(r)});return this},updateStyle:function(e){var r=this._private.cy;if(!r.styleEnabled())return this;if(r.batching()){var a=r._private.batchStyleEles;return a.merge(this),this}var n=r.hasCompoundNodes(),i=this;e=!!(e||e===void 0),n&&(i=this.spawnSelf().merge(this.descendants()).merge(this.parents()));var s=i;return e?s.emitAndNotify("style"):s.emit("style"),i.forEach(function(o){return o._private.styleDirty=!0}),this},cleanStyle:function(){var e=this.cy();if(e.styleEnabled())for(var r=0;r<this.length;r++){var a=this[r];a._private.styleDirty&&(a._private.styleDirty=!1,e.style().apply(a))}},parsedStyle:function(e){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!0,a=this[0],n=a.cy();if(n.styleEnabled()&&a){this.cleanStyle();var i=a._private.style[e];return i??(r?n.style().getDefaultProperty(e):null)}},numericStyle:function(e){var r=this[0];if(r.cy().styleEnabled()&&r){var a=r.pstyle(e);return a.pfValue!==void 0?a.pfValue:a.value}},numericStyleUnits:function(e){var r=this[0];if(r.cy().styleEnabled()&&r)return r.pstyle(e).units},renderedStyle:function(e){var r=this.cy();if(!r.styleEnabled())return this;var a=this[0];if(a)return r.style().getRenderedStyle(a,e)},style:function(e,r){var a=this.cy();if(!a.styleEnabled())return this;var n=!1,i=a.style();if(Ce(e)){var s=e;i.applyBypass(this,s,n),this.emitAndNotify("style")}else if(de(e))if(r===void 0){var o=this[0];return o?i.getStylePropertyValue(o,e):void 0}else i.applyBypass(this,e,r,n),this.emitAndNotify("style");else if(e===void 0){var u=this[0];return u?i.getRawStyle(u):void 0}return this},removeStyle:function(e){var r=this.cy();if(!r.styleEnabled())return this;var a=!1,n=r.style(),i=this;if(e===void 0)for(var s=0;s<i.length;s++){var o=i[s];n.removeAllBypasses(o,a)}else{e=e.split(/\s+/);for(var u=0;u<i.length;u++){var l=i[u];n.removeBypasses(l,e,a)}}return this.emitAndNotify("style"),this},show:function(){return this.css("display","element"),this},hide:function(){return this.css("display","none"),this},effectiveOpacity:function(){var e=this.cy();if(!e.styleEnabled())return 1;var r=e.hasCompoundNodes(),a=this[0];if(a){var n=a._private,i=a.pstyle("opacity").value;if(!r)return i;var s=n.data.parent?a.parents():null;if(s)for(var o=0;o<s.length;o++){var u=s[o],l=u.pstyle("opacity").value;i=l*i}return i}},transparent:function(){var e=this.cy();if(!e.styleEnabled())return!1;var r=this[0],a=r.cy().hasCompoundNodes();if(r)return a?r.effectiveOpacity()===0:r.pstyle("opacity").value===0},backgrounding:function(){var e=this.cy();if(!e.styleEnabled())return!1;var r=this[0];return!!r._private.backgrounding}};function Un(t,e){var r=t._private,a=r.data.parent?t.parents():null;if(a)for(var n=0;n<a.length;n++){var i=a[n];if(!e(i))return!1}return!0}function Oi(t){var e=t.ok,r=t.edgeOkViaNode||t.ok,a=t.parentOk||t.ok;return function(){var n=this.cy();if(!n.styleEnabled())return!0;var i=this[0],s=n.hasCompoundNodes();if(i){var o=i._private;if(!e(i))return!1;if(i.isNode())return!s||Un(i,a);var u=o.source,l=o.target;return r(u)&&(!s||Un(u,r))&&(u===l||r(l)&&(!s||Un(l,r)))}}}var Xr=Cn("eleTakesUpSpace",function(t){return t.pstyle("display").value==="element"&&t.width()!==0&&(t.isNode()?t.height()!==0:!0)});je.takesUpSpace=Dn("takesUpSpace",Oi({ok:Xr}));var sg=Cn("eleInteractive",function(t){return t.pstyle("events").value==="yes"&&t.pstyle("visibility").value==="visible"&&Xr(t)}),og=Cn("parentInteractive",function(t){return t.pstyle("visibility").value==="visible"&&Xr(t)});je.interactive=Dn("interactive",Oi({ok:sg,parentOk:og,edgeOkViaNode:Xr}));je.noninteractive=function(){var t=this[0];if(t)return!t.interactive()};var ug=Cn("eleVisible",function(t){return t.pstyle("visibility").value==="visible"&&t.pstyle("opacity").pfValue!==0&&Xr(t)}),lg=Xr;je.visible=Dn("visible",Oi({ok:ug,edgeOkViaNode:lg}));je.hidden=function(){var t=this[0];if(t)return!t.visible()};je.isBundledBezier=Dn("isBundledBezier",function(){return this.cy().styleEnabled()?!this.removed()&&this.pstyle("curve-style").value==="bezier"&&this.takesUpSpace():!1});je.bypass=je.css=je.style;je.renderedCss=je.renderedStyle;je.removeBypass=je.removeCss=je.removeStyle;je.pstyle=je.parsedStyle;var Jt={};function Ds(t){return function(){var e=arguments,r=[];if(e.length===2){var a=e[0],n=e[1];this.on(t.event,a,n)}else if(e.length===1&&Ge(e[0])){var i=e[0];this.on(t.event,i)}else if(e.length===0||e.length===1&&Re(e[0])){for(var s=e.length===1?e[0]:null,o=0;o<this.length;o++){var u=this[o],l=!t.ableField||u._private[t.ableField],f=u._private[t.field]!=t.value;if(t.overrideAble){var h=t.overrideAble(u);if(h!==void 0&&(l=h,!h))return this}l&&(u._private[t.field]=t.value,f&&r.push(u))}var d=this.spawn(r);d.updateStyle(),d.emit(t.event),s&&d.emit(s)}return this}}function qr(t){Jt[t.field]=function(){var e=this[0];if(e){if(t.overrideField){var r=t.overrideField(e);if(r!==void 0)return r}return e._private[t.field]}},Jt[t.on]=Ds({event:t.on,field:t.field,ableField:t.ableField,overrideAble:t.overrideAble,value:!0}),Jt[t.off]=Ds({event:t.off,field:t.field,ableField:t.ableField,overrideAble:t.overrideAble,value:!1})}qr({field:"locked",overrideField:function(e){return e.cy().autolock()?!0:void 0},on:"lock",off:"unlock"});qr({field:"grabbable",overrideField:function(e){return e.cy().autoungrabify()||e.pannable()?!1:void 0},on:"grabify",off:"ungrabify"});qr({field:"selected",ableField:"selectable",overrideAble:function(e){return e.cy().autounselectify()?!1:void 0},on:"select",off:"unselect"});qr({field:"selectable",overrideField:function(e){return e.cy().autounselectify()?!1:void 0},on:"selectify",off:"unselectify"});Jt.deselect=Jt.unselect;Jt.grabbed=function(){var t=this[0];if(t)return t._private.grabbed};qr({field:"active",on:"activate",off:"unactivate"});qr({field:"pannable",on:"panify",off:"unpanify"});Jt.inactive=function(){var t=this[0];if(t)return!t._private.active};var it={},Ss=function(e){return function(a){for(var n=this,i=[],s=0;s<n.length;s++){var o=n[s];if(o.isNode()){for(var u=!1,l=o.connectedEdges(),f=0;f<l.length;f++){var h=l[f],d=h.source(),c=h.target();if(e.noIncomingEdges&&c===o&&d!==o||e.noOutgoingEdges&&d===o&&c!==o){u=!0;break}}u||i.push(o)}}return this.spawn(i,!0).filter(a)}},Ls=function(e){return function(r){for(var a=this,n=[],i=0;i<a.length;i++){var s=a[i];if(s.isNode())for(var o=s.connectedEdges(),u=0;u<o.length;u++){var l=o[u],f=l.source(),h=l.target();e.outgoing&&f===s?(n.push(l),n.push(h)):e.incoming&&h===s&&(n.push(l),n.push(f))}}return this.spawn(n,!0).filter(r)}},As=function(e){return function(r){for(var a=this,n=[],i={};;){var s=e.outgoing?a.outgoers():a.incomers();if(s.length===0)break;for(var o=!1,u=0;u<s.length;u++){var l=s[u],f=l.id();i[f]||(i[f]=!0,n.push(l),o=!0)}if(!o)break;a=s}return this.spawn(n,!0).filter(r)}};it.clearTraversalCache=function(){for(var t=0;t<this.length;t++)this[t]._private.traversalCache=null};be(it,{roots:Ss({noIncomingEdges:!0}),leaves:Ss({noOutgoingEdges:!0}),outgoers:wt(Ls({outgoing:!0}),"outgoers"),successors:As({outgoing:!0}),incomers:wt(Ls({incoming:!0}),"incomers"),predecessors:As({incoming:!0})});be(it,{neighborhood:wt(function(t){for(var e=[],r=this.nodes(),a=0;a<r.length;a++)for(var n=r[a],i=n.connectedEdges(),s=0;s<i.length;s++){var o=i[s],u=o.source(),l=o.target(),f=n===u?l:u;f.length>0&&e.push(f[0]),e.push(o[0])}return this.spawn(e,!0).filter(t)},"neighborhood"),closedNeighborhood:function(e){return this.neighborhood().add(this).filter(e)},openNeighborhood:function(e){return this.neighborhood(e)}});it.neighbourhood=it.neighborhood;it.closedNeighbourhood=it.closedNeighborhood;it.openNeighbourhood=it.openNeighborhood;be(it,{source:wt(function(e){var r=this[0],a;return r&&(a=r._private.source||r.cy().collection()),a&&e?a.filter(e):a},"source"),target:wt(function(e){var r=this[0],a;return r&&(a=r._private.target||r.cy().collection()),a&&e?a.filter(e):a},"target"),sources:Os({attr:"source"}),targets:Os({attr:"target"})});function Os(t){return function(r){for(var a=[],n=0;n<this.length;n++){var i=this[n],s=i._private[t.attr];s&&a.push(s)}return this.spawn(a,!0).filter(r)}}be(it,{edgesWith:wt(Ns(),"edgesWith"),edgesTo:wt(Ns({thisIsSrc:!0}),"edgesTo")});function Ns(t){return function(r){var a=[],n=this._private.cy,i=t||{};de(r)&&(r=n.$(r));for(var s=0;s<r.length;s++)for(var o=r[s]._private.edges,u=0;u<o.length;u++){var l=o[u],f=l._private.data,h=this.hasElementWithId(f.source)&&r.hasElementWithId(f.target),d=r.hasElementWithId(f.source)&&this.hasElementWithId(f.target),c=h||d;c&&((i.thisIsSrc||i.thisIsTgt)&&(i.thisIsSrc&&!h||i.thisIsTgt&&!d)||a.push(l))}return this.spawn(a,!0)}}be(it,{connectedEdges:wt(function(t){for(var e=[],r=this,a=0;a<r.length;a++){var n=r[a];if(n.isNode())for(var i=n._private.edges,s=0;s<i.length;s++){var o=i[s];e.push(o)}}return this.spawn(e,!0).filter(t)},"connectedEdges"),connectedNodes:wt(function(t){for(var e=[],r=this,a=0;a<r.length;a++){var n=r[a];n.isEdge()&&(e.push(n.source()[0]),e.push(n.target()[0]))}return this.spawn(e,!0).filter(t)},"connectedNodes"),parallelEdges:wt(Is(),"parallelEdges"),codirectedEdges:wt(Is({codirected:!0}),"codirectedEdges")});function Is(t){var e={codirected:!1};return t=be({},e,t),function(a){for(var n=[],i=this.edges(),s=t,o=0;o<i.length;o++)for(var u=i[o],l=u._private,f=l.source,h=f._private.data.id,d=l.data.target,c=f._private.edges,v=0;v<c.length;v++){var p=c[v],g=p._private.data,y=g.target,b=g.source,m=y===d&&b===h,T=h===y&&d===b;(s.codirected&&m||!s.codirected&&(m||T))&&n.push(p)}return this.spawn(n,!0).filter(a)}}be(it,{components:function(e){var r=this,a=r.cy(),n=a.collection(),i=e==null?r.nodes():e.nodes(),s=[];e!=null&&i.empty()&&(i=e.sources());var o=function(f,h){n.merge(f),i.unmerge(f),h.merge(f)};if(i.empty())return r.spawn();var u=function(){var f=a.collection();s.push(f);var h=i[0];o(h,f),r.bfs({directed:!1,roots:h,visit:function(c){return o(c,f)}}),f.forEach(function(d){d.connectedEdges().forEach(function(c){r.has(c)&&f.has(c.source())&&f.has(c.target())&&f.merge(c)})})};do u();while(i.length>0);return s},component:function(){var e=this[0];return e.cy().mutableElements().components(e)[0]}});it.componentsOf=it.components;var et=function(e,r){var a=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!1,n=arguments.length>3&&arguments[3]!==void 0?arguments[3]:!1;if(e===void 0){ze("A collection must have a reference to the core");return}var i=new Bt,s=!1;if(!r)r=[];else if(r.length>0&&Ce(r[0])&&!Ta(r[0])){s=!0;for(var o=[],u=new Ur,l=0,f=r.length;l<f;l++){var h=r[l];h.data==null&&(h.data={});var d=h.data;if(d.id==null)d.id=xo();else if(e.hasElementWithId(d.id)||u.has(d.id))continue;var c=new pn(e,h,!1);o.push(c),u.add(d.id)}r=o}this.length=0;for(var v=0,p=r.length;v<p;v++){var g=r[v][0];if(g!=null){var y=g._private.data.id;(!a||!i.has(y))&&(a&&i.set(y,{index:this.length,ele:g}),this[this.length]=g,this.length++)}}this._private={eles:this,cy:e,get map(){return this.lazyMap==null&&this.rebuildMap(),this.lazyMap},set map(b){this.lazyMap=b},rebuildMap:function(){for(var m=this.lazyMap=new Bt,T=this.eles,C=0;C<T.length;C++){var S=T[C];m.set(S.id(),{index:C,ele:S})}}},a&&(this._private.map=i),s&&!n&&this.restore()},Pe=pn.prototype=et.prototype=Object.create(Array.prototype);Pe.instanceString=function(){return"collection"};Pe.spawn=function(t,e){return new et(this.cy(),t,e)};Pe.spawnSelf=function(){return this.spawn(this)};Pe.cy=function(){return this._private.cy};Pe.renderer=function(){return this._private.cy.renderer()};Pe.element=function(){return this[0]};Pe.collection=function(){return oo(this)?this:new et(this._private.cy,[this])};Pe.unique=function(){return new et(this._private.cy,this,!0)};Pe.hasElementWithId=function(t){return t=""+t,this._private.map.has(t)};Pe.getElementById=function(t){t=""+t;var e=this._private.cy,r=this._private.map.get(t);return r?r.ele:new et(e)};Pe.$id=Pe.getElementById;Pe.poolIndex=function(){var t=this._private.cy,e=t._private.elements,r=this[0]._private.data.id;return e._private.map.get(r).index};Pe.indexOf=function(t){var e=t[0]._private.data.id;return this._private.map.get(e).index};Pe.indexOfId=function(t){return t=""+t,this._private.map.get(t).index};Pe.json=function(t){var e=this.element(),r=this.cy();if(e==null&&t)return this;if(e!=null){var a=e._private;if(Ce(t)){if(r.startBatch(),t.data){e.data(t.data);var n=a.data;if(e.isEdge()){var i=!1,s={},o=t.data.source,u=t.data.target;o!=null&&o!=n.source&&(s.source=""+o,i=!0),u!=null&&u!=n.target&&(s.target=""+u,i=!0),i&&(e=e.move(s))}else{var l="parent"in t.data,f=t.data.parent;l&&(f!=null||n.parent!=null)&&f!=n.parent&&(f===void 0&&(f=null),f!=null&&(f=""+f),e=e.move({parent:f}))}}t.position&&e.position(t.position);var h=function(p,g,y){var b=t[p];b!=null&&b!==a[p]&&(b?e[g]():e[y]())};return h("removed","remove","restore"),h("selected","select","unselect"),h("selectable","selectify","unselectify"),h("locked","lock","unlock"),h("grabbable","grabify","ungrabify"),h("pannable","panify","unpanify"),t.classes!=null&&e.classes(t.classes),r.endBatch(),this}else if(t===void 0){var d={data:Pt(a.data),position:Pt(a.position),group:a.group,removed:a.removed,selected:a.selected,selectable:a.selectable,locked:a.locked,grabbable:a.grabbable,pannable:a.pannable,classes:null};d.classes="";var c=0;return a.classes.forEach(function(v){return d.classes+=c++===0?v:" "+v}),d}}};Pe.jsons=function(){for(var t=[],e=0;e<this.length;e++){var r=this[e],a=r.json();t.push(a)}return t};Pe.clone=function(){for(var t=this.cy(),e=[],r=0;r<this.length;r++){var a=this[r],n=a.json(),i=new pn(t,n,!1);e.push(i)}return new et(t,e)};Pe.copy=Pe.clone;Pe.restore=function(){for(var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!0,e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!0,r=this,a=r.cy(),n=a._private,i=[],s=[],o,u=0,l=r.length;u<l;u++){var f=r[u];e&&!f.removed()||(f.isNode()?i.push(f):s.push(f))}o=i.concat(s);var h,d=function(){o.splice(h,1),h--};for(h=0;h<o.length;h++){var c=o[h],v=c._private,p=v.data;if(c.clearTraversalCache(),!(!e&&!v.removed)){if(p.id===void 0)p.id=xo();else if(ie(p.id))p.id=""+p.id;else if(jt(p.id)||!de(p.id)){ze("Can not create element with invalid string ID `"+p.id+"`"),d();continue}else if(a.hasElementWithId(p.id)){ze("Can not create second element with ID `"+p.id+"`"),d();continue}}var g=p.id;if(c.isNode()){var y=v.position;y.x==null&&(y.x=0),y.y==null&&(y.y=0)}if(c.isEdge()){for(var b=c,m=["source","target"],T=m.length,C=!1,S=0;S<T;S++){var E=m[S],w=p[E];ie(w)&&(w=p[E]=""+p[E]),w==null||w===""?(ze("Can not create edge `"+g+"` with unspecified "+E),C=!0):a.hasElementWithId(w)||(ze("Can not create edge `"+g+"` with nonexistant "+E+" `"+w+"`"),C=!0)}if(C){d();continue}var x=a.getElementById(p.source),D=a.getElementById(p.target);x.same(D)?x._private.edges.push(b):(x._private.edges.push(b),D._private.edges.push(b)),b._private.source=x,b._private.target=D}v.map=new Bt,v.map.set(g,{ele:c,index:0}),v.removed=!1,e&&a.addToPool(c)}for(var L=0;L<i.length;L++){var A=i[L],N=A._private.data;ie(N.parent)&&(N.parent=""+N.parent);var O=N.parent,M=O!=null;if(M||A._private.parent){var R=A._private.parent?a.collection().merge(A._private.parent):a.getElementById(O);if(R.empty())N.parent=void 0;else if(R[0].removed())Ne("Node added with missing parent, reference to parent removed"),N.parent=void 0,A._private.parent=null;else{for(var k=!1,P=R;!P.empty();){if(A.same(P)){k=!0,N.parent=void 0;break}P=P.parent()}k||(R[0]._private.children.push(A),A._private.parent=R[0],n.hasCompoundNodes=!0)}}}if(o.length>0){for(var B=o.length===r.length?r:new et(a,o),z=0;z<B.length;z++){var G=B[z];G.isNode()||(G.parallelEdges().clearTraversalCache(),G.source().clearTraversalCache(),G.target().clearTraversalCache())}var F;n.hasCompoundNodes?F=a.collection().merge(B).merge(B.connectedNodes()).merge(B.parent()):F=B,F.dirtyCompoundBoundsCache().dirtyBoundingBoxCache().updateStyle(t),t?B.emitAndNotify("add"):e&&B.emit("add")}return r};Pe.removed=function(){var t=this[0];return t&&t._private.removed};Pe.inside=function(){var t=this[0];return t&&!t._private.removed};Pe.remove=function(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!0,e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!0,r=this,a=[],n={},i=r._private.cy;function s(O){for(var M=O._private.edges,R=0;R<M.length;R++)u(M[R])}function o(O){for(var M=O._private.children,R=0;R<M.length;R++)u(M[R])}function u(O){var M=n[O.id()];e&&O.removed()||M||(n[O.id()]=!0,O.isNode()?(a.push(O),s(O),o(O)):a.unshift(O))}for(var l=0,f=r.length;l<f;l++){var h=r[l];u(h)}function d(O,M){var R=O._private.edges;er(R,M),O.clearTraversalCache()}function c(O){O.clearTraversalCache()}var v=[];v.ids={};function p(O,M){M=M[0],O=O[0];var R=O._private.children,k=O.id();er(R,M),M._private.parent=null,v.ids[k]||(v.ids[k]=!0,v.push(O))}r.dirtyCompoundBoundsCache(),e&&i.removeFromPool(a);for(var g=0;g<a.length;g++){var y=a[g];if(y.isEdge()){var b=y.source()[0],m=y.target()[0];d(b,y),d(m,y);for(var T=y.parallelEdges(),C=0;C<T.length;C++){var S=T[C];c(S),S.isBundledBezier()&&S.dirtyBoundingBoxCache()}}else{var E=y.parent();E.length!==0&&p(E,y)}e&&(y._private.removed=!0)}var w=i._private.elements;i._private.hasCompoundNodes=!1;for(var x=0;x<w.length;x++){var D=w[x];if(D.isParent()){i._private.hasCompoundNodes=!0;break}}var L=new et(this.cy(),a);L.size()>0&&(t?L.emitAndNotify("remove"):e&&L.emit("remove"));for(var A=0;A<v.length;A++){var N=v[A];(!e||!N.removed())&&N.updateStyle()}return L};Pe.move=function(t){var e=this._private.cy,r=this,a=!1,n=!1,i=function(v){return v==null?v:""+v};if(t.source!==void 0||t.target!==void 0){var s=i(t.source),o=i(t.target),u=s!=null&&e.hasElementWithId(s),l=o!=null&&e.hasElementWithId(o);(u||l)&&(e.batch(function(){r.remove(a,n),r.emitAndNotify("moveout");for(var c=0;c<r.length;c++){var v=r[c],p=v._private.data;v.isEdge()&&(u&&(p.source=s),l&&(p.target=o))}r.restore(a,n)}),r.emitAndNotify("move"))}else if(t.parent!==void 0){var f=i(t.parent),h=f===null||e.hasElementWithId(f);if(h){var d=f===null?void 0:f;e.batch(function(){var c=r.remove(a,n);c.emitAndNotify("moveout");for(var v=0;v<r.length;v++){var p=r[v],g=p._private.data;p.isNode()&&(g.parent=d)}c.restore(a,n)}),r.emitAndNotify("move")}}return this};[Po,Ed,Xa,Qt,Vr,Bd,xn,Jd,su,ou,ag,un,qa,je,Jt,it].forEach(function(t){be(Pe,t)});var fg={add:function(e){var r,a=this;if(pt(e)){var n=e;if(n._private.cy===a)r=n.restore();else{for(var i=[],s=0;s<n.length;s++){var o=n[s];i.push(o.json())}r=new et(a,i)}}else if(Re(e)){var u=e;r=new et(a,u)}else if(Ce(e)&&(Re(e.nodes)||Re(e.edges))){for(var l=e,f=[],h=["nodes","edges"],d=0,c=h.length;d<c;d++){var v=h[d],p=l[v];if(Re(p))for(var g=0,y=p.length;g<y;g++){var b=be({group:v},p[g]);f.push(b)}}r=new et(a,f)}else{var m=e;r=new pn(a,m).collection()}return r},remove:function(e){if(!pt(e)){if(de(e)){var r=e;e=this.$(r)}}return e.remove()}};/*! Bezier curve function generator. Copyright Gaetan Renaudeau. MIT License: http://en.wikipedia.org/wiki/MIT_License */function hg(t,e,r,a){var n=4,i=.001,s=1e-7,o=10,u=11,l=1/(u-1),f=typeof Float32Array<"u";if(arguments.length!==4)return!1;for(var h=0;h<4;++h)if(typeof arguments[h]!="number"||isNaN(arguments[h])||!isFinite(arguments[h]))return!1;t=Math.min(t,1),r=Math.min(r,1),t=Math.max(t,0),r=Math.max(r,0);var d=f?new Float32Array(u):new Array(u);function c(D,L){return 1-3*L+3*D}function v(D,L){return 3*L-6*D}function p(D){return 3*D}function g(D,L,A){return((c(L,A)*D+v(L,A))*D+p(L))*D}function y(D,L,A){return 3*c(L,A)*D*D+2*v(L,A)*D+p(L)}function b(D,L){for(var A=0;A<n;++A){var N=y(L,t,r);if(N===0)return L;var O=g(L,t,r)-D;L-=O/N}return L}function m(){for(var D=0;D<u;++D)d[D]=g(D*l,t,r)}function T(D,L,A){var N,O,M=0;do O=L+(A-L)/2,N=g(O,t,r)-D,N>0?A=O:L=O;while(Math.abs(N)>s&&++M<o);return O}function C(D){for(var L=0,A=1,N=u-1;A!==N&&d[A]<=D;++A)L+=l;--A;var O=(D-d[A])/(d[A+1]-d[A]),M=L+O*l,R=y(M,t,r);return R>=i?b(D,M):R===0?M:T(D,L,L+l)}var S=!1;function E(){S=!0,(t!==e||r!==a)&&m()}var w=function(L){return S||E(),t===e&&r===a?L:L===0?0:L===1?1:g(C(L),e,a)};w.getControlPoints=function(){return[{x:t,y:e},{x:r,y:a}]};var x="generateBezier("+[t,e,r,a]+")";return w.toString=function(){return x},w}/*! Runge-Kutta spring physics function generator. Adapted from Framer.js, copyright Koen Bok. MIT License: http://en.wikipedia.org/wiki/MIT_License */var cg=function(){function t(a){return-a.tension*a.x-a.friction*a.v}function e(a,n,i){var s={x:a.x+i.dx*n,v:a.v+i.dv*n,tension:a.tension,friction:a.friction};return{dx:s.v,dv:t(s)}}function r(a,n){var i={dx:a.v,dv:t(a)},s=e(a,n*.5,i),o=e(a,n*.5,s),u=e(a,n,o),l=1/6*(i.dx+2*(s.dx+o.dx)+u.dx),f=1/6*(i.dv+2*(s.dv+o.dv)+u.dv);return a.x=a.x+l*n,a.v=a.v+f*n,a}return function a(n,i,s){var o={x:-1,v:0,tension:null,friction:null},u=[0],l=0,f=1/1e4,h=16/1e3,d,c,v;for(n=parseFloat(n)||500,i=parseFloat(i)||20,s=s||null,o.tension=n,o.friction=i,d=s!==null,d?(l=a(n,i),c=l/s*h):c=h;v=r(v||o,c),u.push(1+v.x),l+=16,Math.abs(v.x)>f&&Math.abs(v.v)>f;);return d?function(p){return u[p*(u.length-1)|0]}:l}}(),ke=function(e,r,a,n){var i=hg(e,r,a,n);return function(s,o,u){return s+(o-s)*i(u)}},Wa={linear:function(e,r,a){return e+(r-e)*a},ease:ke(.25,.1,.25,1),"ease-in":ke(.42,0,1,1),"ease-out":ke(0,0,.58,1),"ease-in-out":ke(.42,0,.58,1),"ease-in-sine":ke(.47,0,.745,.715),"ease-out-sine":ke(.39,.575,.565,1),"ease-in-out-sine":ke(.445,.05,.55,.95),"ease-in-quad":ke(.55,.085,.68,.53),"ease-out-quad":ke(.25,.46,.45,.94),"ease-in-out-quad":ke(.455,.03,.515,.955),"ease-in-cubic":ke(.55,.055,.675,.19),"ease-out-cubic":ke(.215,.61,.355,1),"ease-in-out-cubic":ke(.645,.045,.355,1),"ease-in-quart":ke(.895,.03,.685,.22),"ease-out-quart":ke(.165,.84,.44,1),"ease-in-out-quart":ke(.77,0,.175,1),"ease-in-quint":ke(.755,.05,.855,.06),"ease-out-quint":ke(.23,1,.32,1),"ease-in-out-quint":ke(.86,0,.07,1),"ease-in-expo":ke(.95,.05,.795,.035),"ease-out-expo":ke(.19,1,.22,1),"ease-in-out-expo":ke(1,0,0,1),"ease-in-circ":ke(.6,.04,.98,.335),"ease-out-circ":ke(.075,.82,.165,1),"ease-in-out-circ":ke(.785,.135,.15,.86),spring:function(e,r,a){if(a===0)return Wa.linear;var n=cg(e,r,a);return function(i,s,o){return i+(s-i)*n(o)}},"cubic-bezier":ke};function Ms(t,e,r,a,n){if(a===1||e===r)return r;var i=n(e,r,a);return t==null||((t.roundValue||t.color)&&(i=Math.round(i)),t.min!==void 0&&(i=Math.max(i,t.min)),t.max!==void 0&&(i=Math.min(i,t.max))),i}function Rs(t,e){return t.pfValue!=null||t.value!=null?t.pfValue!=null&&(e==null||e.type.units!=="%")?t.pfValue:t.value:t}function Sr(t,e,r,a,n){var i=n!=null?n.type:null;r<0?r=0:r>1&&(r=1);var s=Rs(t,n),o=Rs(e,n);if(ie(s)&&ie(o))return Ms(i,s,o,r,a);if(Re(s)&&Re(o)){for(var u=[],l=0;l<o.length;l++){var f=s[l],h=o[l];if(f!=null&&h!=null){var d=Ms(i,f,h,r,a);u.push(d)}else u.push(h)}return u}}function vg(t,e,r,a){var n=!a,i=t._private,s=e._private,o=s.easing,u=s.startTime,l=a?t:t.cy(),f=l.style();if(!s.easingImpl)if(o==null)s.easingImpl=Wa.linear;else{var h;if(de(o)){var d=f.parse("transition-timing-function",o);h=d.value}else h=o;var c,v;de(h)?(c=h,v=[]):(c=h[1],v=h.slice(2).map(function(B){return+B})),v.length>0?(c==="spring"&&v.push(s.duration),s.easingImpl=Wa[c].apply(null,v)):s.easingImpl=Wa[c]}var p=s.easingImpl,g;if(s.duration===0?g=1:g=(r-u)/s.duration,s.applying&&(g=s.progress),g<0?g=0:g>1&&(g=1),s.delay==null){var y=s.startPosition,b=s.position;if(b&&n&&!t.locked()){var m={};aa(y.x,b.x)&&(m.x=Sr(y.x,b.x,g,p)),aa(y.y,b.y)&&(m.y=Sr(y.y,b.y,g,p)),t.position(m)}var T=s.startPan,C=s.pan,S=i.pan,E=C!=null&&a;E&&(aa(T.x,C.x)&&(S.x=Sr(T.x,C.x,g,p)),aa(T.y,C.y)&&(S.y=Sr(T.y,C.y,g,p)),t.emit("pan"));var w=s.startZoom,x=s.zoom,D=x!=null&&a;D&&(aa(w,x)&&(i.zoom=ga(i.minZoom,Sr(w,x,g,p),i.maxZoom)),t.emit("zoom")),(E||D)&&t.emit("viewport");var L=s.style;if(L&&L.length>0&&n){for(var A=0;A<L.length;A++){var N=L[A],O=N.name,M=N,R=s.startStyle[O],k=f.properties[R.name],P=Sr(R,M,g,p,k);f.overrideBypass(t,O,P)}t.emit("style")}}return s.progress=g,g}function aa(t,e){return t==null||e==null?!1:ie(t)&&ie(e)?!0:!!(t&&e)}function dg(t,e,r,a){var n=e._private;n.started=!0,n.startTime=r-n.progress*n.duration}function ks(t,e){var r=e._private.aniEles,a=[];function n(f,h){var d=f._private,c=d.animation.current,v=d.animation.queue,p=!1;if(c.length===0){var g=v.shift();g&&c.push(g)}for(var y=function(S){for(var E=S.length-1;E>=0;E--){var w=S[E];w()}S.splice(0,S.length)},b=c.length-1;b>=0;b--){var m=c[b],T=m._private;if(T.stopped){c.splice(b,1),T.hooked=!1,T.playing=!1,T.started=!1,y(T.frames);continue}!T.playing&&!T.applying||(T.playing&&T.applying&&(T.applying=!1),T.started||dg(f,m,t),vg(f,m,t,h),T.applying&&(T.applying=!1),y(T.frames),T.step!=null&&T.step(t),m.completed()&&(c.splice(b,1),T.hooked=!1,T.playing=!1,T.started=!1,y(T.completes)),p=!0)}return!h&&c.length===0&&v.length===0&&a.push(f),p}for(var i=!1,s=0;s<r.length;s++){var o=r[s],u=n(o);i=i||u}var l=n(e,!0);(i||l)&&(r.length>0?e.notify("draw",r):e.notify("draw")),r.unmerge(a),e.emit("step")}var gg={animate:Oe.animate(),animation:Oe.animation(),animated:Oe.animated(),clearQueue:Oe.clearQueue(),delay:Oe.delay(),delayAnimation:Oe.delayAnimation(),stop:Oe.stop(),addToAnimationPool:function(e){var r=this;r.styleEnabled()&&r._private.aniEles.merge(e)},stopAnimationLoop:function(){this._private.animationsRunning=!1},startAnimationLoop:function(){var e=this;if(e._private.animationsRunning=!0,!e.styleEnabled())return;function r(){e._private.animationsRunning&&rn(function(i){ks(i,e),r()})}var a=e.renderer();a&&a.beforeRender?a.beforeRender(function(i,s){ks(s,e)},a.beforeRenderPriorities.animations):r()}},pg={qualifierCompare:function(e,r){return e==null||r==null?e==null&&r==null:e.sameText(r)},eventMatches:function(e,r,a){var n=r.qualifier;return n!=null?e!==a.target&&Ta(a.target)&&n.matches(a.target):!0},addEventFields:function(e,r){r.cy=e,r.target=e},callbackContext:function(e,r,a){return r.qualifier!=null?a.target:e}},Ua=function(e){return de(e)?new tr(e):e},fu={createEmitter:function(){var e=this._private;return e.emitter||(e.emitter=new Tn(pg,this)),this},emitter:function(){return this._private.emitter},on:function(e,r,a){return this.emitter().on(e,Ua(r),a),this},removeListener:function(e,r,a){return this.emitter().removeListener(e,Ua(r),a),this},removeAllListeners:function(){return this.emitter().removeAllListeners(),this},one:function(e,r,a){return this.emitter().one(e,Ua(r),a),this},once:function(e,r,a){return this.emitter().one(e,Ua(r),a),this},emit:function(e,r){return this.emitter().emit(e,r),this},emitAndNotify:function(e,r){return this.emit(e),this.notify(e,r),this}};Oe.eventAliasesOn(fu);var ei={png:function(e){var r=this._private.renderer;return e=e||{},r.png(e)},jpg:function(e){var r=this._private.renderer;return e=e||{},e.bg=e.bg||"#fff",r.jpg(e)}};ei.jpeg=ei.jpg;var Ka={layout:function(e){var r=this;if(e==null){ze("Layout options must be specified to make a layout");return}if(e.name==null){ze("A `name` must be specified to make a layout");return}var a=e.name,n=r.extension("layout",a);if(n==null){ze("No such layout `"+a+"` found.  Did you forget to import it and `cytoscape.use()` it?");return}var i;de(e.eles)?i=r.$(e.eles):i=e.eles!=null?e.eles:r.$();var s=new n(be({},e,{cy:r,eles:i}));return s}};Ka.createLayout=Ka.makeLayout=Ka.layout;var yg={notify:function(e,r){var a=this._private;if(this.batching()){a.batchNotifications=a.batchNotifications||{};var n=a.batchNotifications[e]=a.batchNotifications[e]||this.collection();r!=null&&n.merge(r);return}if(a.notificationsEnabled){var i=this.renderer();this.destroyed()||!i||i.notify(e,r)}},notifications:function(e){var r=this._private;return e===void 0?r.notificationsEnabled:(r.notificationsEnabled=!!e,this)},noNotifications:function(e){this.notifications(!1),e(),this.notifications(!0)},batching:function(){return this._private.batchCount>0},startBatch:function(){var e=this._private;return e.batchCount==null&&(e.batchCount=0),e.batchCount===0&&(e.batchStyleEles=this.collection(),e.batchNotifications={}),e.batchCount++,this},endBatch:function(){var e=this._private;if(e.batchCount===0)return this;if(e.batchCount--,e.batchCount===0){e.batchStyleEles.updateStyle();var r=this.renderer();Object.keys(e.batchNotifications).forEach(function(a){var n=e.batchNotifications[a];n.empty()?r.notify(a):r.notify(a,n)})}return this},batch:function(e){return this.startBatch(),e(),this.endBatch(),this},batchData:function(e){var r=this;return this.batch(function(){for(var a=Object.keys(e),n=0;n<a.length;n++){var i=a[n],s=e[i],o=r.getElementById(i);o.data(s)}})}},mg=tt({hideEdgesOnViewport:!1,textureOnViewport:!1,motionBlur:!1,motionBlurOpacity:.05,pixelRatio:void 0,desktopTapThreshold:4,touchTapThreshold:8,wheelSensitivity:1,debug:!1,showFps:!1}),ti={renderTo:function(e,r,a,n){var i=this._private.renderer;return i.renderTo(e,r,a,n),this},renderer:function(){return this._private.renderer},forceRender:function(){return this.notify("draw"),this},resize:function(){return this.invalidateSize(),this.emitAndNotify("resize"),this},initRenderer:function(e){var r=this,a=r.extension("renderer",e.name);if(a==null){ze("Can not initialise: No such renderer `".concat(e.name,"` found. Did you forget to import it and `cytoscape.use()` it?"));return}e.wheelSensitivity!==void 0&&Ne("You have set a custom wheel sensitivity.  This will make your app zoom unnaturally when using mainstream mice.  You should change this value from the default only if you can guarantee that all your users will use the same hardware and OS configuration as your current machine.");var n=mg(e);n.cy=r,r._private.renderer=new a(n),this.notify("init")},destroyRenderer:function(){var e=this;e.notify("destroy");var r=e.container();if(r)for(r._cyreg=null;r.childNodes.length>0;)r.removeChild(r.childNodes[0]);e._private.renderer=null,e.mutableElements().forEach(function(a){var n=a._private;n.rscratch={},n.rstyle={},n.animation.current=[],n.animation.queue=[]})},onRender:function(e){return this.on("render",e)},offRender:function(e){return this.off("render",e)}};ti.invalidateDimensions=ti.resize;var Za={collection:function(e,r){return de(e)?this.$(e):pt(e)?e.collection():Re(e)?(r||(r={}),new et(this,e,r.unique,r.removed)):new et(this)},nodes:function(e){var r=this.$(function(a){return a.isNode()});return e?r.filter(e):r},edges:function(e){var r=this.$(function(a){return a.isEdge()});return e?r.filter(e):r},$:function(e){var r=this._private.elements;return e?r.filter(e):r.spawnSelf()},mutableElements:function(){return this._private.elements}};Za.elements=Za.filter=Za.$;var ot={},la="t",bg="f";ot.apply=function(t){for(var e=this,r=e._private,a=r.cy,n=a.collection(),i=0;i<t.length;i++){var s=t[i],o=e.getContextMeta(s);if(!o.empty){var u=e.getContextStyle(o),l=e.applyContextStyle(o,u,s);s._private.appliedInitStyle?e.updateTransitions(s,l.diffProps):s._private.appliedInitStyle=!0;var f=e.updateStyleHints(s);f&&n.push(s)}}return n};ot.getPropertiesDiff=function(t,e){var r=this,a=r._private.propDiffs=r._private.propDiffs||{},n=t+"-"+e,i=a[n];if(i)return i;for(var s=[],o={},u=0;u<r.length;u++){var l=r[u],f=t[u]===la,h=e[u]===la,d=f!==h,c=l.mappedProperties.length>0;if(d||h&&c){var v=void 0;d&&c||d?v=l.properties:c&&(v=l.mappedProperties);for(var p=0;p<v.length;p++){for(var g=v[p],y=g.name,b=!1,m=u+1;m<r.length;m++){var T=r[m],C=e[m]===la;if(C&&(b=T.properties[g.name]!=null,b))break}!o[y]&&!b&&(o[y]=!0,s.push(y))}}}return a[n]=s,s};ot.getContextMeta=function(t){for(var e=this,r="",a,n=t._private.styleCxtKey||"",i=0;i<e.length;i++){var s=e[i],o=s.selector&&s.selector.matches(t);o?r+=la:r+=bg}return a=e.getPropertiesDiff(n,r),t._private.styleCxtKey=r,{key:r,diffPropNames:a,empty:a.length===0}};ot.getContextStyle=function(t){var e=t.key,r=this,a=this._private.contextStyles=this._private.contextStyles||{};if(a[e])return a[e];for(var n={_private:{key:e}},i=0;i<r.length;i++){var s=r[i],o=e[i]===la;if(o)for(var u=0;u<s.properties.length;u++){var l=s.properties[u];n[l.name]=l}}return a[e]=n,n};ot.applyContextStyle=function(t,e,r){for(var a=this,n=t.diffPropNames,i={},s=a.types,o=0;o<n.length;o++){var u=n[o],l=e[u],f=r.pstyle(u);if(!l)if(f)f.bypass?l={name:u,deleteBypassed:!0}:l={name:u,delete:!0};else continue;if(f!==l){if(l.mapped===s.fn&&f!=null&&f.mapping!=null&&f.mapping.value===l.value){var h=f.mapping,d=h.fnValue=l.value(r);if(d===h.prevFnValue)continue}var c=i[u]={prev:f};a.applyParsedProperty(r,l),c.next=r.pstyle(u),c.next&&c.next.bypass&&(c.next=c.next.bypassed)}}return{diffProps:i}};ot.updateStyleHints=function(t){var e=t._private,r=this,a=r.propertyGroupNames,n=r.propertyGroupKeys,i=function(Q,ne,ce){return r.getPropertiesHash(Q,ne,ce)},s=e.styleKey;if(t.removed())return!1;var o=e.group==="nodes",u=t._private.style;a=Object.keys(u);for(var l=0;l<n.length;l++){var f=n[l];e.styleKeys[f]=[Nr,ia]}for(var h=function(Q,ne){return e.styleKeys[ne][0]=ca(Q,e.styleKeys[ne][0])},d=function(Q,ne){return e.styleKeys[ne][1]=va(Q,e.styleKeys[ne][1])},c=function(Q,ne){h(Q,ne),d(Q,ne)},v=function(Q,ne){for(var ce=0;ce<Q.length;ce++){var te=Q.charCodeAt(ce);h(te,ne),d(te,ne)}},p=2e9,g=function(Q){return-128<Q&&Q<128&&Math.floor(Q)!==Q?p-(Q*1024|0):Q},y=0;y<a.length;y++){var b=a[y],m=u[b];if(m!=null){var T=this.properties[b],C=T.type,S=T.groupKey,E=void 0;T.hashOverride!=null?E=T.hashOverride(t,m):m.pfValue!=null&&(E=m.pfValue);var w=T.enums==null?m.value:null,x=E!=null,D=w!=null,L=x||D,A=m.units;if(C.number&&L&&!C.multiple){var N=x?E:w;c(g(N),S),!x&&A!=null&&v(A,S)}else v(m.strValue,S)}}for(var O=[Nr,ia],M=0;M<n.length;M++){var R=n[M],k=e.styleKeys[R];O[0]=ca(k[0],O[0]),O[1]=va(k[1],O[1])}e.styleKey=Cf(O[0],O[1]);var P=e.styleKeys;e.labelDimsKey=qt(P.labelDimensions);var B=i(t,["label"],P.labelDimensions);if(e.labelKey=qt(B),e.labelStyleKey=qt(Ma(P.commonLabel,B)),!o){var z=i(t,["source-label"],P.labelDimensions);e.sourceLabelKey=qt(z),e.sourceLabelStyleKey=qt(Ma(P.commonLabel,z));var G=i(t,["target-label"],P.labelDimensions);e.targetLabelKey=qt(G),e.targetLabelStyleKey=qt(Ma(P.commonLabel,G))}if(o){var F=e.styleKeys,U=F.nodeBody,Y=F.nodeBorder,W=F.nodeOutline,K=F.backgroundImage,j=F.compound,_=F.pie,V=[U,Y,W,K,j,_].filter(function(H){return H!=null}).reduce(Ma,[Nr,ia]);e.nodeKey=qt(V),e.hasPie=_!=null&&_[0]!==Nr&&_[1]!==ia}return s!==e.styleKey};ot.clearStyleHints=function(t){var e=t._private;e.styleCxtKey="",e.styleKeys={},e.styleKey=null,e.labelKey=null,e.labelStyleKey=null,e.sourceLabelKey=null,e.sourceLabelStyleKey=null,e.targetLabelKey=null,e.targetLabelStyleKey=null,e.nodeKey=null,e.hasPie=null};ot.applyParsedProperty=function(t,e){var r=this,a=e,n=t._private.style,i,s=r.types,o=r.properties[a.name].type,u=a.bypass,l=n[a.name],f=l&&l.bypass,h=t._private,d="mapping",c=function(U){return U==null?null:U.pfValue!=null?U.pfValue:U.value},v=function(){var U=c(l),Y=c(a);r.checkTriggers(t,a.name,U,Y)};if(e.name==="curve-style"&&t.isEdge()&&(e.value!=="bezier"&&t.isLoop()||e.value==="haystack"&&(t.source().isParent()||t.target().isParent()))&&(a=e=this.parse(e.name,"bezier",u)),a.delete)return n[a.name]=void 0,v(),!0;if(a.deleteBypassed)return l?l.bypass?(l.bypassed=void 0,v(),!0):!1:(v(),!0);if(a.deleteBypass)return l?l.bypass?(n[a.name]=l.bypassed,v(),!0):!1:(v(),!0);var p=function(){Ne("Do not assign mappings to elements without corresponding data (i.e. ele `"+t.id()+"` has no mapping for property `"+a.name+"` with data field `"+a.field+"`); try a `["+a.field+"]` selector to limit scope to elements with `"+a.field+"` defined")};switch(a.mapped){case s.mapData:{for(var g=a.field.split("."),y=h.data,b=0;b<g.length&&y;b++){var m=g[b];y=y[m]}if(y==null)return p(),!1;var T;if(ie(y)){var C=a.fieldMax-a.fieldMin;C===0?T=0:T=(y-a.fieldMin)/C}else return Ne("Do not use continuous mappers without specifying numeric data (i.e. `"+a.field+": "+y+"` for `"+t.id()+"` is non-numeric)"),!1;if(T<0?T=0:T>1&&(T=1),o.color){var S=a.valueMin[0],E=a.valueMax[0],w=a.valueMin[1],x=a.valueMax[1],D=a.valueMin[2],L=a.valueMax[2],A=a.valueMin[3]==null?1:a.valueMin[3],N=a.valueMax[3]==null?1:a.valueMax[3],O=[Math.round(S+(E-S)*T),Math.round(w+(x-w)*T),Math.round(D+(L-D)*T),Math.round(A+(N-A)*T)];i={bypass:a.bypass,name:a.name,value:O,strValue:"rgb("+O[0]+", "+O[1]+", "+O[2]+")"}}else if(o.number){var M=a.valueMin+(a.valueMax-a.valueMin)*T;i=this.parse(a.name,M,a.bypass,d)}else return!1;if(!i)return p(),!1;i.mapping=a,a=i;break}case s.data:{for(var R=a.field.split("."),k=h.data,P=0;P<R.length&&k;P++){var B=R[P];k=k[B]}if(k!=null&&(i=this.parse(a.name,k,a.bypass,d)),!i)return p(),!1;i.mapping=a,a=i;break}case s.fn:{var z=a.value,G=a.fnValue!=null?a.fnValue:z(t);if(a.prevFnValue=G,G==null)return Ne("Custom function mappers may not return null (i.e. `"+a.name+"` for ele `"+t.id()+"` is null)"),!1;if(i=this.parse(a.name,G,a.bypass,d),!i)return Ne("Custom function mappers may not return invalid values for the property type (i.e. `"+a.name+"` for ele `"+t.id()+"` is invalid)"),!1;i.mapping=Pt(a),a=i;break}case void 0:break;default:return!1}return u?(f?a.bypassed=l.bypassed:a.bypassed=l,n[a.name]=a):f?l.bypassed=a:n[a.name]=a,v(),!0};ot.cleanElements=function(t,e){for(var r=0;r<t.length;r++){var a=t[r];if(this.clearStyleHints(a),a.dirtyCompoundBoundsCache(),a.dirtyBoundingBoxCache(),!e)a._private.style={};else for(var n=a._private.style,i=Object.keys(n),s=0;s<i.length;s++){var o=i[s],u=n[o];u!=null&&(u.bypass?u.bypassed=null:n[o]=null)}}};ot.update=function(){var t=this._private.cy,e=t.mutableElements();e.updateStyle()};ot.updateTransitions=function(t,e){var r=this,a=t._private,n=t.pstyle("transition-property").value,i=t.pstyle("transition-duration").pfValue,s=t.pstyle("transition-delay").pfValue;if(n.length>0&&i>0){for(var o={},u=!1,l=0;l<n.length;l++){var f=n[l],h=t.pstyle(f),d=e[f];if(d){var c=d.prev,v=c,p=d.next!=null?d.next:h,g=!1,y=void 0,b=1e-6;v&&(ie(v.pfValue)&&ie(p.pfValue)?(g=p.pfValue-v.pfValue,y=v.pfValue+b*g):ie(v.value)&&ie(p.value)?(g=p.value-v.value,y=v.value+b*g):Re(v.value)&&Re(p.value)&&(g=v.value[0]!==p.value[0]||v.value[1]!==p.value[1]||v.value[2]!==p.value[2],y=v.strValue),g&&(o[f]=p.strValue,this.applyBypass(t,f,y),u=!0))}}if(!u)return;a.transitioning=!0,new $r(function(m){s>0?t.delayAnimation(s).play().promise().then(m):m()}).then(function(){return t.animation({style:o,duration:i,easing:t.pstyle("transition-timing-function").value,queue:!1}).play().promise()}).then(function(){r.removeBypasses(t,n),t.emitAndNotify("style"),a.transitioning=!1})}else a.transitioning&&(this.removeBypasses(t,n),t.emitAndNotify("style"),a.transitioning=!1)};ot.checkTrigger=function(t,e,r,a,n,i){var s=this.properties[e],o=n(s);o!=null&&o(r,a)&&i(s)};ot.checkZOrderTrigger=function(t,e,r,a){var n=this;this.checkTrigger(t,e,r,a,function(i){return i.triggersZOrder},function(){n._private.cy.notify("zorder",t)})};ot.checkBoundsTrigger=function(t,e,r,a){this.checkTrigger(t,e,r,a,function(n){return n.triggersBounds},function(n){t.dirtyCompoundBoundsCache(),t.dirtyBoundingBoxCache(),n.triggersBoundsOfParallelBeziers&&e==="curve-style"&&(r==="bezier"||a==="bezier")&&t.parallelEdges().forEach(function(i){i.isBundledBezier()&&i.dirtyBoundingBoxCache()}),n.triggersBoundsOfConnectedEdges&&e==="display"&&(r==="none"||a==="none")&&t.connectedEdges().forEach(function(i){i.dirtyBoundingBoxCache()})})};ot.checkTriggers=function(t,e,r,a){t.dirtyStyleCache(),this.checkZOrderTrigger(t,e,r,a),this.checkBoundsTrigger(t,e,r,a)};var La={};La.applyBypass=function(t,e,r,a){var n=this,i=[],s=!0;if(e==="*"||e==="**"){if(r!==void 0)for(var o=0;o<n.properties.length;o++){var u=n.properties[o],l=u.name,f=this.parse(l,r,!0);f&&i.push(f)}}else if(de(e)){var h=this.parse(e,r,!0);h&&i.push(h)}else if(Ce(e)){var d=e;a=r;for(var c=Object.keys(d),v=0;v<c.length;v++){var p=c[v],g=d[p];if(g===void 0&&(g=d[vn(p)]),g!==void 0){var y=this.parse(p,g,!0);y&&i.push(y)}}}else return!1;if(i.length===0)return!1;for(var b=!1,m=0;m<t.length;m++){for(var T=t[m],C={},S=void 0,E=0;E<i.length;E++){var w=i[E];if(a){var x=T.pstyle(w.name);S=C[w.name]={prev:x}}b=this.applyParsedProperty(T,Pt(w))||b,a&&(S.next=T.pstyle(w.name))}b&&this.updateStyleHints(T),a&&this.updateTransitions(T,C,s)}return b};La.overrideBypass=function(t,e,r){e=pi(e);for(var a=0;a<t.length;a++){var n=t[a],i=n._private.style[e],s=this.properties[e].type,o=s.color,u=s.mutiple,l=i?i.pfValue!=null?i.pfValue:i.value:null;!i||!i.bypass?this.applyBypass(n,e,r):(i.value=r,i.pfValue!=null&&(i.pfValue=r),o?i.strValue="rgb("+r.join(",")+")":u?i.strValue=r.join(" "):i.strValue=""+r,this.updateStyleHints(n)),this.checkTriggers(n,e,l,r)}};La.removeAllBypasses=function(t,e){return this.removeBypasses(t,this.propertyNames,e)};La.removeBypasses=function(t,e,r){for(var a=!0,n=0;n<t.length;n++){for(var i=t[n],s={},o=0;o<e.length;o++){var u=e[o],l=this.properties[u],f=i.pstyle(l.name);if(!(!f||!f.bypass)){var h="",d=this.parse(u,h,!0),c=s[l.name]={prev:f};this.applyParsedProperty(i,d),c.next=i.pstyle(l.name)}}this.updateStyleHints(i),r&&this.updateTransitions(i,s,a)}};var Ni={};Ni.getEmSizeInPixels=function(){var t=this.containerCss("font-size");return t!=null?parseFloat(t):1};Ni.containerCss=function(t){var e=this._private.cy,r=e.container(),a=e.window();if(a&&r&&a.getComputedStyle)return a.getComputedStyle(r).getPropertyValue(t)};var Ft={};Ft.getRenderedStyle=function(t,e){return e?this.getStylePropertyValue(t,e,!0):this.getRawStyle(t,!0)};Ft.getRawStyle=function(t,e){var r=this;if(t=t[0],t){for(var a={},n=0;n<r.properties.length;n++){var i=r.properties[n],s=r.getStylePropertyValue(t,i.name,e);s!=null&&(a[i.name]=s,a[vn(i.name)]=s)}return a}};Ft.getIndexedStyle=function(t,e,r,a){var n=t.pstyle(e)[r][a];return n??t.cy().style().getDefaultProperty(e)[r][0]};Ft.getStylePropertyValue=function(t,e,r){var a=this;if(t=t[0],t){var n=a.properties[e];n.alias&&(n=n.pointsTo);var i=n.type,s=t.pstyle(n.name);if(s){var o=s.value,u=s.units,l=s.strValue;if(r&&i.number&&o!=null&&ie(o)){var f=t.cy().zoom(),h=function(g){return g*f},d=function(g,y){return h(g)+y},c=Re(o),v=c?u.every(function(p){return p!=null}):u!=null;return v?c?o.map(function(p,g){return d(p,u[g])}).join(" "):d(o,u):c?o.map(function(p){return de(p)?p:""+h(p)}).join(" "):""+h(o)}else if(l!=null)return l}return null}};Ft.getAnimationStartStyle=function(t,e){for(var r={},a=0;a<e.length;a++){var n=e[a],i=n.name,s=t.pstyle(i);s!==void 0&&(Ce(s)?s=this.parse(i,s.strValue):s=this.parse(i,s)),s&&(r[i]=s)}return r};Ft.getPropsList=function(t){var e=this,r=[],a=t,n=e.properties;if(a)for(var i=Object.keys(a),s=0;s<i.length;s++){var o=i[s],u=a[o],l=n[o]||n[pi(o)],f=this.parse(l.name,u);f&&r.push(f)}return r};Ft.getNonDefaultPropertiesHash=function(t,e,r){var a=r.slice(),n,i,s,o,u,l;for(u=0;u<e.length;u++)if(n=e[u],i=t.pstyle(n,!1),i!=null)if(i.pfValue!=null)a[0]=ca(o,a[0]),a[1]=va(o,a[1]);else for(s=i.strValue,l=0;l<s.length;l++)o=s.charCodeAt(l),a[0]=ca(o,a[0]),a[1]=va(o,a[1]);return a};Ft.getPropertiesHash=Ft.getNonDefaultPropertiesHash;var Sn={};Sn.appendFromJson=function(t){for(var e=this,r=0;r<t.length;r++){var a=t[r],n=a.selector,i=a.style||a.css,s=Object.keys(i);e.selector(n);for(var o=0;o<s.length;o++){var u=s[o],l=i[u];e.css(u,l)}}return e};Sn.fromJson=function(t){var e=this;return e.resetToDefault(),e.appendFromJson(t),e};Sn.json=function(){for(var t=[],e=this.defaultLength;e<this.length;e++){for(var r=this[e],a=r.selector,n=r.properties,i={},s=0;s<n.length;s++){var o=n[s];i[o.name]=o.strValue}t.push({selector:a?a.toString():"core",style:i})}return t};var Ii={};Ii.appendFromString=function(t){var e=this,r=this,a=""+t,n,i,s;a=a.replace(/[/][*](\s|.)+?[*][/]/g,"");function o(){a.length>n.length?a=a.substr(n.length):a=""}function u(){i.length>s.length?i=i.substr(s.length):i=""}for(;;){var l=a.match(/^\s*$/);if(l)break;var f=a.match(/^\s*((?:.|\s)+?)\s*\{((?:.|\s)+?)\}/);if(!f){Ne("Halting stylesheet parsing: String stylesheet contains more to parse but no selector and block found in: "+a);break}n=f[0];var h=f[1];if(h!=="core"){var d=new tr(h);if(d.invalid){Ne("Skipping parsing of block: Invalid selector found in string stylesheet: "+h),o();continue}}var c=f[2],v=!1;i=c;for(var p=[];;){var g=i.match(/^\s*$/);if(g)break;var y=i.match(/^\s*(.+?)\s*:\s*(.+?)(?:\s*;|\s*$)/);if(!y){Ne("Skipping parsing of block: Invalid formatting of style property and value definitions found in:"+c),v=!0;break}s=y[0];var b=y[1],m=y[2],T=e.properties[b];if(!T){Ne("Skipping property: Invalid property name in: "+s),u();continue}var C=r.parse(b,m);if(!C){Ne("Skipping property: Invalid property definition in: "+s),u();continue}p.push({name:b,val:m}),u()}if(v){o();break}r.selector(h);for(var S=0;S<p.length;S++){var E=p[S];r.css(E.name,E.val)}o()}return r};Ii.fromString=function(t){var e=this;return e.resetToDefault(),e.appendFromString(t),e};var Je={};(function(){var t=He,e=Sl,r=Al,a=Ol,n=Nl,i=function(V){return"^"+V+"\\s*\\(\\s*([\\w\\.]+)\\s*\\)$"},s=function(V){var H=t+"|\\w+|"+e+"|"+r+"|"+a+"|"+n;return"^"+V+"\\s*\\(([\\w\\.]+)\\s*\\,\\s*("+t+")\\s*\\,\\s*("+t+")\\s*,\\s*("+H+")\\s*\\,\\s*("+H+")\\)$"},o=[`^url\\s*\\(\\s*['"]?(.+?)['"]?\\s*\\)$`,"^(none)$","^(.+)$"];Je.types={time:{number:!0,min:0,units:"s|ms",implicitUnits:"ms"},percent:{number:!0,min:0,max:100,units:"%",implicitUnits:"%"},percentages:{number:!0,min:0,max:100,units:"%",implicitUnits:"%",multiple:!0},zeroOneNumber:{number:!0,min:0,max:1,unitless:!0},zeroOneNumbers:{number:!0,min:0,max:1,unitless:!0,multiple:!0},nOneOneNumber:{number:!0,min:-1,max:1,unitless:!0},nonNegativeInt:{number:!0,min:0,integer:!0,unitless:!0},nonNegativeNumber:{number:!0,min:0,unitless:!0},position:{enums:["parent","origin"]},nodeSize:{number:!0,min:0,enums:["label"]},number:{number:!0,unitless:!0},numbers:{number:!0,unitless:!0,multiple:!0},positiveNumber:{number:!0,unitless:!0,min:0,strictMin:!0},size:{number:!0,min:0},bidirectionalSize:{number:!0},bidirectionalSizeMaybePercent:{number:!0,allowPercent:!0},bidirectionalSizes:{number:!0,multiple:!0},sizeMaybePercent:{number:!0,min:0,allowPercent:!0},axisDirection:{enums:["horizontal","leftward","rightward","vertical","upward","downward","auto"]},paddingRelativeTo:{enums:["width","height","average","min","max"]},bgWH:{number:!0,min:0,allowPercent:!0,enums:["auto"],multiple:!0},bgPos:{number:!0,allowPercent:!0,multiple:!0},bgRelativeTo:{enums:["inner","include-padding"],multiple:!0},bgRepeat:{enums:["repeat","repeat-x","repeat-y","no-repeat"],multiple:!0},bgFit:{enums:["none","contain","cover"],multiple:!0},bgCrossOrigin:{enums:["anonymous","use-credentials","null"],multiple:!0},bgClip:{enums:["none","node"],multiple:!0},bgContainment:{enums:["inside","over"],multiple:!0},color:{color:!0},colors:{color:!0,multiple:!0},fill:{enums:["solid","linear-gradient","radial-gradient"]},bool:{enums:["yes","no"]},bools:{enums:["yes","no"],multiple:!0},lineStyle:{enums:["solid","dotted","dashed"]},lineCap:{enums:["butt","round","square"]},linePosition:{enums:["center","inside","outside"]},lineJoin:{enums:["round","bevel","miter"]},borderStyle:{enums:["solid","dotted","dashed","double"]},curveStyle:{enums:["bezier","unbundled-bezier","haystack","segments","straight","straight-triangle","taxi","round-segments","round-taxi"]},radiusType:{enums:["arc-radius","influence-radius"],multiple:!0},fontFamily:{regex:'^([\\w- \\"]+(?:\\s*,\\s*[\\w- \\"]+)*)$'},fontStyle:{enums:["italic","normal","oblique"]},fontWeight:{enums:["normal","bold","bolder","lighter","100","200","300","400","500","600","800","900",100,200,300,400,500,600,700,800,900]},textDecoration:{enums:["none","underline","overline","line-through"]},textTransform:{enums:["none","uppercase","lowercase"]},textWrap:{enums:["none","wrap","ellipsis"]},textOverflowWrap:{enums:["whitespace","anywhere"]},textBackgroundShape:{enums:["rectangle","roundrectangle","round-rectangle"]},nodeShape:{enums:["rectangle","roundrectangle","round-rectangle","cutrectangle","cut-rectangle","bottomroundrectangle","bottom-round-rectangle","barrel","ellipse","triangle","round-triangle","square","pentagon","round-pentagon","hexagon","round-hexagon","concavehexagon","concave-hexagon","heptagon","round-heptagon","octagon","round-octagon","tag","round-tag","star","diamond","round-diamond","vee","rhomboid","right-rhomboid","polygon"]},overlayShape:{enums:["roundrectangle","round-rectangle","ellipse"]},cornerRadius:{number:!0,min:0,units:"px|em",implicitUnits:"px",enums:["auto"]},compoundIncludeLabels:{enums:["include","exclude"]},arrowShape:{enums:["tee","triangle","triangle-tee","circle-triangle","triangle-cross","triangle-backcurve","vee","square","circle","diamond","chevron","none"]},arrowFill:{enums:["filled","hollow"]},arrowWidth:{number:!0,units:"%|px|em",implicitUnits:"px",enums:["match-line"]},display:{enums:["element","none"]},visibility:{enums:["hidden","visible"]},zCompoundDepth:{enums:["bottom","orphan","auto","top"]},zIndexCompare:{enums:["auto","manual"]},valign:{enums:["top","center","bottom"]},halign:{enums:["left","center","right"]},justification:{enums:["left","center","right","auto"]},text:{string:!0},data:{mapping:!0,regex:i("data")},layoutData:{mapping:!0,regex:i("layoutData")},scratch:{mapping:!0,regex:i("scratch")},mapData:{mapping:!0,regex:s("mapData")},mapLayoutData:{mapping:!0,regex:s("mapLayoutData")},mapScratch:{mapping:!0,regex:s("mapScratch")},fn:{mapping:!0,fn:!0},url:{regexes:o,singleRegexMatchValue:!0},urls:{regexes:o,singleRegexMatchValue:!0,multiple:!0},propList:{propList:!0},angle:{number:!0,units:"deg|rad",implicitUnits:"rad"},textRotation:{number:!0,units:"deg|rad",implicitUnits:"rad",enums:["none","autorotate"]},polygonPointList:{number:!0,multiple:!0,evenMultiple:!0,min:-1,max:1,unitless:!0},edgeDistances:{enums:["intersection","node-position","endpoints"]},edgeEndpoint:{number:!0,multiple:!0,units:"%|px|em|deg|rad",implicitUnits:"px",enums:["inside-to-node","outside-to-node","outside-to-node-or-label","outside-to-line","outside-to-line-or-label"],singleEnum:!0,validate:function(V,H){switch(V.length){case 2:return H[0]!=="deg"&&H[0]!=="rad"&&H[1]!=="deg"&&H[1]!=="rad";case 1:return de(V[0])||H[0]==="deg"||H[0]==="rad";default:return!1}}},easing:{regexes:["^(spring)\\s*\\(\\s*("+t+")\\s*,\\s*("+t+")\\s*\\)$","^(cubic-bezier)\\s*\\(\\s*("+t+")\\s*,\\s*("+t+")\\s*,\\s*("+t+")\\s*,\\s*("+t+")\\s*\\)$"],enums:["linear","ease","ease-in","ease-out","ease-in-out","ease-in-sine","ease-out-sine","ease-in-out-sine","ease-in-quad","ease-out-quad","ease-in-out-quad","ease-in-cubic","ease-out-cubic","ease-in-out-cubic","ease-in-quart","ease-out-quart","ease-in-out-quart","ease-in-quint","ease-out-quint","ease-in-out-quint","ease-in-expo","ease-out-expo","ease-in-out-expo","ease-in-circ","ease-out-circ","ease-in-out-circ"]},gradientDirection:{enums:["to-bottom","to-top","to-left","to-right","to-bottom-right","to-bottom-left","to-top-right","to-top-left","to-right-bottom","to-left-bottom","to-right-top","to-left-top"]},boundsExpansion:{number:!0,multiple:!0,min:0,validate:function(V){var H=V.length;return H===1||H===2||H===4}}};var u={zeroNonZero:function(V,H){return(V==null||H==null)&&V!==H||V==0&&H!=0?!0:V!=0&&H==0},any:function(V,H){return V!=H},emptyNonEmpty:function(V,H){var Q=jt(V),ne=jt(H);return Q&&!ne||!Q&&ne}},l=Je.types,f=[{name:"label",type:l.text,triggersBounds:u.any,triggersZOrder:u.emptyNonEmpty},{name:"text-rotation",type:l.textRotation,triggersBounds:u.any},{name:"text-margin-x",type:l.bidirectionalSize,triggersBounds:u.any},{name:"text-margin-y",type:l.bidirectionalSize,triggersBounds:u.any}],h=[{name:"source-label",type:l.text,triggersBounds:u.any},{name:"source-text-rotation",type:l.textRotation,triggersBounds:u.any},{name:"source-text-margin-x",type:l.bidirectionalSize,triggersBounds:u.any},{name:"source-text-margin-y",type:l.bidirectionalSize,triggersBounds:u.any},{name:"source-text-offset",type:l.size,triggersBounds:u.any}],d=[{name:"target-label",type:l.text,triggersBounds:u.any},{name:"target-text-rotation",type:l.textRotation,triggersBounds:u.any},{name:"target-text-margin-x",type:l.bidirectionalSize,triggersBounds:u.any},{name:"target-text-margin-y",type:l.bidirectionalSize,triggersBounds:u.any},{name:"target-text-offset",type:l.size,triggersBounds:u.any}],c=[{name:"font-family",type:l.fontFamily,triggersBounds:u.any},{name:"font-style",type:l.fontStyle,triggersBounds:u.any},{name:"font-weight",type:l.fontWeight,triggersBounds:u.any},{name:"font-size",type:l.size,triggersBounds:u.any},{name:"text-transform",type:l.textTransform,triggersBounds:u.any},{name:"text-wrap",type:l.textWrap,triggersBounds:u.any},{name:"text-overflow-wrap",type:l.textOverflowWrap,triggersBounds:u.any},{name:"text-max-width",type:l.size,triggersBounds:u.any},{name:"text-outline-width",type:l.size,triggersBounds:u.any},{name:"line-height",type:l.positiveNumber,triggersBounds:u.any}],v=[{name:"text-valign",type:l.valign,triggersBounds:u.any},{name:"text-halign",type:l.halign,triggersBounds:u.any},{name:"color",type:l.color},{name:"text-outline-color",type:l.color},{name:"text-outline-opacity",type:l.zeroOneNumber},{name:"text-background-color",type:l.color},{name:"text-background-opacity",type:l.zeroOneNumber},{name:"text-background-padding",type:l.size,triggersBounds:u.any},{name:"text-border-opacity",type:l.zeroOneNumber},{name:"text-border-color",type:l.color},{name:"text-border-width",type:l.size,triggersBounds:u.any},{name:"text-border-style",type:l.borderStyle,triggersBounds:u.any},{name:"text-background-shape",type:l.textBackgroundShape,triggersBounds:u.any},{name:"text-justification",type:l.justification}],p=[{name:"events",type:l.bool,triggersZOrder:u.any},{name:"text-events",type:l.bool,triggersZOrder:u.any}],g=[{name:"display",type:l.display,triggersZOrder:u.any,triggersBounds:u.any,triggersBoundsOfConnectedEdges:!0},{name:"visibility",type:l.visibility,triggersZOrder:u.any},{name:"opacity",type:l.zeroOneNumber,triggersZOrder:u.zeroNonZero},{name:"text-opacity",type:l.zeroOneNumber},{name:"min-zoomed-font-size",type:l.size},{name:"z-compound-depth",type:l.zCompoundDepth,triggersZOrder:u.any},{name:"z-index-compare",type:l.zIndexCompare,triggersZOrder:u.any},{name:"z-index",type:l.number,triggersZOrder:u.any}],y=[{name:"overlay-padding",type:l.size,triggersBounds:u.any},{name:"overlay-color",type:l.color},{name:"overlay-opacity",type:l.zeroOneNumber,triggersBounds:u.zeroNonZero},{name:"overlay-shape",type:l.overlayShape,triggersBounds:u.any},{name:"overlay-corner-radius",type:l.cornerRadius}],b=[{name:"underlay-padding",type:l.size,triggersBounds:u.any},{name:"underlay-color",type:l.color},{name:"underlay-opacity",type:l.zeroOneNumber,triggersBounds:u.zeroNonZero},{name:"underlay-shape",type:l.overlayShape,triggersBounds:u.any},{name:"underlay-corner-radius",type:l.cornerRadius}],m=[{name:"transition-property",type:l.propList},{name:"transition-duration",type:l.time},{name:"transition-delay",type:l.time},{name:"transition-timing-function",type:l.easing}],T=function(V,H){return H.value==="label"?-V.poolIndex():H.pfValue},C=[{name:"height",type:l.nodeSize,triggersBounds:u.any,hashOverride:T},{name:"width",type:l.nodeSize,triggersBounds:u.any,hashOverride:T},{name:"shape",type:l.nodeShape,triggersBounds:u.any},{name:"shape-polygon-points",type:l.polygonPointList,triggersBounds:u.any},{name:"corner-radius",type:l.cornerRadius},{name:"background-color",type:l.color},{name:"background-fill",type:l.fill},{name:"background-opacity",type:l.zeroOneNumber},{name:"background-blacken",type:l.nOneOneNumber},{name:"background-gradient-stop-colors",type:l.colors},{name:"background-gradient-stop-positions",type:l.percentages},{name:"background-gradient-direction",type:l.gradientDirection},{name:"padding",type:l.sizeMaybePercent,triggersBounds:u.any},{name:"padding-relative-to",type:l.paddingRelativeTo,triggersBounds:u.any},{name:"bounds-expansion",type:l.boundsExpansion,triggersBounds:u.any}],S=[{name:"border-color",type:l.color},{name:"border-opacity",type:l.zeroOneNumber},{name:"border-width",type:l.size,triggersBounds:u.any},{name:"border-style",type:l.borderStyle},{name:"border-cap",type:l.lineCap},{name:"border-join",type:l.lineJoin},{name:"border-dash-pattern",type:l.numbers},{name:"border-dash-offset",type:l.number},{name:"border-position",type:l.linePosition}],E=[{name:"outline-color",type:l.color},{name:"outline-opacity",type:l.zeroOneNumber},{name:"outline-width",type:l.size,triggersBounds:u.any},{name:"outline-style",type:l.borderStyle},{name:"outline-offset",type:l.size,triggersBounds:u.any}],w=[{name:"background-image",type:l.urls},{name:"background-image-crossorigin",type:l.bgCrossOrigin},{name:"background-image-opacity",type:l.zeroOneNumbers},{name:"background-image-containment",type:l.bgContainment},{name:"background-image-smoothing",type:l.bools},{name:"background-position-x",type:l.bgPos},{name:"background-position-y",type:l.bgPos},{name:"background-width-relative-to",type:l.bgRelativeTo},{name:"background-height-relative-to",type:l.bgRelativeTo},{name:"background-repeat",type:l.bgRepeat},{name:"background-fit",type:l.bgFit},{name:"background-clip",type:l.bgClip},{name:"background-width",type:l.bgWH},{name:"background-height",type:l.bgWH},{name:"background-offset-x",type:l.bgPos},{name:"background-offset-y",type:l.bgPos}],x=[{name:"position",type:l.position,triggersBounds:u.any},{name:"compound-sizing-wrt-labels",type:l.compoundIncludeLabels,triggersBounds:u.any},{name:"min-width",type:l.size,triggersBounds:u.any},{name:"min-width-bias-left",type:l.sizeMaybePercent,triggersBounds:u.any},{name:"min-width-bias-right",type:l.sizeMaybePercent,triggersBounds:u.any},{name:"min-height",type:l.size,triggersBounds:u.any},{name:"min-height-bias-top",type:l.sizeMaybePercent,triggersBounds:u.any},{name:"min-height-bias-bottom",type:l.sizeMaybePercent,triggersBounds:u.any}],D=[{name:"line-style",type:l.lineStyle},{name:"line-color",type:l.color},{name:"line-fill",type:l.fill},{name:"line-cap",type:l.lineCap},{name:"line-opacity",type:l.zeroOneNumber},{name:"line-dash-pattern",type:l.numbers},{name:"line-dash-offset",type:l.number},{name:"line-outline-width",type:l.size},{name:"line-outline-color",type:l.color},{name:"line-gradient-stop-colors",type:l.colors},{name:"line-gradient-stop-positions",type:l.percentages},{name:"curve-style",type:l.curveStyle,triggersBounds:u.any,triggersBoundsOfParallelBeziers:!0},{name:"haystack-radius",type:l.zeroOneNumber,triggersBounds:u.any},{name:"source-endpoint",type:l.edgeEndpoint,triggersBounds:u.any},{name:"target-endpoint",type:l.edgeEndpoint,triggersBounds:u.any},{name:"control-point-step-size",type:l.size,triggersBounds:u.any},{name:"control-point-distances",type:l.bidirectionalSizes,triggersBounds:u.any},{name:"control-point-weights",type:l.numbers,triggersBounds:u.any},{name:"segment-distances",type:l.bidirectionalSizes,triggersBounds:u.any},{name:"segment-weights",type:l.numbers,triggersBounds:u.any},{name:"segment-radii",type:l.numbers,triggersBounds:u.any},{name:"radius-type",type:l.radiusType,triggersBounds:u.any},{name:"taxi-turn",type:l.bidirectionalSizeMaybePercent,triggersBounds:u.any},{name:"taxi-turn-min-distance",type:l.size,triggersBounds:u.any},{name:"taxi-direction",type:l.axisDirection,triggersBounds:u.any},{name:"taxi-radius",type:l.number,triggersBounds:u.any},{name:"edge-distances",type:l.edgeDistances,triggersBounds:u.any},{name:"arrow-scale",type:l.positiveNumber,triggersBounds:u.any},{name:"loop-direction",type:l.angle,triggersBounds:u.any},{name:"loop-sweep",type:l.angle,triggersBounds:u.any},{name:"source-distance-from-node",type:l.size,triggersBounds:u.any},{name:"target-distance-from-node",type:l.size,triggersBounds:u.any}],L=[{name:"ghost",type:l.bool,triggersBounds:u.any},{name:"ghost-offset-x",type:l.bidirectionalSize,triggersBounds:u.any},{name:"ghost-offset-y",type:l.bidirectionalSize,triggersBounds:u.any},{name:"ghost-opacity",type:l.zeroOneNumber}],A=[{name:"selection-box-color",type:l.color},{name:"selection-box-opacity",type:l.zeroOneNumber},{name:"selection-box-border-color",type:l.color},{name:"selection-box-border-width",type:l.size},{name:"active-bg-color",type:l.color},{name:"active-bg-opacity",type:l.zeroOneNumber},{name:"active-bg-size",type:l.size},{name:"outside-texture-bg-color",type:l.color},{name:"outside-texture-bg-opacity",type:l.zeroOneNumber}],N=[];Je.pieBackgroundN=16,N.push({name:"pie-size",type:l.sizeMaybePercent});for(var O=1;O<=Je.pieBackgroundN;O++)N.push({name:"pie-"+O+"-background-color",type:l.color}),N.push({name:"pie-"+O+"-background-size",type:l.percent}),N.push({name:"pie-"+O+"-background-opacity",type:l.zeroOneNumber});var M=[],R=Je.arrowPrefixes=["source","mid-source","target","mid-target"];[{name:"arrow-shape",type:l.arrowShape,triggersBounds:u.any},{name:"arrow-color",type:l.color},{name:"arrow-fill",type:l.arrowFill},{name:"arrow-width",type:l.arrowWidth}].forEach(function(_){R.forEach(function(V){var H=V+"-"+_.name,Q=_.type,ne=_.triggersBounds;M.push({name:H,type:Q,triggersBounds:ne})})},{});var k=Je.properties=[].concat(p,m,g,y,b,L,v,c,f,h,d,C,S,E,w,N,x,D,M,A),P=Je.propertyGroups={behavior:p,transition:m,visibility:g,overlay:y,underlay:b,ghost:L,commonLabel:v,labelDimensions:c,mainLabel:f,sourceLabel:h,targetLabel:d,nodeBody:C,nodeBorder:S,nodeOutline:E,backgroundImage:w,pie:N,compound:x,edgeLine:D,edgeArrow:M,core:A},B=Je.propertyGroupNames={},z=Je.propertyGroupKeys=Object.keys(P);z.forEach(function(_){B[_]=P[_].map(function(V){return V.name}),P[_].forEach(function(V){return V.groupKey=_})});var G=Je.aliases=[{name:"content",pointsTo:"label"},{name:"control-point-distance",pointsTo:"control-point-distances"},{name:"control-point-weight",pointsTo:"control-point-weights"},{name:"segment-distance",pointsTo:"segment-distances"},{name:"segment-weight",pointsTo:"segment-weights"},{name:"segment-radius",pointsTo:"segment-radii"},{name:"edge-text-rotation",pointsTo:"text-rotation"},{name:"padding-left",pointsTo:"padding"},{name:"padding-right",pointsTo:"padding"},{name:"padding-top",pointsTo:"padding"},{name:"padding-bottom",pointsTo:"padding"}];Je.propertyNames=k.map(function(_){return _.name});for(var F=0;F<k.length;F++){var U=k[F];k[U.name]=U}for(var Y=0;Y<G.length;Y++){var W=G[Y],K=k[W.pointsTo],j={name:W.name,alias:!0,pointsTo:K};k.push(j),k[W.name]=j}})();Je.getDefaultProperty=function(t){return this.getDefaultProperties()[t]};Je.getDefaultProperties=function(){var t=this._private;if(t.defaultProperties!=null)return t.defaultProperties;for(var e=be({"selection-box-color":"#ddd","selection-box-opacity":.65,"selection-box-border-color":"#aaa","selection-box-border-width":1,"active-bg-color":"black","active-bg-opacity":.15,"active-bg-size":30,"outside-texture-bg-color":"#000","outside-texture-bg-opacity":.125,events:"yes","text-events":"no","text-valign":"top","text-halign":"center","text-justification":"auto","line-height":1,color:"#000","text-outline-color":"#000","text-outline-width":0,"text-outline-opacity":1,"text-opacity":1,"text-decoration":"none","text-transform":"none","text-wrap":"none","text-overflow-wrap":"whitespace","text-max-width":9999,"text-background-color":"#000","text-background-opacity":0,"text-background-shape":"rectangle","text-background-padding":0,"text-border-opacity":0,"text-border-width":0,"text-border-style":"solid","text-border-color":"#000","font-family":"Helvetica Neue, Helvetica, sans-serif","font-style":"normal","font-weight":"normal","font-size":16,"min-zoomed-font-size":0,"text-rotation":"none","source-text-rotation":"none","target-text-rotation":"none",visibility:"visible",display:"element",opacity:1,"z-compound-depth":"auto","z-index-compare":"auto","z-index":0,label:"","text-margin-x":0,"text-margin-y":0,"source-label":"","source-text-offset":0,"source-text-margin-x":0,"source-text-margin-y":0,"target-label":"","target-text-offset":0,"target-text-margin-x":0,"target-text-margin-y":0,"overlay-opacity":0,"overlay-color":"#000","overlay-padding":10,"overlay-shape":"round-rectangle","overlay-corner-radius":"auto","underlay-opacity":0,"underlay-color":"#000","underlay-padding":10,"underlay-shape":"round-rectangle","underlay-corner-radius":"auto","transition-property":"none","transition-duration":0,"transition-delay":0,"transition-timing-function":"linear","background-blacken":0,"background-color":"#999","background-fill":"solid","background-opacity":1,"background-image":"none","background-image-crossorigin":"anonymous","background-image-opacity":1,"background-image-containment":"inside","background-image-smoothing":"yes","background-position-x":"50%","background-position-y":"50%","background-offset-x":0,"background-offset-y":0,"background-width-relative-to":"include-padding","background-height-relative-to":"include-padding","background-repeat":"no-repeat","background-fit":"none","background-clip":"node","background-width":"auto","background-height":"auto","border-color":"#000","border-opacity":1,"border-width":0,"border-style":"solid","border-dash-pattern":[4,2],"border-dash-offset":0,"border-cap":"butt","border-join":"miter","border-position":"center","outline-color":"#999","outline-opacity":1,"outline-width":0,"outline-offset":0,"outline-style":"solid",height:30,width:30,shape:"ellipse","shape-polygon-points":"-1, -1,   1, -1,   1, 1,   -1, 1","corner-radius":"auto","bounds-expansion":0,"background-gradient-direction":"to-bottom","background-gradient-stop-colors":"#999","background-gradient-stop-positions":"0%",ghost:"no","ghost-offset-y":0,"ghost-offset-x":0,"ghost-opacity":0,padding:0,"padding-relative-to":"width",position:"origin","compound-sizing-wrt-labels":"include","min-width":0,"min-width-bias-left":0,"min-width-bias-right":0,"min-height":0,"min-height-bias-top":0,"min-height-bias-bottom":0},{"pie-size":"100%"},[{name:"pie-{{i}}-background-color",value:"black"},{name:"pie-{{i}}-background-size",value:"0%"},{name:"pie-{{i}}-background-opacity",value:1}].reduce(function(u,l){for(var f=1;f<=Je.pieBackgroundN;f++){var h=l.name.replace("{{i}}",f),d=l.value;u[h]=d}return u},{}),{"line-style":"solid","line-color":"#999","line-fill":"solid","line-cap":"butt","line-opacity":1,"line-outline-width":0,"line-outline-color":"#000","line-gradient-stop-colors":"#999","line-gradient-stop-positions":"0%","control-point-step-size":40,"control-point-weights":.5,"segment-weights":.5,"segment-distances":20,"segment-radii":15,"radius-type":"arc-radius","taxi-turn":"50%","taxi-radius":15,"taxi-turn-min-distance":10,"taxi-direction":"auto","edge-distances":"intersection","curve-style":"haystack","haystack-radius":0,"arrow-scale":1,"loop-direction":"-45deg","loop-sweep":"-90deg","source-distance-from-node":0,"target-distance-from-node":0,"source-endpoint":"outside-to-node","target-endpoint":"outside-to-node","line-dash-pattern":[6,3],"line-dash-offset":0},[{name:"arrow-shape",value:"none"},{name:"arrow-color",value:"#999"},{name:"arrow-fill",value:"filled"},{name:"arrow-width",value:1}].reduce(function(u,l){return Je.arrowPrefixes.forEach(function(f){var h=f+"-"+l.name,d=l.value;u[h]=d}),u},{})),r={},a=0;a<this.properties.length;a++){var n=this.properties[a];if(!n.pointsTo){var i=n.name,s=e[i],o=this.parse(i,s);r[i]=o}}return t.defaultProperties=r,t.defaultProperties};Je.addDefaultStylesheet=function(){this.selector(":parent").css({shape:"rectangle",padding:10,"background-color":"#eee","border-color":"#ccc","border-width":1}).selector("edge").css({width:3}).selector(":loop").css({"curve-style":"bezier"}).selector("edge:compound").css({"curve-style":"bezier","source-endpoint":"outside-to-line","target-endpoint":"outside-to-line"}).selector(":selected").css({"background-color":"#0169D9","line-color":"#0169D9","source-arrow-color":"#0169D9","target-arrow-color":"#0169D9","mid-source-arrow-color":"#0169D9","mid-target-arrow-color":"#0169D9"}).selector(":parent:selected").css({"background-color":"#CCE1F9","border-color":"#aec8e5"}).selector(":active").css({"overlay-color":"black","overlay-padding":10,"overlay-opacity":.25}),this.defaultLength=this.length};var Ln={};Ln.parse=function(t,e,r,a){var n=this;if(Ge(e))return n.parseImplWarn(t,e,r,a);var i=a==="mapping"||a===!0||a===!1||a==null?"dontcare":a,s=r?"t":"f",o=""+e,u=bo(t,o,s,i),l=n.propCache=n.propCache||[],f;return(f=l[u])||(f=l[u]=n.parseImplWarn(t,e,r,a)),(r||a==="mapping")&&(f=Pt(f),f&&(f.value=Pt(f.value))),f};Ln.parseImplWarn=function(t,e,r,a){var n=this.parseImpl(t,e,r,a);return!n&&e!=null&&Ne("The style property `".concat(t,": ").concat(e,"` is invalid")),n&&(n.name==="width"||n.name==="height")&&e==="label"&&Ne("The style value of `label` is deprecated for `"+n.name+"`"),n};Ln.parseImpl=function(t,e,r,a){var n=this;t=pi(t);var i=n.properties[t],s=e,o=n.types;if(!i||e===void 0)return null;i.alias&&(i=i.pointsTo,t=i.name);var u=de(e);u&&(e=e.trim());var l=i.type;if(!l)return null;if(r&&(e===""||e===null))return{name:t,value:e,bypass:!0,deleteBypass:!0};if(Ge(e))return{name:t,value:e,strValue:"fn",mapped:o.fn,bypass:r};var f,h;if(!(!u||a||e.length<7||e[1]!=="a")){if(e.length>=7&&e[0]==="d"&&(f=new RegExp(o.data.regex).exec(e))){if(r)return!1;var d=o.data;return{name:t,value:f,strValue:""+e,mapped:d,field:f[1],bypass:r}}else if(e.length>=10&&e[0]==="m"&&(h=new RegExp(o.mapData.regex).exec(e))){if(r||l.multiple)return!1;var c=o.mapData;if(!(l.color||l.number))return!1;var v=this.parse(t,h[4]);if(!v||v.mapped)return!1;var p=this.parse(t,h[5]);if(!p||p.mapped)return!1;if(v.pfValue===p.pfValue||v.strValue===p.strValue)return Ne("`"+t+": "+e+"` is not a valid mapper because the output range is zero; converting to `"+t+": "+v.strValue+"`"),this.parse(t,v.strValue);if(l.color){var g=v.value,y=p.value,b=g[0]===y[0]&&g[1]===y[1]&&g[2]===y[2]&&(g[3]===y[3]||(g[3]==null||g[3]===1)&&(y[3]==null||y[3]===1));if(b)return!1}return{name:t,value:h,strValue:""+e,mapped:c,field:h[1],fieldMin:parseFloat(h[2]),fieldMax:parseFloat(h[3]),valueMin:v.value,valueMax:p.value,bypass:r}}}if(l.multiple&&a!=="multiple"){var m;if(u?m=e.split(/\s+/):Re(e)?m=e:m=[e],l.evenMultiple&&m.length%2!==0)return null;for(var T=[],C=[],S=[],E="",w=!1,x=0;x<m.length;x++){var D=n.parse(t,m[x],r,"multiple");w=w||de(D.value),T.push(D.value),S.push(D.pfValue!=null?D.pfValue:D.value),C.push(D.units),E+=(x>0?" ":"")+D.strValue}return l.validate&&!l.validate(T,C)?null:l.singleEnum&&w?T.length===1&&de(T[0])?{name:t,value:T[0],strValue:T[0],bypass:r}:null:{name:t,value:T,pfValue:S,strValue:E,bypass:r,units:C}}var L=function(){for(var V=0;V<l.enums.length;V++){var H=l.enums[V];if(H===e)return{name:t,value:e,strValue:""+e,bypass:r}}return null};if(l.number){var A,N="px";if(l.units&&(A=l.units),l.implicitUnits&&(N=l.implicitUnits),!l.unitless)if(u){var O="px|em"+(l.allowPercent?"|\\%":"");A&&(O=A);var M=e.match("^("+He+")("+O+")?$");M&&(e=M[1],A=M[2]||N)}else(!A||l.implicitUnits)&&(A=N);if(e=parseFloat(e),isNaN(e)&&l.enums===void 0)return null;if(isNaN(e)&&l.enums!==void 0)return e=s,L();if(l.integer&&!bl(e)||l.min!==void 0&&(e<l.min||l.strictMin&&e===l.min)||l.max!==void 0&&(e>l.max||l.strictMax&&e===l.max))return null;var R={name:t,value:e,strValue:""+e+(A||""),units:A,bypass:r};return l.unitless||A!=="px"&&A!=="em"?R.pfValue=e:R.pfValue=A==="px"||!A?e:this.getEmSizeInPixels()*e,(A==="ms"||A==="s")&&(R.pfValue=A==="ms"?e:1e3*e),(A==="deg"||A==="rad")&&(R.pfValue=A==="rad"?e:eh(e)),A==="%"&&(R.pfValue=e/100),R}else if(l.propList){var k=[],P=""+e;if(P!=="none"){for(var B=P.split(/\s*,\s*|\s+/),z=0;z<B.length;z++){var G=B[z].trim();n.properties[G]?k.push(G):Ne("`"+G+"` is not a valid property name")}if(k.length===0)return null}return{name:t,value:k,strValue:k.length===0?"none":k.join(" "),bypass:r}}else if(l.color){var F=Bl(e);return F?{name:t,value:F,pfValue:F,strValue:"rgb("+F[0]+","+F[1]+","+F[2]+")",bypass:r}:null}else if(l.regex||l.regexes){if(l.enums){var U=L();if(U)return U}for(var Y=l.regexes?l.regexes:[l.regex],W=0;W<Y.length;W++){var K=new RegExp(Y[W]),j=K.exec(e);if(j)return{name:t,value:l.singleRegexMatchValue?j[1]:j,strValue:""+e,bypass:r}}return null}else return l.string?{name:t,value:""+e,strValue:""+e,bypass:r}:l.enums?L():null};var nt=function t(e){if(!(this instanceof t))return new t(e);if(!gi(e)){ze("A style must have a core reference");return}this._private={cy:e,coreStyle:{}},this.length=0,this.resetToDefault()},st=nt.prototype;st.instanceString=function(){return"style"};st.clear=function(){for(var t=this._private,e=t.cy,r=e.elements(),a=0;a<this.length;a++)this[a]=void 0;return this.length=0,t.contextStyles={},t.propDiffs={},this.cleanElements(r,!0),r.forEach(function(n){var i=n[0]._private;i.styleDirty=!0,i.appliedInitStyle=!1}),this};st.resetToDefault=function(){return this.clear(),this.addDefaultStylesheet(),this};st.core=function(t){return this._private.coreStyle[t]||this.getDefaultProperty(t)};st.selector=function(t){var e=t==="core"?null:new tr(t),r=this.length++;return this[r]={selector:e,properties:[],mappedProperties:[],index:r},this};st.css=function(){var t=this,e=arguments;if(e.length===1)for(var r=e[0],a=0;a<t.properties.length;a++){var n=t.properties[a],i=r[n.name];i===void 0&&(i=r[vn(n.name)]),i!==void 0&&this.cssRule(n.name,i)}else e.length===2&&this.cssRule(e[0],e[1]);return this};st.style=st.css;st.cssRule=function(t,e){var r=this.parse(t,e);if(r){var a=this.length-1;this[a].properties.push(r),this[a].properties[r.name]=r,r.name.match(/pie-(\d+)-background-size/)&&r.value&&(this._private.hasPie=!0),r.mapped&&this[a].mappedProperties.push(r);var n=!this[a].selector;n&&(this._private.coreStyle[r.name]=r)}return this};st.append=function(t){return uo(t)?t.appendToStyle(this):Re(t)?this.appendFromJson(t):de(t)&&this.appendFromString(t),this};nt.fromJson=function(t,e){var r=new nt(t);return r.fromJson(e),r};nt.fromString=function(t,e){return new nt(t).fromString(e)};[ot,La,Ni,Ft,Sn,Ii,Je,Ln].forEach(function(t){be(st,t)});nt.types=st.types;nt.properties=st.properties;nt.propertyGroups=st.propertyGroups;nt.propertyGroupNames=st.propertyGroupNames;nt.propertyGroupKeys=st.propertyGroupKeys;var Eg={style:function(e){if(e){var r=this.setStyle(e);r.update()}return this._private.style},setStyle:function(e){var r=this._private;return uo(e)?r.style=e.generateStyle(this):Re(e)?r.style=nt.fromJson(this,e):de(e)?r.style=nt.fromString(this,e):r.style=nt(this),r.style},updateStyle:function(){this.mutableElements().updateStyle()}},wg="single",mr={autolock:function(e){if(e!==void 0)this._private.autolock=!!e;else return this._private.autolock;return this},autoungrabify:function(e){if(e!==void 0)this._private.autoungrabify=!!e;else return this._private.autoungrabify;return this},autounselectify:function(e){if(e!==void 0)this._private.autounselectify=!!e;else return this._private.autounselectify;return this},selectionType:function(e){var r=this._private;if(r.selectionType==null&&(r.selectionType=wg),e!==void 0)(e==="additive"||e==="single")&&(r.selectionType=e);else return r.selectionType;return this},panningEnabled:function(e){if(e!==void 0)this._private.panningEnabled=!!e;else return this._private.panningEnabled;return this},userPanningEnabled:function(e){if(e!==void 0)this._private.userPanningEnabled=!!e;else return this._private.userPanningEnabled;return this},zoomingEnabled:function(e){if(e!==void 0)this._private.zoomingEnabled=!!e;else return this._private.zoomingEnabled;return this},userZoomingEnabled:function(e){if(e!==void 0)this._private.userZoomingEnabled=!!e;else return this._private.userZoomingEnabled;return this},boxSelectionEnabled:function(e){if(e!==void 0)this._private.boxSelectionEnabled=!!e;else return this._private.boxSelectionEnabled;return this},pan:function(){var e=arguments,r=this._private.pan,a,n,i,s,o;switch(e.length){case 0:return r;case 1:if(de(e[0]))return a=e[0],r[a];if(Ce(e[0])){if(!this._private.panningEnabled)return this;i=e[0],s=i.x,o=i.y,ie(s)&&(r.x=s),ie(o)&&(r.y=o),this.emit("pan viewport")}break;case 2:if(!this._private.panningEnabled)return this;a=e[0],n=e[1],(a==="x"||a==="y")&&ie(n)&&(r[a]=n),this.emit("pan viewport");break}return this.notify("viewport"),this},panBy:function(e,r){var a=arguments,n=this._private.pan,i,s,o,u,l;if(!this._private.panningEnabled)return this;switch(a.length){case 1:Ce(e)&&(o=a[0],u=o.x,l=o.y,ie(u)&&(n.x+=u),ie(l)&&(n.y+=l),this.emit("pan viewport"));break;case 2:i=e,s=r,(i==="x"||i==="y")&&ie(s)&&(n[i]+=s),this.emit("pan viewport");break}return this.notify("viewport"),this},fit:function(e,r){var a=this.getFitViewport(e,r);if(a){var n=this._private;n.zoom=a.zoom,n.pan=a.pan,this.emit("pan zoom viewport"),this.notify("viewport")}return this},getFitViewport:function(e,r){if(ie(e)&&r===void 0&&(r=e,e=void 0),!(!this._private.panningEnabled||!this._private.zoomingEnabled)){var a;if(de(e)){var n=e;e=this.$(n)}else if(xl(e)){var i=e;a={x1:i.x1,y1:i.y1,x2:i.x2,y2:i.y2},a.w=a.x2-a.x1,a.h=a.y2-a.y1}else pt(e)||(e=this.mutableElements());if(!(pt(e)&&e.empty())){a=a||e.boundingBox();var s=this.width(),o=this.height(),u;if(r=ie(r)?r:0,!isNaN(s)&&!isNaN(o)&&s>0&&o>0&&!isNaN(a.w)&&!isNaN(a.h)&&a.w>0&&a.h>0){u=Math.min((s-2*r)/a.w,(o-2*r)/a.h),u=u>this._private.maxZoom?this._private.maxZoom:u,u=u<this._private.minZoom?this._private.minZoom:u;var l={x:(s-u*(a.x1+a.x2))/2,y:(o-u*(a.y1+a.y2))/2};return{zoom:u,pan:l}}}}},zoomRange:function(e,r){var a=this._private;if(r==null){var n=e;e=n.min,r=n.max}return ie(e)&&ie(r)&&e<=r?(a.minZoom=e,a.maxZoom=r):ie(e)&&r===void 0&&e<=a.maxZoom?a.minZoom=e:ie(r)&&e===void 0&&r>=a.minZoom&&(a.maxZoom=r),this},minZoom:function(e){return e===void 0?this._private.minZoom:this.zoomRange({min:e})},maxZoom:function(e){return e===void 0?this._private.maxZoom:this.zoomRange({max:e})},getZoomedViewport:function(e){var r=this._private,a=r.pan,n=r.zoom,i,s,o=!1;if(r.zoomingEnabled||(o=!0),ie(e)?s=e:Ce(e)&&(s=e.level,e.position!=null?i=yn(e.position,n,a):e.renderedPosition!=null&&(i=e.renderedPosition),i!=null&&!r.panningEnabled&&(o=!0)),s=s>r.maxZoom?r.maxZoom:s,s=s<r.minZoom?r.minZoom:s,o||!ie(s)||s===n||i!=null&&(!ie(i.x)||!ie(i.y)))return null;if(i!=null){var u=a,l=n,f=s,h={x:-f/l*(i.x-u.x)+i.x,y:-f/l*(i.y-u.y)+i.y};return{zoomed:!0,panned:!0,zoom:f,pan:h}}else return{zoomed:!0,panned:!1,zoom:s,pan:a}},zoom:function(e){if(e===void 0)return this._private.zoom;var r=this.getZoomedViewport(e),a=this._private;return r==null||!r.zoomed?this:(a.zoom=r.zoom,r.panned&&(a.pan.x=r.pan.x,a.pan.y=r.pan.y),this.emit("zoom"+(r.panned?" pan":"")+" viewport"),this.notify("viewport"),this)},viewport:function(e){var r=this._private,a=!0,n=!0,i=[],s=!1,o=!1;if(!e)return this;if(ie(e.zoom)||(a=!1),Ce(e.pan)||(n=!1),!a&&!n)return this;if(a){var u=e.zoom;u<r.minZoom||u>r.maxZoom||!r.zoomingEnabled?s=!0:(r.zoom=u,i.push("zoom"))}if(n&&(!s||!e.cancelOnFailedZoom)&&r.panningEnabled){var l=e.pan;ie(l.x)&&(r.pan.x=l.x,o=!1),ie(l.y)&&(r.pan.y=l.y,o=!1),o||i.push("pan")}return i.length>0&&(i.push("viewport"),this.emit(i.join(" ")),this.notify("viewport")),this},center:function(e){var r=this.getCenterPan(e);return r&&(this._private.pan=r,this.emit("pan viewport"),this.notify("viewport")),this},getCenterPan:function(e,r){if(this._private.panningEnabled){if(de(e)){var a=e;e=this.mutableElements().filter(a)}else pt(e)||(e=this.mutableElements());if(e.length!==0){var n=e.boundingBox(),i=this.width(),s=this.height();r=r===void 0?this._private.zoom:r;var o={x:(i-r*(n.x1+n.x2))/2,y:(s-r*(n.y1+n.y2))/2};return o}}},reset:function(){return!this._private.panningEnabled||!this._private.zoomingEnabled?this:(this.viewport({pan:{x:0,y:0},zoom:1}),this)},invalidateSize:function(){this._private.sizeCache=null},size:function(){var e=this._private,r=e.container,a=this;return e.sizeCache=e.sizeCache||(r?function(){var n=a.window().getComputedStyle(r),i=function(o){return parseFloat(n.getPropertyValue(o))};return{width:r.clientWidth-i("padding-left")-i("padding-right"),height:r.clientHeight-i("padding-top")-i("padding-bottom")}}():{width:1,height:1})},width:function(){return this.size().width},height:function(){return this.size().height},extent:function(){var e=this._private.pan,r=this._private.zoom,a=this.renderedExtent(),n={x1:(a.x1-e.x)/r,x2:(a.x2-e.x)/r,y1:(a.y1-e.y)/r,y2:(a.y2-e.y)/r};return n.w=n.x2-n.x1,n.h=n.y2-n.y1,n},renderedExtent:function(){var e=this.width(),r=this.height();return{x1:0,y1:0,x2:e,y2:r,w:e,h:r}},multiClickDebounceTime:function(e){if(e)this._private.multiClickDebounceTime=e;else return this._private.multiClickDebounceTime;return this}};mr.centre=mr.center;mr.autolockNodes=mr.autolock;mr.autoungrabifyNodes=mr.autoungrabify;var Ea={data:Oe.data({field:"data",bindingEvent:"data",allowBinding:!0,allowSetting:!0,settingEvent:"data",settingTriggersEvent:!0,triggerFnName:"trigger",allowGetting:!0,updateStyle:!0}),removeData:Oe.removeData({field:"data",event:"data",triggerFnName:"trigger",triggerEvent:!0,updateStyle:!0}),scratch:Oe.data({field:"scratch",bindingEvent:"scratch",allowBinding:!0,allowSetting:!0,settingEvent:"scratch",settingTriggersEvent:!0,triggerFnName:"trigger",allowGetting:!0,updateStyle:!0}),removeScratch:Oe.removeData({field:"scratch",event:"scratch",triggerFnName:"trigger",triggerEvent:!0,updateStyle:!0})};Ea.attr=Ea.data;Ea.removeAttr=Ea.removeData;var wa=function(e){var r=this;e=be({},e);var a=e.container;a&&!tn(a)&&tn(a[0])&&(a=a[0]);var n=a?a._cyreg:null;n=n||{},n&&n.cy&&(n.cy.destroy(),n={});var i=n.readies=n.readies||[];a&&(a._cyreg=n),n.cy=r;var s=Ye!==void 0&&a!==void 0&&!e.headless,o=e;o.layout=be({name:s?"grid":"null"},o.layout),o.renderer=be({name:s?"canvas":"null"},o.renderer);var u=function(v,p,g){return p!==void 0?p:g!==void 0?g:v},l=this._private={container:a,ready:!1,options:o,elements:new et(this),listeners:[],aniEles:new et(this),data:o.data||{},scratch:{},layout:null,renderer:null,destroyed:!1,notificationsEnabled:!0,minZoom:1e-50,maxZoom:1e50,zoomingEnabled:u(!0,o.zoomingEnabled),userZoomingEnabled:u(!0,o.userZoomingEnabled),panningEnabled:u(!0,o.panningEnabled),userPanningEnabled:u(!0,o.userPanningEnabled),boxSelectionEnabled:u(!0,o.boxSelectionEnabled),autolock:u(!1,o.autolock,o.autolockNodes),autoungrabify:u(!1,o.autoungrabify,o.autoungrabifyNodes),autounselectify:u(!1,o.autounselectify),styleEnabled:o.styleEnabled===void 0?s:o.styleEnabled,zoom:ie(o.zoom)?o.zoom:1,pan:{x:Ce(o.pan)&&ie(o.pan.x)?o.pan.x:0,y:Ce(o.pan)&&ie(o.pan.y)?o.pan.y:0},animation:{current:[],queue:[]},hasCompoundNodes:!1,multiClickDebounceTime:u(250,o.multiClickDebounceTime)};this.createEmitter(),this.selectionType(o.selectionType),this.zoomRange({min:o.minZoom,max:o.maxZoom});var f=function(v,p){var g=v.some(Tl);if(g)return $r.all(v).then(p);p(v)};l.styleEnabled&&r.setStyle([]);var h=be({},o,o.renderer);r.initRenderer(h);var d=function(v,p,g){r.notifications(!1);var y=r.mutableElements();y.length>0&&y.remove(),v!=null&&(Ce(v)||Re(v))&&r.add(v),r.one("layoutready",function(m){r.notifications(!0),r.emit(m),r.one("load",p),r.emitAndNotify("load")}).one("layoutstop",function(){r.one("done",g),r.emit("done")});var b=be({},r._private.options.layout);b.eles=r.elements(),r.layout(b).run()};f([o.style,o.elements],function(c){var v=c[0],p=c[1];l.styleEnabled&&r.style().append(v),d(p,function(){r.startAnimationLoop(),l.ready=!0,Ge(o.ready)&&r.on("ready",o.ready);for(var g=0;g<i.length;g++){var y=i[g];r.on("ready",y)}n&&(n.readies=[]),r.emit("ready")},o.done)})},ln=wa.prototype;be(ln,{instanceString:function(){return"core"},isReady:function(){return this._private.ready},destroyed:function(){return this._private.destroyed},ready:function(e){return this.isReady()?this.emitter().emit("ready",[],e):this.on("ready",e),this},destroy:function(){var e=this;if(!e.destroyed())return e.stopAnimationLoop(),e.destroyRenderer(),this.emit("destroy"),e._private.destroyed=!0,e},hasElementWithId:function(e){return this._private.elements.hasElementWithId(e)},getElementById:function(e){return this._private.elements.getElementById(e)},hasCompoundNodes:function(){return this._private.hasCompoundNodes},headless:function(){return this._private.renderer.isHeadless()},styleEnabled:function(){return this._private.styleEnabled},addToPool:function(e){return this._private.elements.merge(e),this},removeFromPool:function(e){return this._private.elements.unmerge(e),this},container:function(){return this._private.container||null},window:function(){var e=this._private.container;if(e==null)return Ye;var r=this._private.container.ownerDocument;return r===void 0||r==null?Ye:r.defaultView||Ye},mount:function(e){if(e!=null){var r=this,a=r._private,n=a.options;return!tn(e)&&tn(e[0])&&(e=e[0]),r.stopAnimationLoop(),r.destroyRenderer(),a.container=e,a.styleEnabled=!0,r.invalidateSize(),r.initRenderer(be({},n,n.renderer,{name:n.renderer.name==="null"?"canvas":n.renderer.name})),r.startAnimationLoop(),r.style(n.style),r.emit("mount"),r}},unmount:function(){var e=this;return e.stopAnimationLoop(),e.destroyRenderer(),e.initRenderer({name:"null"}),e.emit("unmount"),e},options:function(){return Pt(this._private.options)},json:function(e){var r=this,a=r._private,n=r.mutableElements(),i=function(T){return r.getElementById(T.id())};if(Ce(e)){if(r.startBatch(),e.elements){var s={},o=function(T,C){for(var S=[],E=[],w=0;w<T.length;w++){var x=T[w];if(!x.data.id){Ne("cy.json() cannot handle elements without an ID attribute");continue}var D=""+x.data.id,L=r.getElementById(D);s[D]=!0,L.length!==0?E.push({ele:L,json:x}):(C&&(x.group=C),S.push(x))}r.add(S);for(var A=0;A<E.length;A++){var N=E[A],O=N.ele,M=N.json;O.json(M)}};if(Re(e.elements))o(e.elements);else for(var u=["nodes","edges"],l=0;l<u.length;l++){var f=u[l],h=e.elements[f];Re(h)&&o(h,f)}var d=r.collection();n.filter(function(m){return!s[m.id()]}).forEach(function(m){m.isParent()?d.merge(m):m.remove()}),d.forEach(function(m){return m.children().move({parent:null})}),d.forEach(function(m){return i(m).remove()})}e.style&&r.style(e.style),e.zoom!=null&&e.zoom!==a.zoom&&r.zoom(e.zoom),e.pan&&(e.pan.x!==a.pan.x||e.pan.y!==a.pan.y)&&r.pan(e.pan),e.data&&r.data(e.data);for(var c=["minZoom","maxZoom","zoomingEnabled","userZoomingEnabled","panningEnabled","userPanningEnabled","boxSelectionEnabled","autolock","autoungrabify","autounselectify","multiClickDebounceTime"],v=0;v<c.length;v++){var p=c[v];e[p]!=null&&r[p](e[p])}return r.endBatch(),this}else{var g=!!e,y={};g?y.elements=this.elements().map(function(m){return m.json()}):(y.elements={},n.forEach(function(m){var T=m.group();y.elements[T]||(y.elements[T]=[]),y.elements[T].push(m.json())})),this._private.styleEnabled&&(y.style=r.style().json()),y.data=Pt(r.data());var b=a.options;return y.zoomingEnabled=a.zoomingEnabled,y.userZoomingEnabled=a.userZoomingEnabled,y.zoom=a.zoom,y.minZoom=a.minZoom,y.maxZoom=a.maxZoom,y.panningEnabled=a.panningEnabled,y.userPanningEnabled=a.userPanningEnabled,y.pan=Pt(a.pan),y.boxSelectionEnabled=a.boxSelectionEnabled,y.renderer=Pt(b.renderer),y.hideEdgesOnViewport=b.hideEdgesOnViewport,y.textureOnViewport=b.textureOnViewport,y.wheelSensitivity=b.wheelSensitivity,y.motionBlur=b.motionBlur,y.multiClickDebounceTime=b.multiClickDebounceTime,y}}});ln.$id=ln.getElementById;[fg,gg,fu,ei,Ka,yg,ti,Za,Eg,mr,Ea].forEach(function(t){be(ln,t)});var xg={fit:!0,directed:!1,padding:30,circle:!1,grid:!1,spacingFactor:1.75,boundingBox:void 0,avoidOverlap:!0,nodeDimensionsIncludeLabels:!1,roots:void 0,depthSort:void 0,animate:!1,animationDuration:500,animationEasing:void 0,animateFilter:function(e,r){return!0},ready:void 0,stop:void 0,transform:function(e,r){return r}},Tg={maximal:!1,acyclic:!1},Lr=function(e){return e.scratch("breadthfirst")},Ps=function(e,r){return e.scratch("breadthfirst",r)};function hu(t){this.options=be({},xg,Tg,t)}hu.prototype.run=function(){var t=this.options,e=t,r=t.cy,a=e.eles,n=a.nodes().filter(function(te){return!te.isParent()}),i=a,s=e.directed,o=e.acyclic||e.maximal||e.maximalAdjustments>0,u=gt(e.boundingBox?e.boundingBox:{x1:0,y1:0,w:r.width(),h:r.height()}),l;if(pt(e.roots))l=e.roots;else if(Re(e.roots)){for(var f=[],h=0;h<e.roots.length;h++){var d=e.roots[h],c=r.getElementById(d);f.push(c)}l=r.collection(f)}else if(de(e.roots))l=r.$(e.roots);else if(s)l=n.roots();else{var v=a.components();l=r.collection();for(var p=function(se){var ue=v[se],ve=ue.maxDegree(!1),fe=ue.filter(function(pe){return pe.degree(!1)===ve});l=l.add(fe)},g=0;g<v.length;g++)p(g)}var y=[],b={},m=function(se,ue){y[ue]==null&&(y[ue]=[]);var ve=y[ue].length;y[ue].push(se),Ps(se,{index:ve,depth:ue})},T=function(se,ue){var ve=Lr(se),fe=ve.depth,pe=ve.index;y[fe][pe]=null,m(se,ue)};i.bfs({roots:l,directed:e.directed,visit:function(se,ue,ve,fe,pe){var Ae=se[0],xe=Ae.id();m(Ae,pe),b[xe]=!0}});for(var C=[],S=0;S<n.length;S++){var E=n[S];b[E.id()]||C.push(E)}var w=function(se){for(var ue=y[se],ve=0;ve<ue.length;ve++){var fe=ue[ve];if(fe==null){ue.splice(ve,1),ve--;continue}Ps(fe,{depth:se,index:ve})}},x=function(){for(var se=0;se<y.length;se++)w(se)},D=function(se,ue){for(var ve=Lr(se),fe=se.incomers().filter(function(I){return I.isNode()&&a.has(I)}),pe=-1,Ae=se.id(),xe=0;xe<fe.length;xe++){var we=fe[xe],De=Lr(we);pe=Math.max(pe,De.depth)}if(ve.depth<=pe){if(!e.acyclic&&ue[Ae])return null;var ee=pe+1;return T(se,ee),ue[Ae]=ee,!0}return!1};if(s&&o){var L=[],A={},N=function(se){return L.push(se)},O=function(){return L.shift()};for(n.forEach(function(te){return L.push(te)});L.length>0;){var M=O(),R=D(M,A);if(R)M.outgoers().filter(function(te){return te.isNode()&&a.has(te)}).forEach(N);else if(R===null){Ne("Detected double maximal shift for node `"+M.id()+"`.  Bailing maximal adjustment due to cycle.  Use `options.maximal: true` only on DAGs.");break}}}x();var k=0;if(e.avoidOverlap)for(var P=0;P<n.length;P++){var B=n[P],z=B.layoutDimensions(e),G=z.w,F=z.h;k=Math.max(k,G,F)}var U={},Y=function(se){if(U[se.id()])return U[se.id()];for(var ue=Lr(se).depth,ve=se.neighborhood(),fe=0,pe=0,Ae=0;Ae<ve.length;Ae++){var xe=ve[Ae];if(!(xe.isEdge()||xe.isParent()||!n.has(xe))){var we=Lr(xe);if(we!=null){var De=we.index,ee=we.depth;if(!(De==null||ee==null)){var I=y[ee].length;ee<ue&&(fe+=De/I,pe++)}}}}return pe=Math.max(1,pe),fe=fe/pe,pe===0&&(fe=0),U[se.id()]=fe,fe},W=function(se,ue){var ve=Y(se),fe=Y(ue),pe=ve-fe;return pe===0?fo(se.id(),ue.id()):pe};e.depthSort!==void 0&&(W=e.depthSort);for(var K=0;K<y.length;K++)y[K].sort(W),w(K);for(var j=[],_=0;_<C.length;_++)j.push(C[_]);y.unshift(j),x();for(var V=0,H=0;H<y.length;H++)V=Math.max(y[H].length,V);var Q={x:u.x1+u.w/2,y:u.x1+u.h/2},ne=y.reduce(function(te,se){return Math.max(te,se.length)},0),ce=function(se){var ue=Lr(se),ve=ue.depth,fe=ue.index,pe=y[ve].length,Ae=Math.max(u.w/((e.grid?ne:pe)+1),k),xe=Math.max(u.h/(y.length+1),k),we=Math.min(u.w/2/y.length,u.h/2/y.length);if(we=Math.max(we,k),e.circle){var ee=we*ve+we-(y.length>0&&y[0].length<=3?we/2:0),I=2*Math.PI/y[ve].length*fe;return ve===0&&y[0].length===1&&(ee=1),{x:Q.x+ee*Math.cos(I),y:Q.y+ee*Math.sin(I)}}else{var De={x:Q.x+(fe+1-(pe+1)/2)*Ae,y:(ve+1)*xe};return De}};return a.nodes().layoutPositions(this,e,ce),this};var Cg={fit:!0,padding:30,boundingBox:void 0,avoidOverlap:!0,nodeDimensionsIncludeLabels:!1,spacingFactor:void 0,radius:void 0,startAngle:3/2*Math.PI,sweep:void 0,clockwise:!0,sort:void 0,animate:!1,animationDuration:500,animationEasing:void 0,animateFilter:function(e,r){return!0},ready:void 0,stop:void 0,transform:function(e,r){return r}};function cu(t){this.options=be({},Cg,t)}cu.prototype.run=function(){var t=this.options,e=t,r=t.cy,a=e.eles,n=e.counterclockwise!==void 0?!e.counterclockwise:e.clockwise,i=a.nodes().not(":parent");e.sort&&(i=i.sort(e.sort));for(var s=gt(e.boundingBox?e.boundingBox:{x1:0,y1:0,w:r.width(),h:r.height()}),o={x:s.x1+s.w/2,y:s.y1+s.h/2},u=e.sweep===void 0?2*Math.PI-2*Math.PI/i.length:e.sweep,l=u/Math.max(1,i.length-1),f,h=0,d=0;d<i.length;d++){var c=i[d],v=c.layoutDimensions(e),p=v.w,g=v.h;h=Math.max(h,p,g)}if(ie(e.radius)?f=e.radius:i.length<=1?f=0:f=Math.min(s.h,s.w)/2-h,i.length>1&&e.avoidOverlap){h*=1.75;var y=Math.cos(l)-Math.cos(0),b=Math.sin(l)-Math.sin(0),m=Math.sqrt(h*h/(y*y+b*b));f=Math.max(m,f)}var T=function(S,E){var w=e.startAngle+E*l*(n?1:-1),x=f*Math.cos(w),D=f*Math.sin(w),L={x:o.x+x,y:o.y+D};return L};return a.nodes().layoutPositions(this,e,T),this};var Dg={fit:!0,padding:30,startAngle:3/2*Math.PI,sweep:void 0,clockwise:!0,equidistant:!1,minNodeSpacing:10,boundingBox:void 0,avoidOverlap:!0,nodeDimensionsIncludeLabels:!1,height:void 0,width:void 0,spacingFactor:void 0,concentric:function(e){return e.degree()},levelWidth:function(e){return e.maxDegree()/4},animate:!1,animationDuration:500,animationEasing:void 0,animateFilter:function(e,r){return!0},ready:void 0,stop:void 0,transform:function(e,r){return r}};function vu(t){this.options=be({},Dg,t)}vu.prototype.run=function(){for(var t=this.options,e=t,r=e.counterclockwise!==void 0?!e.counterclockwise:e.clockwise,a=t.cy,n=e.eles,i=n.nodes().not(":parent"),s=gt(e.boundingBox?e.boundingBox:{x1:0,y1:0,w:a.width(),h:a.height()}),o={x:s.x1+s.w/2,y:s.y1+s.h/2},u=[],l=0,f=0;f<i.length;f++){var h=i[f],d=void 0;d=e.concentric(h),u.push({value:d,node:h}),h._private.scratch.concentric=d}i.updateStyle();for(var c=0;c<i.length;c++){var v=i[c],p=v.layoutDimensions(e);l=Math.max(l,p.w,p.h)}u.sort(function(te,se){return se.value-te.value});for(var g=e.levelWidth(i),y=[[]],b=y[0],m=0;m<u.length;m++){var T=u[m];if(b.length>0){var C=Math.abs(b[0].value-T.value);C>=g&&(b=[],y.push(b))}b.push(T)}var S=l+e.minNodeSpacing;if(!e.avoidOverlap){var E=y.length>0&&y[0].length>1,w=Math.min(s.w,s.h)/2-S,x=w/(y.length+E?1:0);S=Math.min(S,x)}for(var D=0,L=0;L<y.length;L++){var A=y[L],N=e.sweep===void 0?2*Math.PI-2*Math.PI/A.length:e.sweep,O=A.dTheta=N/Math.max(1,A.length-1);if(A.length>1&&e.avoidOverlap){var M=Math.cos(O)-Math.cos(0),R=Math.sin(O)-Math.sin(0),k=Math.sqrt(S*S/(M*M+R*R));D=Math.max(k,D)}A.r=D,D+=S}if(e.equidistant){for(var P=0,B=0,z=0;z<y.length;z++){var G=y[z],F=G.r-B;P=Math.max(P,F)}B=0;for(var U=0;U<y.length;U++){var Y=y[U];U===0&&(B=Y.r),Y.r=B,B+=P}}for(var W={},K=0;K<y.length;K++)for(var j=y[K],_=j.dTheta,V=j.r,H=0;H<j.length;H++){var Q=j[H],ne=e.startAngle+(r?1:-1)*_*H,ce={x:o.x+V*Math.cos(ne),y:o.y+V*Math.sin(ne)};W[Q.node.id()]=ce}return n.nodes().layoutPositions(this,e,function(te){var se=te.id();return W[se]}),this};var $n,Sg={ready:function(){},stop:function(){},animate:!0,animationEasing:void 0,animationDuration:void 0,animateFilter:function(e,r){return!0},animationThreshold:250,refresh:20,fit:!0,padding:30,boundingBox:void 0,nodeDimensionsIncludeLabels:!1,randomize:!1,componentSpacing:40,nodeRepulsion:function(e){return 2048},nodeOverlap:4,idealEdgeLength:function(e){return 32},edgeElasticity:function(e){return 32},nestingFactor:1.2,gravity:1,numIter:1e3,initialTemp:1e3,coolingFactor:.99,minTemp:1};function An(t){this.options=be({},Sg,t),this.options.layout=this;var e=this.options.eles.nodes(),r=this.options.eles.edges(),a=r.filter(function(n){var i=n.source().data("id"),s=n.target().data("id"),o=e.some(function(l){return l.data("id")===i}),u=e.some(function(l){return l.data("id")===s});return!o||!u});this.options.eles=this.options.eles.not(a)}An.prototype.run=function(){var t=this.options,e=t.cy,r=this;r.stopped=!1,(t.animate===!0||t.animate===!1)&&r.emit({type:"layoutstart",layout:r}),t.debug===!0?$n=!0:$n=!1;var a=Lg(e,r,t);$n&&Ng(a),t.randomize&&Ig(a);var n=$t(),i=function(){Mg(a,e,t),t.fit===!0&&e.fit(t.padding)},s=function(d){return!(r.stopped||d>=t.numIter||(Rg(a,t),a.temperature=a.temperature*t.coolingFactor,a.temperature<t.minTemp))},o=function(){if(t.animate===!0||t.animate===!1)i(),r.one("layoutstop",t.stop),r.emit({type:"layoutstop",layout:r});else{var d=t.eles.nodes(),c=du(a,t,d);d.layoutPositions(r,t,c)}},u=0,l=!0;if(t.animate===!0){var f=function h(){for(var d=0;l&&d<t.refresh;)l=s(u),u++,d++;if(!l)Fs(a,t),o();else{var c=$t();c-n>=t.animationThreshold&&i(),rn(h)}};f()}else{for(;l;)l=s(u),u++;Fs(a,t),o()}return this};An.prototype.stop=function(){return this.stopped=!0,this.thread&&this.thread.stop(),this.emit("layoutstop"),this};An.prototype.destroy=function(){return this.thread&&this.thread.stop(),this};var Lg=function(e,r,a){for(var n=a.eles.edges(),i=a.eles.nodes(),s=gt(a.boundingBox?a.boundingBox:{x1:0,y1:0,w:e.width(),h:e.height()}),o={isCompound:e.hasCompoundNodes(),layoutNodes:[],idToIndex:{},nodeSize:i.size(),graphSet:[],indexToGraph:[],layoutEdges:[],edgeSize:n.size(),temperature:a.initialTemp,clientWidth:s.w,clientHeight:s.h,boundingBox:s},u=a.eles.components(),l={},f=0;f<u.length;f++)for(var h=u[f],d=0;d<h.length;d++){var c=h[d];l[c.id()]=f}for(var f=0;f<o.nodeSize;f++){var v=i[f],p=v.layoutDimensions(a),g={};g.isLocked=v.locked(),g.id=v.data("id"),g.parentId=v.data("parent"),g.cmptId=l[v.id()],g.children=[],g.positionX=v.position("x"),g.positionY=v.position("y"),g.offsetX=0,g.offsetY=0,g.height=p.w,g.width=p.h,g.maxX=g.positionX+g.width/2,g.minX=g.positionX-g.width/2,g.maxY=g.positionY+g.height/2,g.minY=g.positionY-g.height/2,g.padLeft=parseFloat(v.style("padding")),g.padRight=parseFloat(v.style("padding")),g.padTop=parseFloat(v.style("padding")),g.padBottom=parseFloat(v.style("padding")),g.nodeRepulsion=Ge(a.nodeRepulsion)?a.nodeRepulsion(v):a.nodeRepulsion,o.layoutNodes.push(g),o.idToIndex[g.id]=f}for(var y=[],b=0,m=-1,T=[],f=0;f<o.nodeSize;f++){var v=o.layoutNodes[f],C=v.parentId;C!=null?o.layoutNodes[o.idToIndex[C]].children.push(v.id):(y[++m]=v.id,T.push(v.id))}for(o.graphSet.push(T);b<=m;){var S=y[b++],E=o.idToIndex[S],c=o.layoutNodes[E],w=c.children;if(w.length>0){o.graphSet.push(w);for(var f=0;f<w.length;f++)y[++m]=w[f]}}for(var f=0;f<o.graphSet.length;f++)for(var x=o.graphSet[f],d=0;d<x.length;d++){var D=o.idToIndex[x[d]];o.indexToGraph[D]=f}for(var f=0;f<o.edgeSize;f++){var L=n[f],A={};A.id=L.data("id"),A.sourceId=L.data("source"),A.targetId=L.data("target");var N=Ge(a.idealEdgeLength)?a.idealEdgeLength(L):a.idealEdgeLength,O=Ge(a.edgeElasticity)?a.edgeElasticity(L):a.edgeElasticity,M=o.idToIndex[A.sourceId],R=o.idToIndex[A.targetId],k=o.indexToGraph[M],P=o.indexToGraph[R];if(k!=P){for(var B=Ag(A.sourceId,A.targetId,o),z=o.graphSet[B],G=0,g=o.layoutNodes[M];z.indexOf(g.id)===-1;)g=o.layoutNodes[o.idToIndex[g.parentId]],G++;for(g=o.layoutNodes[R];z.indexOf(g.id)===-1;)g=o.layoutNodes[o.idToIndex[g.parentId]],G++;N*=G*a.nestingFactor}A.idealLength=N,A.elasticity=O,o.layoutEdges.push(A)}return o},Ag=function(e,r,a){var n=Og(e,r,0,a);return 2>n.count?0:n.graph},Og=function t(e,r,a,n){var i=n.graphSet[a];if(-1<i.indexOf(e)&&-1<i.indexOf(r))return{count:2,graph:a};for(var s=0,o=0;o<i.length;o++){var u=i[o],l=n.idToIndex[u],f=n.layoutNodes[l].children;if(f.length!==0){var h=n.indexToGraph[n.idToIndex[f[0]]],d=t(e,r,h,n);if(d.count!==0)if(d.count===1){if(s++,s===2)break}else return d}}return{count:s,graph:a}},Ng,Ig=function(e,r){for(var a=e.clientWidth,n=e.clientHeight,i=0;i<e.nodeSize;i++){var s=e.layoutNodes[i];s.children.length===0&&!s.isLocked&&(s.positionX=Math.random()*a,s.positionY=Math.random()*n)}},du=function(e,r,a){var n=e.boundingBox,i={x1:1/0,x2:-1/0,y1:1/0,y2:-1/0};return r.boundingBox&&(a.forEach(function(s){var o=e.layoutNodes[e.idToIndex[s.data("id")]];i.x1=Math.min(i.x1,o.positionX),i.x2=Math.max(i.x2,o.positionX),i.y1=Math.min(i.y1,o.positionY),i.y2=Math.max(i.y2,o.positionY)}),i.w=i.x2-i.x1,i.h=i.y2-i.y1),function(s,o){var u=e.layoutNodes[e.idToIndex[s.data("id")]];if(r.boundingBox){var l=(u.positionX-i.x1)/i.w,f=(u.positionY-i.y1)/i.h;return{x:n.x1+l*n.w,y:n.y1+f*n.h}}else return{x:u.positionX,y:u.positionY}}},Mg=function(e,r,a){var n=a.layout,i=a.eles.nodes(),s=du(e,a,i);i.positions(s),e.ready!==!0&&(e.ready=!0,n.one("layoutready",a.ready),n.emit({type:"layoutready",layout:this}))},Rg=function(e,r,a){kg(e,r),Fg(e),Gg(e,r),zg(e),Vg(e)},kg=function(e,r){for(var a=0;a<e.graphSet.length;a++)for(var n=e.graphSet[a],i=n.length,s=0;s<i;s++)for(var o=e.layoutNodes[e.idToIndex[n[s]]],u=s+1;u<i;u++){var l=e.layoutNodes[e.idToIndex[n[u]]];Pg(o,l,e,r)}},Bs=function(e){return-e+2*e*Math.random()},Pg=function(e,r,a,n){var i=e.cmptId,s=r.cmptId;if(!(i!==s&&!a.isCompound)){var o=r.positionX-e.positionX,u=r.positionY-e.positionY,l=1;o===0&&u===0&&(o=Bs(l),u=Bs(l));var f=Bg(e,r,o,u);if(f>0)var h=n.nodeOverlap*f,d=Math.sqrt(o*o+u*u),c=h*o/d,v=h*u/d;else var p=fn(e,o,u),g=fn(r,-1*o,-1*u),y=g.x-p.x,b=g.y-p.y,m=y*y+b*b,d=Math.sqrt(m),h=(e.nodeRepulsion+r.nodeRepulsion)/m,c=h*y/d,v=h*b/d;e.isLocked||(e.offsetX-=c,e.offsetY-=v),r.isLocked||(r.offsetX+=c,r.offsetY+=v)}},Bg=function(e,r,a,n){if(a>0)var i=e.maxX-r.minX;else var i=r.maxX-e.minX;if(n>0)var s=e.maxY-r.minY;else var s=r.maxY-e.minY;return i>=0&&s>=0?Math.sqrt(i*i+s*s):0},fn=function(e,r,a){var n=e.positionX,i=e.positionY,s=e.height||1,o=e.width||1,u=a/r,l=s/o,f={};return r===0&&0<a||r===0&&0>a?(f.x=n,f.y=i+s/2,f):0<r&&-1*l<=u&&u<=l?(f.x=n+o/2,f.y=i+o*a/2/r,f):0>r&&-1*l<=u&&u<=l?(f.x=n-o/2,f.y=i-o*a/2/r,f):0<a&&(u<=-1*l||u>=l)?(f.x=n+s*r/2/a,f.y=i+s/2,f):(0>a&&(u<=-1*l||u>=l)&&(f.x=n-s*r/2/a,f.y=i-s/2),f)},Fg=function(e,r){for(var a=0;a<e.edgeSize;a++){var n=e.layoutEdges[a],i=e.idToIndex[n.sourceId],s=e.layoutNodes[i],o=e.idToIndex[n.targetId],u=e.layoutNodes[o],l=u.positionX-s.positionX,f=u.positionY-s.positionY;if(!(l===0&&f===0)){var h=fn(s,l,f),d=fn(u,-1*l,-1*f),c=d.x-h.x,v=d.y-h.y,p=Math.sqrt(c*c+v*v),g=Math.pow(n.idealLength-p,2)/n.elasticity;if(p!==0)var y=g*c/p,b=g*v/p;else var y=0,b=0;s.isLocked||(s.offsetX+=y,s.offsetY+=b),u.isLocked||(u.offsetX-=y,u.offsetY-=b)}}},Gg=function(e,r){if(r.gravity!==0)for(var a=1,n=0;n<e.graphSet.length;n++){var i=e.graphSet[n],s=i.length;if(n===0)var o=e.clientHeight/2,u=e.clientWidth/2;else var l=e.layoutNodes[e.idToIndex[i[0]]],f=e.layoutNodes[e.idToIndex[l.parentId]],o=f.positionX,u=f.positionY;for(var h=0;h<s;h++){var d=e.layoutNodes[e.idToIndex[i[h]]];if(!d.isLocked){var c=o-d.positionX,v=u-d.positionY,p=Math.sqrt(c*c+v*v);if(p>a){var g=r.gravity*c/p,y=r.gravity*v/p;d.offsetX+=g,d.offsetY+=y}}}}},zg=function(e,r){var a=[],n=0,i=-1;for(a.push.apply(a,e.graphSet[0]),i+=e.graphSet[0].length;n<=i;){var s=a[n++],o=e.idToIndex[s],u=e.layoutNodes[o],l=u.children;if(0<l.length&&!u.isLocked){for(var f=u.offsetX,h=u.offsetY,d=0;d<l.length;d++){var c=e.layoutNodes[e.idToIndex[l[d]]];c.offsetX+=f,c.offsetY+=h,a[++i]=l[d]}u.offsetX=0,u.offsetY=0}}},Vg=function(e,r){for(var a=0;a<e.nodeSize;a++){var n=e.layoutNodes[a];0<n.children.length&&(n.maxX=void 0,n.minX=void 0,n.maxY=void 0,n.minY=void 0)}for(var a=0;a<e.nodeSize;a++){var n=e.layoutNodes[a];if(!(0<n.children.length||n.isLocked)){var i=Ug(n.offsetX,n.offsetY,e.temperature);n.positionX+=i.x,n.positionY+=i.y,n.offsetX=0,n.offsetY=0,n.minX=n.positionX-n.width,n.maxX=n.positionX+n.width,n.minY=n.positionY-n.height,n.maxY=n.positionY+n.height,$g(n,e)}}for(var a=0;a<e.nodeSize;a++){var n=e.layoutNodes[a];0<n.children.length&&!n.isLocked&&(n.positionX=(n.maxX+n.minX)/2,n.positionY=(n.maxY+n.minY)/2,n.width=n.maxX-n.minX,n.height=n.maxY-n.minY)}},Ug=function(e,r,a){var n=Math.sqrt(e*e+r*r);if(n>a)var i={x:a*e/n,y:a*r/n};else var i={x:e,y:r};return i},$g=function t(e,r){var a=e.parentId;if(a!=null){var n=r.layoutNodes[r.idToIndex[a]],i=!1;if((n.maxX==null||e.maxX+n.padRight>n.maxX)&&(n.maxX=e.maxX+n.padRight,i=!0),(n.minX==null||e.minX-n.padLeft<n.minX)&&(n.minX=e.minX-n.padLeft,i=!0),(n.maxY==null||e.maxY+n.padBottom>n.maxY)&&(n.maxY=e.maxY+n.padBottom,i=!0),(n.minY==null||e.minY-n.padTop<n.minY)&&(n.minY=e.minY-n.padTop,i=!0),i)return t(n,r)}},Fs=function(e,r){for(var a=e.layoutNodes,n=[],i=0;i<a.length;i++){var s=a[i],o=s.cmptId,u=n[o]=n[o]||[];u.push(s)}for(var l=0,i=0;i<n.length;i++){var f=n[i];if(f){f.x1=1/0,f.x2=-1/0,f.y1=1/0,f.y2=-1/0;for(var h=0;h<f.length;h++){var d=f[h];f.x1=Math.min(f.x1,d.positionX-d.width/2),f.x2=Math.max(f.x2,d.positionX+d.width/2),f.y1=Math.min(f.y1,d.positionY-d.height/2),f.y2=Math.max(f.y2,d.positionY+d.height/2)}f.w=f.x2-f.x1,f.h=f.y2-f.y1,l+=f.w*f.h}}n.sort(function(b,m){return m.w*m.h-b.w*b.h});for(var c=0,v=0,p=0,g=0,y=Math.sqrt(l)*e.clientWidth/e.clientHeight,i=0;i<n.length;i++){var f=n[i];if(f){for(var h=0;h<f.length;h++){var d=f[h];d.isLocked||(d.positionX+=c-f.x1,d.positionY+=v-f.y1)}c+=f.w+r.componentSpacing,p+=f.w+r.componentSpacing,g=Math.max(g,f.h),p>y&&(v+=g+r.componentSpacing,c=0,p=0,g=0)}}},Yg={fit:!0,padding:30,boundingBox:void 0,avoidOverlap:!0,avoidOverlapPadding:10,nodeDimensionsIncludeLabels:!1,spacingFactor:void 0,condense:!1,rows:void 0,cols:void 0,position:function(e){},sort:void 0,animate:!1,animationDuration:500,animationEasing:void 0,animateFilter:function(e,r){return!0},ready:void 0,stop:void 0,transform:function(e,r){return r}};function gu(t){this.options=be({},Yg,t)}gu.prototype.run=function(){var t=this.options,e=t,r=t.cy,a=e.eles,n=a.nodes().not(":parent");e.sort&&(n=n.sort(e.sort));var i=gt(e.boundingBox?e.boundingBox:{x1:0,y1:0,w:r.width(),h:r.height()});if(i.h===0||i.w===0)a.nodes().layoutPositions(this,e,function(U){return{x:i.x1,y:i.y1}});else{var s=n.size(),o=Math.sqrt(s*i.h/i.w),u=Math.round(o),l=Math.round(i.w/i.h*o),f=function(Y){if(Y==null)return Math.min(u,l);var W=Math.min(u,l);W==u?u=Y:l=Y},h=function(Y){if(Y==null)return Math.max(u,l);var W=Math.max(u,l);W==u?u=Y:l=Y},d=e.rows,c=e.cols!=null?e.cols:e.columns;if(d!=null&&c!=null)u=d,l=c;else if(d!=null&&c==null)u=d,l=Math.ceil(s/u);else if(d==null&&c!=null)l=c,u=Math.ceil(s/l);else if(l*u>s){var v=f(),p=h();(v-1)*p>=s?f(v-1):(p-1)*v>=s&&h(p-1)}else for(;l*u<s;){var g=f(),y=h();(y+1)*g>=s?h(y+1):f(g+1)}var b=i.w/l,m=i.h/u;if(e.condense&&(b=0,m=0),e.avoidOverlap)for(var T=0;T<n.length;T++){var C=n[T],S=C._private.position;(S.x==null||S.y==null)&&(S.x=0,S.y=0);var E=C.layoutDimensions(e),w=e.avoidOverlapPadding,x=E.w+w,D=E.h+w;b=Math.max(b,x),m=Math.max(m,D)}for(var L={},A=function(Y,W){return!!L["c-"+Y+"-"+W]},N=function(Y,W){L["c-"+Y+"-"+W]=!0},O=0,M=0,R=function(){M++,M>=l&&(M=0,O++)},k={},P=0;P<n.length;P++){var B=n[P],z=e.position(B);if(z&&(z.row!==void 0||z.col!==void 0)){var G={row:z.row,col:z.col};if(G.col===void 0)for(G.col=0;A(G.row,G.col);)G.col++;else if(G.row===void 0)for(G.row=0;A(G.row,G.col);)G.row++;k[B.id()]=G,N(G.row,G.col)}}var F=function(Y,W){var K,j;if(Y.locked()||Y.isParent())return!1;var _=k[Y.id()];if(_)K=_.col*b+b/2+i.x1,j=_.row*m+m/2+i.y1;else{for(;A(O,M);)R();K=M*b+b/2+i.x1,j=O*m+m/2+i.y1,N(O,M),R()}return{x:K,y:j}};n.layoutPositions(this,e,F)}return this};var _g={ready:function(){},stop:function(){}};function Mi(t){this.options=be({},_g,t)}Mi.prototype.run=function(){var t=this.options,e=t.eles,r=this;return t.cy,r.emit("layoutstart"),e.nodes().positions(function(){return{x:0,y:0}}),r.one("layoutready",t.ready),r.emit("layoutready"),r.one("layoutstop",t.stop),r.emit("layoutstop"),this};Mi.prototype.stop=function(){return this};var Hg={positions:void 0,zoom:void 0,pan:void 0,fit:!0,padding:30,spacingFactor:void 0,animate:!1,animationDuration:500,animationEasing:void 0,animateFilter:function(e,r){return!0},ready:void 0,stop:void 0,transform:function(e,r){return r}};function pu(t){this.options=be({},Hg,t)}pu.prototype.run=function(){var t=this.options,e=t.eles,r=e.nodes(),a=Ge(t.positions);function n(i){if(t.positions==null)return Kf(i.position());if(a)return t.positions(i);var s=t.positions[i._private.data.id];return s??null}return r.layoutPositions(this,t,function(i,s){var o=n(i);return i.locked()||o==null?!1:o}),this};var Xg={fit:!0,padding:30,boundingBox:void 0,animate:!1,animationDuration:500,animationEasing:void 0,animateFilter:function(e,r){return!0},ready:void 0,stop:void 0,transform:function(e,r){return r}};function yu(t){this.options=be({},Xg,t)}yu.prototype.run=function(){var t=this.options,e=t.cy,r=t.eles,a=gt(t.boundingBox?t.boundingBox:{x1:0,y1:0,w:e.width(),h:e.height()}),n=function(s,o){return{x:a.x1+Math.round(Math.random()*a.w),y:a.y1+Math.round(Math.random()*a.h)}};return r.nodes().layoutPositions(this,t,n),this};var qg=[{name:"breadthfirst",impl:hu},{name:"circle",impl:cu},{name:"concentric",impl:vu},{name:"cose",impl:An},{name:"grid",impl:gu},{name:"null",impl:Mi},{name:"preset",impl:pu},{name:"random",impl:yu}];function mu(t){this.options=t,this.notifications=0}var Gs=function(){},zs=function(){throw new Error("A headless instance can not render images")};mu.prototype={recalculateRenderedStyle:Gs,notify:function(){this.notifications++},init:Gs,isHeadless:function(){return!0},png:zs,jpg:zs};var Ri={};Ri.arrowShapeWidth=.3;Ri.registerArrowShapes=function(){var t=this.arrowShapes={},e=this,r=function(l,f,h,d,c,v,p){var g=c.x-h/2-p,y=c.x+h/2+p,b=c.y-h/2-p,m=c.y+h/2+p,T=g<=l&&l<=y&&b<=f&&f<=m;return T},a=function(l,f,h,d,c){var v=l*Math.cos(d)-f*Math.sin(d),p=l*Math.sin(d)+f*Math.cos(d),g=v*h,y=p*h,b=g+c.x,m=y+c.y;return{x:b,y:m}},n=function(l,f,h,d){for(var c=[],v=0;v<l.length;v+=2){var p=l[v],g=l[v+1];c.push(a(p,g,f,h,d))}return c},i=function(l){for(var f=[],h=0;h<l.length;h++){var d=l[h];f.push(d.x,d.y)}return f},s=function(l){return l.pstyle("width").pfValue*l.pstyle("arrow-scale").pfValue*2},o=function(l,f){de(f)&&(f=t[f]),t[l]=be({name:l,points:[-.15,-.3,.15,-.3,.15,.3,-.15,.3],collide:function(d,c,v,p,g,y){var b=i(n(this.points,v+2*y,p,g)),m=dt(d,c,b);return m},roughCollide:r,draw:function(d,c,v,p){var g=n(this.points,c,v,p);e.arrowShapeImpl("polygon")(d,g)},spacing:function(d){return 0},gap:s},f)};o("none",{collide:an,roughCollide:an,draw:mi,spacing:Ki,gap:Ki}),o("triangle",{points:[-.15,-.3,0,0,.15,-.3]}),o("arrow","triangle"),o("triangle-backcurve",{points:t.triangle.points,controlPoint:[0,-.15],roughCollide:r,draw:function(l,f,h,d,c){var v=n(this.points,f,h,d),p=this.controlPoint,g=a(p[0],p[1],f,h,d);e.arrowShapeImpl(this.name)(l,v,g)},gap:function(l){return s(l)*.8}}),o("triangle-tee",{points:[0,0,.15,-.3,-.15,-.3,0,0],pointsTee:[-.15,-.4,-.15,-.5,.15,-.5,.15,-.4],collide:function(l,f,h,d,c,v,p){var g=i(n(this.points,h+2*p,d,c)),y=i(n(this.pointsTee,h+2*p,d,c)),b=dt(l,f,g)||dt(l,f,y);return b},draw:function(l,f,h,d,c){var v=n(this.points,f,h,d),p=n(this.pointsTee,f,h,d);e.arrowShapeImpl(this.name)(l,v,p)}}),o("circle-triangle",{radius:.15,pointsTr:[0,-.15,.15,-.45,-.15,-.45,0,-.15],collide:function(l,f,h,d,c,v,p){var g=c,y=Math.pow(g.x-l,2)+Math.pow(g.y-f,2)<=Math.pow((h+2*p)*this.radius,2),b=i(n(this.points,h+2*p,d,c));return dt(l,f,b)||y},draw:function(l,f,h,d,c){var v=n(this.pointsTr,f,h,d);e.arrowShapeImpl(this.name)(l,v,d.x,d.y,this.radius*f)},spacing:function(l){return e.getArrowWidth(l.pstyle("width").pfValue,l.pstyle("arrow-scale").value)*this.radius}}),o("triangle-cross",{points:[0,0,.15,-.3,-.15,-.3,0,0],baseCrossLinePts:[-.15,-.4,-.15,-.4,.15,-.4,.15,-.4],crossLinePts:function(l,f){var h=this.baseCrossLinePts.slice(),d=f/l,c=3,v=5;return h[c]=h[c]-d,h[v]=h[v]-d,h},collide:function(l,f,h,d,c,v,p){var g=i(n(this.points,h+2*p,d,c)),y=i(n(this.crossLinePts(h,v),h+2*p,d,c)),b=dt(l,f,g)||dt(l,f,y);return b},draw:function(l,f,h,d,c){var v=n(this.points,f,h,d),p=n(this.crossLinePts(f,c),f,h,d);e.arrowShapeImpl(this.name)(l,v,p)}}),o("vee",{points:[-.15,-.3,0,0,.15,-.3,0,-.15],gap:function(l){return s(l)*.525}}),o("circle",{radius:.15,collide:function(l,f,h,d,c,v,p){var g=c,y=Math.pow(g.x-l,2)+Math.pow(g.y-f,2)<=Math.pow((h+2*p)*this.radius,2);return y},draw:function(l,f,h,d,c){e.arrowShapeImpl(this.name)(l,d.x,d.y,this.radius*f)},spacing:function(l){return e.getArrowWidth(l.pstyle("width").pfValue,l.pstyle("arrow-scale").value)*this.radius}}),o("tee",{points:[-.15,0,-.15,-.1,.15,-.1,.15,0],spacing:function(l){return 1},gap:function(l){return 1}}),o("square",{points:[-.15,0,.15,0,.15,-.3,-.15,-.3]}),o("diamond",{points:[-.15,-.15,0,-.3,.15,-.15,0,0],gap:function(l){return l.pstyle("width").pfValue*l.pstyle("arrow-scale").value}}),o("chevron",{points:[0,0,-.15,-.15,-.1,-.2,0,-.1,.1,-.2,.15,-.15],gap:function(l){return .95*l.pstyle("width").pfValue*l.pstyle("arrow-scale").value}})};var wr={};wr.projectIntoViewport=function(t,e){var r=this.cy,a=this.findContainerClientCoords(),n=a[0],i=a[1],s=a[4],o=r.pan(),u=r.zoom(),l=((t-n)/s-o.x)/u,f=((e-i)/s-o.y)/u;return[l,f]};wr.findContainerClientCoords=function(){if(this.containerBB)return this.containerBB;var t=this.container,e=t.getBoundingClientRect(),r=this.cy.window().getComputedStyle(t),a=function(y){return parseFloat(r.getPropertyValue(y))},n={left:a("padding-left"),right:a("padding-right"),top:a("padding-top"),bottom:a("padding-bottom")},i={left:a("border-left-width"),right:a("border-right-width"),top:a("border-top-width"),bottom:a("border-bottom-width")},s=t.clientWidth,o=t.clientHeight,u=n.left+n.right,l=n.top+n.bottom,f=i.left+i.right,h=e.width/(s+f),d=s-u,c=o-l,v=e.left+n.left+i.left,p=e.top+n.top+i.top;return this.containerBB=[v,p,d,c,h]};wr.invalidateContainerClientCoordsCache=function(){this.containerBB=null};wr.findNearestElement=function(t,e,r,a){return this.findNearestElements(t,e,r,a)[0]};wr.findNearestElements=function(t,e,r,a){var n=this,i=this,s=i.getCachedZSortedEles(),o=[],u=i.cy.zoom(),l=i.cy.hasCompoundNodes(),f=(a?24:8)/u,h=(a?8:2)/u,d=(a?8:2)/u,c=1/0,v,p;r&&(s=s.interactive);function g(E,w){if(E.isNode()){if(p)return;p=E,o.push(E)}if(E.isEdge()&&(w==null||w<c))if(v){if(v.pstyle("z-compound-depth").value===E.pstyle("z-compound-depth").value&&v.pstyle("z-compound-depth").value===E.pstyle("z-compound-depth").value){for(var x=0;x<o.length;x++)if(o[x].isEdge()){o[x]=E,v=E,c=w??c;break}}}else o.push(E),v=E,c=w??c}function y(E){var w=E.outerWidth()+2*h,x=E.outerHeight()+2*h,D=w/2,L=x/2,A=E.position(),N=E.pstyle("corner-radius").value==="auto"?"auto":E.pstyle("corner-radius").pfValue,O=E._private.rscratch;if(A.x-D<=t&&t<=A.x+D&&A.y-L<=e&&e<=A.y+L){var M=i.nodeShapes[n.getNodeShape(E)];if(M.checkPoint(t,e,0,w,x,A.x,A.y,N,O))return g(E,0),!0}}function b(E){var w=E._private,x=w.rscratch,D=E.pstyle("width").pfValue,L=E.pstyle("arrow-scale").value,A=D/2+f,N=A*A,O=A*2,P=w.source,B=w.target,M;if(x.edgeType==="segments"||x.edgeType==="straight"||x.edgeType==="haystack"){for(var R=x.allpts,k=0;k+3<R.length;k+=2)if(uh(t,e,R[k],R[k+1],R[k+2],R[k+3],O)&&N>(M=vh(t,e,R[k],R[k+1],R[k+2],R[k+3])))return g(E,M),!0}else if(x.edgeType==="bezier"||x.edgeType==="multibezier"||x.edgeType==="self"||x.edgeType==="compound"){for(var R=x.allpts,k=0;k+5<x.allpts.length;k+=4)if(lh(t,e,R[k],R[k+1],R[k+2],R[k+3],R[k+4],R[k+5],O)&&N>(M=ch(t,e,R[k],R[k+1],R[k+2],R[k+3],R[k+4],R[k+5])))return g(E,M),!0}for(var P=P||w.source,B=B||w.target,z=n.getArrowWidth(D,L),G=[{name:"source",x:x.arrowStartX,y:x.arrowStartY,angle:x.srcArrowAngle},{name:"target",x:x.arrowEndX,y:x.arrowEndY,angle:x.tgtArrowAngle},{name:"mid-source",x:x.midX,y:x.midY,angle:x.midsrcArrowAngle},{name:"mid-target",x:x.midX,y:x.midY,angle:x.midtgtArrowAngle}],k=0;k<G.length;k++){var F=G[k],U=i.arrowShapes[E.pstyle(F.name+"-arrow-shape").value],Y=E.pstyle("width").pfValue;if(U.roughCollide(t,e,z,F.angle,{x:F.x,y:F.y},Y,f)&&U.collide(t,e,z,F.angle,{x:F.x,y:F.y},Y,f))return g(E),!0}l&&o.length>0&&(y(P),y(B))}function m(E,w,x){return At(E,w,x)}function T(E,w){var x=E._private,D=d,L;w?L=w+"-":L="",E.boundingBox();var A=x.labelBounds[w||"main"],N=E.pstyle(L+"label").value,O=E.pstyle("text-events").strValue==="yes";if(!(!O||!N)){var M=m(x.rscratch,"labelX",w),R=m(x.rscratch,"labelY",w),k=m(x.rscratch,"labelAngle",w),P=E.pstyle(L+"text-margin-x").pfValue,B=E.pstyle(L+"text-margin-y").pfValue,z=A.x1-D-P,G=A.x2+D-P,F=A.y1-D-B,U=A.y2+D-B;if(k){var Y=Math.cos(k),W=Math.sin(k),K=function(ce,te){return ce=ce-M,te=te-R,{x:ce*Y-te*W+M,y:ce*W+te*Y+R}},j=K(z,F),_=K(z,U),V=K(G,F),H=K(G,U),Q=[j.x+P,j.y+B,V.x+P,V.y+B,H.x+P,H.y+B,_.x+P,_.y+B];if(dt(t,e,Q))return g(E),!0}else if(Gr(A,t,e))return g(E),!0}}for(var C=s.length-1;C>=0;C--){var S=s[C];S.isNode()?y(S)||T(S):b(S)||T(S)||T(S,"source")||T(S,"target")}return o};wr.getAllInBox=function(t,e,r,a){var n=this.getCachedZSortedEles().interactive,i=[],s=Math.min(t,r),o=Math.max(t,r),u=Math.min(e,a),l=Math.max(e,a);t=s,r=o,e=u,a=l;for(var f=gt({x1:t,y1:e,x2:r,y2:a}),h=0;h<n.length;h++){var d=n[h];if(d.isNode()){var c=d,v=c.boundingBox({includeNodes:!0,includeEdges:!1,includeLabels:!1});wi(f,v)&&!Lo(v,f)&&i.push(c)}else{var p=d,g=p._private,y=g.rscratch;if(y.startX!=null&&y.startY!=null&&!Gr(f,y.startX,y.startY)||y.endX!=null&&y.endY!=null&&!Gr(f,y.endX,y.endY))continue;if(y.edgeType==="bezier"||y.edgeType==="multibezier"||y.edgeType==="self"||y.edgeType==="compound"||y.edgeType==="segments"||y.edgeType==="haystack"){for(var b=g.rstyle.bezierPts||g.rstyle.linePts||g.rstyle.haystackPts,m=!0,T=0;T<b.length;T++)if(!oh(f,b[T])){m=!1;break}m&&i.push(p)}else(y.edgeType==="haystack"||y.edgeType==="straight")&&i.push(p)}}return i};var hn={};hn.calculateArrowAngles=function(t){var e=t._private.rscratch,r=e.edgeType==="haystack",a=e.edgeType==="bezier",n=e.edgeType==="multibezier",i=e.edgeType==="segments",s=e.edgeType==="compound",o=e.edgeType==="self",u,l,f,h,d,c,y,b;if(r?(f=e.haystackPts[0],h=e.haystackPts[1],d=e.haystackPts[2],c=e.haystackPts[3]):(f=e.arrowStartX,h=e.arrowStartY,d=e.arrowEndX,c=e.arrowEndY),y=e.midX,b=e.midY,i)u=f-e.segpts[0],l=h-e.segpts[1];else if(n||s||o||a){var v=e.allpts,p=Ke(v[0],v[2],v[4],.1),g=Ke(v[1],v[3],v[5],.1);u=f-p,l=h-g}else u=f-y,l=h-b;e.srcArrowAngle=Ra(u,l);var y=e.midX,b=e.midY;if(r&&(y=(f+d)/2,b=(h+c)/2),u=d-f,l=c-h,i){var v=e.allpts;if(v.length/2%2===0){var m=v.length/2,T=m-2;u=v[m]-v[T],l=v[m+1]-v[T+1]}else if(e.isRound)u=e.midVector[1],l=-e.midVector[0];else{var m=v.length/2-1,T=m-2;u=v[m]-v[T],l=v[m+1]-v[T+1]}}else if(n||s||o){var v=e.allpts,C=e.ctrlpts,S,E,w,x;if(C.length/2%2===0){var D=v.length/2-1,L=D+2,A=L+2;S=Ke(v[D],v[L],v[A],0),E=Ke(v[D+1],v[L+1],v[A+1],0),w=Ke(v[D],v[L],v[A],1e-4),x=Ke(v[D+1],v[L+1],v[A+1],1e-4)}else{var L=v.length/2-1,D=L-2,A=L+2;S=Ke(v[D],v[L],v[A],.4999),E=Ke(v[D+1],v[L+1],v[A+1],.4999),w=Ke(v[D],v[L],v[A],.5),x=Ke(v[D+1],v[L+1],v[A+1],.5)}u=w-S,l=x-E}if(e.midtgtArrowAngle=Ra(u,l),e.midDispX=u,e.midDispY=l,u*=-1,l*=-1,i){var v=e.allpts;if(v.length/2%2!==0){if(!e.isRound){var m=v.length/2-1,N=m+2;u=-(v[N]-v[m]),l=-(v[N+1]-v[m+1])}}}if(e.midsrcArrowAngle=Ra(u,l),i)u=d-e.segpts[e.segpts.length-2],l=c-e.segpts[e.segpts.length-1];else if(n||s||o||a){var v=e.allpts,O=v.length,p=Ke(v[O-6],v[O-4],v[O-2],.9),g=Ke(v[O-5],v[O-3],v[O-1],.9);u=d-p,l=c-g}else u=d-y,l=c-b;e.tgtArrowAngle=Ra(u,l)};hn.getArrowWidth=hn.getArrowHeight=function(t,e){var r=this.arrowWidthCache=this.arrowWidthCache||{},a=r[t+", "+e];return a||(a=Math.max(Math.pow(t*13.37,.9),29)*e,r[t+", "+e]=a,a)};var ri,ai,kt={},bt={},Vs,Us,hr,Qa,Ut,or,fr,Rt,Ar,$a,bu,Eu,ni,ii,$s,Ys=function(e,r,a){a.x=r.x-e.x,a.y=r.y-e.y,a.len=Math.sqrt(a.x*a.x+a.y*a.y),a.nx=a.x/a.len,a.ny=a.y/a.len,a.ang=Math.atan2(a.ny,a.nx)},Wg=function(e,r){r.x=e.x*-1,r.y=e.y*-1,r.nx=e.nx*-1,r.ny=e.ny*-1,r.ang=e.ang>0?-(Math.PI-e.ang):Math.PI+e.ang},Kg=function(e,r,a,n,i){if(e!==$s?Ys(r,e,kt):Wg(bt,kt),Ys(r,a,bt),Vs=kt.nx*bt.ny-kt.ny*bt.nx,Us=kt.nx*bt.nx-kt.ny*-bt.ny,Ut=Math.asin(Math.max(-1,Math.min(1,Vs))),Math.abs(Ut)<1e-6){ri=r.x,ai=r.y,fr=Ar=0;return}hr=1,Qa=!1,Us<0?Ut<0?Ut=Math.PI+Ut:(Ut=Math.PI-Ut,hr=-1,Qa=!0):Ut>0&&(hr=-1,Qa=!0),r.radius!==void 0?Ar=r.radius:Ar=n,or=Ut/2,$a=Math.min(kt.len/2,bt.len/2),i?(Rt=Math.abs(Math.cos(or)*Ar/Math.sin(or)),Rt>$a?(Rt=$a,fr=Math.abs(Rt*Math.sin(or)/Math.cos(or))):fr=Ar):(Rt=Math.min($a,Ar),fr=Math.abs(Rt*Math.sin(or)/Math.cos(or))),ni=r.x+bt.nx*Rt,ii=r.y+bt.ny*Rt,ri=ni-bt.ny*fr*hr,ai=ii+bt.nx*fr*hr,bu=r.x+kt.nx*Rt,Eu=r.y+kt.ny*Rt,$s=r};function wu(t,e){e.radius===0?t.lineTo(e.cx,e.cy):t.arc(e.cx,e.cy,e.radius,e.startAngle,e.endAngle,e.counterClockwise)}function ki(t,e,r,a){var n=arguments.length>4&&arguments[4]!==void 0?arguments[4]:!0;return a===0||e.radius===0?{cx:e.x,cy:e.y,radius:0,startX:e.x,startY:e.y,stopX:e.x,stopY:e.y,startAngle:void 0,endAngle:void 0,counterClockwise:void 0}:(Kg(t,e,r,a,n),{cx:ri,cy:ai,radius:fr,startX:bu,startY:Eu,stopX:ni,stopY:ii,startAngle:kt.ang+Math.PI/2*hr,endAngle:bt.ang-Math.PI/2*hr,counterClockwise:Qa})}var ut={};ut.findMidptPtsEtc=function(t,e){var r=e.posPts,a=e.intersectionPts,n=e.vectorNormInverse,i,s=t.pstyle("source-endpoint"),o=t.pstyle("target-endpoint"),u=s.units!=null&&o.units!=null,l=function(C,S,E,w){var x=w-S,D=E-C,L=Math.sqrt(D*D+x*x);return{x:-x/L,y:D/L}},f=t.pstyle("edge-distances").value;switch(f){case"node-position":i=r;break;case"intersection":i=a;break;case"endpoints":{if(u){var h=this.manualEndptToPx(t.source()[0],s),d=St(h,2),c=d[0],v=d[1],p=this.manualEndptToPx(t.target()[0],o),g=St(p,2),y=g[0],b=g[1],m={x1:c,y1:v,x2:y,y2:b};n=l(c,v,y,b),i=m}else Ne("Edge ".concat(t.id()," has edge-distances:endpoints specified without manual endpoints specified via source-endpoint and target-endpoint.  Falling back on edge-distances:intersection (default).")),i=a;break}}return{midptPts:i,vectorNormInverse:n}};ut.findHaystackPoints=function(t){for(var e=0;e<t.length;e++){var r=t[e],a=r._private,n=a.rscratch;if(!n.haystack){var i=Math.random()*2*Math.PI;n.source={x:Math.cos(i),y:Math.sin(i)},i=Math.random()*2*Math.PI,n.target={x:Math.cos(i),y:Math.sin(i)}}var s=a.source,o=a.target,u=s.position(),l=o.position(),f=s.width(),h=o.width(),d=s.height(),c=o.height(),v=r.pstyle("haystack-radius").value,p=v/2;n.haystackPts=n.allpts=[n.source.x*f*p+u.x,n.source.y*d*p+u.y,n.target.x*h*p+l.x,n.target.y*c*p+l.y],n.midX=(n.allpts[0]+n.allpts[2])/2,n.midY=(n.allpts[1]+n.allpts[3])/2,n.edgeType="haystack",n.haystack=!0,this.storeEdgeProjections(r),this.calculateArrowAngles(r),this.recalculateEdgeLabelProjections(r),this.calculateLabelAngles(r)}};ut.findSegmentsPoints=function(t,e){var r=t._private.rscratch,a=t.pstyle("segment-weights"),n=t.pstyle("segment-distances"),i=t.pstyle("segment-radii"),s=t.pstyle("radius-type"),o=Math.min(a.pfValue.length,n.pfValue.length),u=i.pfValue[i.pfValue.length-1],l=s.pfValue[s.pfValue.length-1];r.edgeType="segments",r.segpts=[],r.radii=[],r.isArcRadius=[];for(var f=0;f<o;f++){var h=a.pfValue[f],d=n.pfValue[f],c=1-h,v=h,p=this.findMidptPtsEtc(t,e),g=p.midptPts,y=p.vectorNormInverse,b={x:g.x1*c+g.x2*v,y:g.y1*c+g.y2*v};r.segpts.push(b.x+y.x*d,b.y+y.y*d),r.radii.push(i.pfValue[f]!==void 0?i.pfValue[f]:u),r.isArcRadius.push((s.pfValue[f]!==void 0?s.pfValue[f]:l)==="arc-radius")}};ut.findLoopPoints=function(t,e,r,a){var n=t._private.rscratch,i=e.dirCounts,s=e.srcPos,o=t.pstyle("control-point-distances"),u=o?o.pfValue[0]:void 0,l=t.pstyle("loop-direction").pfValue,f=t.pstyle("loop-sweep").pfValue,h=t.pstyle("control-point-step-size").pfValue;n.edgeType="self";var d=r,c=h;a&&(d=0,c=u);var v=l-Math.PI/2,p=v-f/2,g=v+f/2,y=l+"_"+f;d=i[y]===void 0?i[y]=0:++i[y],n.ctrlpts=[s.x+Math.cos(p)*1.4*c*(d/3+1),s.y+Math.sin(p)*1.4*c*(d/3+1),s.x+Math.cos(g)*1.4*c*(d/3+1),s.y+Math.sin(g)*1.4*c*(d/3+1)]};ut.findCompoundLoopPoints=function(t,e,r,a){var n=t._private.rscratch;n.edgeType="compound";var i=e.srcPos,s=e.tgtPos,o=e.srcW,u=e.srcH,l=e.tgtW,f=e.tgtH,h=t.pstyle("control-point-step-size").pfValue,d=t.pstyle("control-point-distances"),c=d?d.pfValue[0]:void 0,v=r,p=h;a&&(v=0,p=c);var g=50,y={x:i.x-o/2,y:i.y-u/2},b={x:s.x-l/2,y:s.y-f/2},m={x:Math.min(y.x,b.x),y:Math.min(y.y,b.y)},T=.5,C=Math.max(T,Math.log(o*.01)),S=Math.max(T,Math.log(l*.01));n.ctrlpts=[m.x,m.y-(1+Math.pow(g,1.12)/100)*p*(v/3+1)*C,m.x-(1+Math.pow(g,1.12)/100)*p*(v/3+1)*S,m.y]};ut.findStraightEdgePoints=function(t){t._private.rscratch.edgeType="straight"};ut.findBezierPoints=function(t,e,r,a,n){var i=t._private.rscratch,s=t.pstyle("control-point-step-size").pfValue,o=t.pstyle("control-point-distances"),u=t.pstyle("control-point-weights"),l=o&&u?Math.min(o.value.length,u.value.length):1,f=o?o.pfValue[0]:void 0,h=u.value[0],d=a;i.edgeType=d?"multibezier":"bezier",i.ctrlpts=[];for(var c=0;c<l;c++){var v=(.5-e.eles.length/2+r)*s*(n?-1:1),p=void 0,g=Do(v);d&&(f=o?o.pfValue[c]:s,h=u.value[c]),a?p=f:p=f!==void 0?g*f:void 0;var y=p!==void 0?p:v,b=1-h,m=h,T=this.findMidptPtsEtc(t,e),C=T.midptPts,S=T.vectorNormInverse,E={x:C.x1*b+C.x2*m,y:C.y1*b+C.y2*m};i.ctrlpts.push(E.x+S.x*y,E.y+S.y*y)}};ut.findTaxiPoints=function(t,e){var r=t._private.rscratch;r.edgeType="segments";var a="vertical",n="horizontal",i="leftward",s="rightward",o="downward",u="upward",l="auto",f=e.posPts,h=e.srcW,d=e.srcH,c=e.tgtW,v=e.tgtH,p=t.pstyle("edge-distances").value,g=p!=="node-position",y=t.pstyle("taxi-direction").value,b=y,m=t.pstyle("taxi-turn"),T=m.units==="%",C=m.pfValue,S=C<0,E=t.pstyle("taxi-turn-min-distance").pfValue,w=g?(h+c)/2:0,x=g?(d+v)/2:0,D=f.x2-f.x1,L=f.y2-f.y1,A=function(ye,me){return ye>0?Math.max(ye-me,0):Math.min(ye+me,0)},N=A(D,w),O=A(L,x),M=!1;b===l?y=Math.abs(N)>Math.abs(O)?n:a:b===u||b===o?(y=a,M=!0):(b===i||b===s)&&(y=n,M=!0);var R=y===a,k=R?O:N,P=R?L:D,B=Do(P),z=!1;!(M&&(T||S))&&(b===o&&P<0||b===u&&P>0||b===i&&P>0||b===s&&P<0)&&(B*=-1,k=B*Math.abs(k),z=!0);var G;if(T){var F=C<0?1+C:C;G=F*k}else{var U=C<0?k:0;G=U+C*B}var Y=function(ye){return Math.abs(ye)<E||Math.abs(ye)>=Math.abs(k)},W=Y(G),K=Y(Math.abs(k)-Math.abs(G)),j=W||K;if(j&&!z)if(R){var _=Math.abs(P)<=d/2,V=Math.abs(D)<=c/2;if(_){var H=(f.x1+f.x2)/2,Q=f.y1,ne=f.y2;r.segpts=[H,Q,H,ne]}else if(V){var ce=(f.y1+f.y2)/2,te=f.x1,se=f.x2;r.segpts=[te,ce,se,ce]}else r.segpts=[f.x1,f.y2]}else{var ue=Math.abs(P)<=h/2,ve=Math.abs(L)<=v/2;if(ue){var fe=(f.y1+f.y2)/2,pe=f.x1,Ae=f.x2;r.segpts=[pe,fe,Ae,fe]}else if(ve){var xe=(f.x1+f.x2)/2,we=f.y1,De=f.y2;r.segpts=[xe,we,xe,De]}else r.segpts=[f.x2,f.y1]}else if(R){var ee=f.y1+G+(g?d/2*B:0),I=f.x1,$=f.x2;r.segpts=[I,ee,$,ee]}else{var J=f.x1+G+(g?h/2*B:0),q=f.y1,X=f.y2;r.segpts=[J,q,J,X]}if(r.isRound){var ae=t.pstyle("taxi-radius").value,Z=t.pstyle("radius-type").value[0]==="arc-radius";r.radii=new Array(r.segpts.length/2).fill(ae),r.isArcRadius=new Array(r.segpts.length/2).fill(Z)}};ut.tryToCorrectInvalidPoints=function(t,e){var r=t._private.rscratch;if(r.edgeType==="bezier"){var a=e.srcPos,n=e.tgtPos,i=e.srcW,s=e.srcH,o=e.tgtW,u=e.tgtH,l=e.srcShape,f=e.tgtShape,h=e.srcCornerRadius,d=e.tgtCornerRadius,c=e.srcRs,v=e.tgtRs,p=!ie(r.startX)||!ie(r.startY),g=!ie(r.arrowStartX)||!ie(r.arrowStartY),y=!ie(r.endX)||!ie(r.endY),b=!ie(r.arrowEndX)||!ie(r.arrowEndY),m=3,T=this.getArrowWidth(t.pstyle("width").pfValue,t.pstyle("arrow-scale").value)*this.arrowShapeWidth,C=m*T,S=gr({x:r.ctrlpts[0],y:r.ctrlpts[1]},{x:r.startX,y:r.startY}),E=S<C,w=gr({x:r.ctrlpts[0],y:r.ctrlpts[1]},{x:r.endX,y:r.endY}),x=w<C,D=!1;if(p||g||E){D=!0;var L={x:r.ctrlpts[0]-a.x,y:r.ctrlpts[1]-a.y},A=Math.sqrt(L.x*L.x+L.y*L.y),N={x:L.x/A,y:L.y/A},O=Math.max(i,s),M={x:r.ctrlpts[0]+N.x*2*O,y:r.ctrlpts[1]+N.y*2*O},R=l.intersectLine(a.x,a.y,i,s,M.x,M.y,0,h,c);E?(r.ctrlpts[0]=r.ctrlpts[0]+N.x*(C-S),r.ctrlpts[1]=r.ctrlpts[1]+N.y*(C-S)):(r.ctrlpts[0]=R[0]+N.x*C,r.ctrlpts[1]=R[1]+N.y*C)}if(y||b||x){D=!0;var k={x:r.ctrlpts[0]-n.x,y:r.ctrlpts[1]-n.y},P=Math.sqrt(k.x*k.x+k.y*k.y),B={x:k.x/P,y:k.y/P},z=Math.max(i,s),G={x:r.ctrlpts[0]+B.x*2*z,y:r.ctrlpts[1]+B.y*2*z},F=f.intersectLine(n.x,n.y,o,u,G.x,G.y,0,d,v);x?(r.ctrlpts[0]=r.ctrlpts[0]+B.x*(C-w),r.ctrlpts[1]=r.ctrlpts[1]+B.y*(C-w)):(r.ctrlpts[0]=F[0]+B.x*C,r.ctrlpts[1]=F[1]+B.y*C)}D&&this.findEndpoints(t)}};ut.storeAllpts=function(t){var e=t._private.rscratch;if(e.edgeType==="multibezier"||e.edgeType==="bezier"||e.edgeType==="self"||e.edgeType==="compound"){e.allpts=[],e.allpts.push(e.startX,e.startY);for(var r=0;r+1<e.ctrlpts.length;r+=2)e.allpts.push(e.ctrlpts[r],e.ctrlpts[r+1]),r+3<e.ctrlpts.length&&e.allpts.push((e.ctrlpts[r]+e.ctrlpts[r+2])/2,(e.ctrlpts[r+1]+e.ctrlpts[r+3])/2);e.allpts.push(e.endX,e.endY);var a,n;e.ctrlpts.length/2%2===0?(a=e.allpts.length/2-1,e.midX=e.allpts[a],e.midY=e.allpts[a+1]):(a=e.allpts.length/2-3,n=.5,e.midX=Ke(e.allpts[a],e.allpts[a+2],e.allpts[a+4],n),e.midY=Ke(e.allpts[a+1],e.allpts[a+3],e.allpts[a+5],n))}else if(e.edgeType==="straight")e.allpts=[e.startX,e.startY,e.endX,e.endY],e.midX=(e.startX+e.endX+e.arrowStartX+e.arrowEndX)/4,e.midY=(e.startY+e.endY+e.arrowStartY+e.arrowEndY)/4;else if(e.edgeType==="segments"){if(e.allpts=[],e.allpts.push(e.startX,e.startY),e.allpts.push.apply(e.allpts,e.segpts),e.allpts.push(e.endX,e.endY),e.isRound){e.roundCorners=[];for(var i=2;i+3<e.allpts.length;i+=2){var s=e.radii[i/2-1],o=e.isArcRadius[i/2-1];e.roundCorners.push(ki({x:e.allpts[i-2],y:e.allpts[i-1]},{x:e.allpts[i],y:e.allpts[i+1],radius:s},{x:e.allpts[i+2],y:e.allpts[i+3]},s,o))}}if(e.segpts.length%4===0){var u=e.segpts.length/2,l=u-2;e.midX=(e.segpts[l]+e.segpts[u])/2,e.midY=(e.segpts[l+1]+e.segpts[u+1])/2}else{var f=e.segpts.length/2-1;if(!e.isRound)e.midX=e.segpts[f],e.midY=e.segpts[f+1];else{var h={x:e.segpts[f],y:e.segpts[f+1]},d=e.roundCorners[f/2],c=[h.x-d.cx,h.y-d.cy],v=d.radius/Math.sqrt(Math.pow(c[0],2)+Math.pow(c[1],2));c=c.map(function(p){return p*v}),e.midX=d.cx+c[0],e.midY=d.cy+c[1],e.midVector=c}}}};ut.checkForInvalidEdgeWarning=function(t){var e=t[0]._private.rscratch;e.nodesOverlap||ie(e.startX)&&ie(e.startY)&&ie(e.endX)&&ie(e.endY)?e.loggedErr=!1:e.loggedErr||(e.loggedErr=!0,Ne("Edge `"+t.id()+"` has invalid endpoints and so it is impossible to draw.  Adjust your edge style (e.g. control points) accordingly or use an alternative edge type.  This is expected behaviour when the source node and the target node overlap."))};ut.findEdgeControlPoints=function(t){var e=this;if(!(!t||t.length===0)){for(var r=this,a=r.cy,n=a.hasCompoundNodes(),i={map:new Bt,get:function(E){var w=this.map.get(E[0]);return w!=null?w.get(E[1]):null},set:function(E,w){var x=this.map.get(E[0]);x==null&&(x=new Bt,this.map.set(E[0],x)),x.set(E[1],w)}},s=[],o=[],u=0;u<t.length;u++){var l=t[u],f=l._private,h=l.pstyle("curve-style").value;if(!(l.removed()||!l.takesUpSpace())){if(h==="haystack"){o.push(l);continue}var d=h==="unbundled-bezier"||h.endsWith("segments")||h==="straight"||h==="straight-triangle"||h.endsWith("taxi"),c=h==="unbundled-bezier"||h==="bezier",v=f.source,p=f.target,g=v.poolIndex(),y=p.poolIndex(),b=[g,y].sort(),m=i.get(b);m==null&&(m={eles:[]},i.set(b,m),s.push(b)),m.eles.push(l),d&&(m.hasUnbundled=!0),c&&(m.hasBezier=!0)}}for(var T=function(E){var w=s[E],x=i.get(w),D=void 0;if(!x.hasUnbundled){var L=x.eles[0].parallelEdges().filter(function(J){return J.isBundledBezier()});bi(x.eles),L.forEach(function(J){return x.eles.push(J)}),x.eles.sort(function(J,q){return J.poolIndex()-q.poolIndex()})}var A=x.eles[0],N=A.source(),O=A.target();if(N.poolIndex()>O.poolIndex()){var M=N;N=O,O=M}var R=x.srcPos=N.position(),k=x.tgtPos=O.position(),P=x.srcW=N.outerWidth(),B=x.srcH=N.outerHeight(),z=x.tgtW=O.outerWidth(),G=x.tgtH=O.outerHeight(),F=x.srcShape=r.nodeShapes[e.getNodeShape(N)],U=x.tgtShape=r.nodeShapes[e.getNodeShape(O)],Y=x.srcCornerRadius=N.pstyle("corner-radius").value==="auto"?"auto":N.pstyle("corner-radius").pfValue,W=x.tgtCornerRadius=O.pstyle("corner-radius").value==="auto"?"auto":O.pstyle("corner-radius").pfValue,K=x.tgtRs=O._private.rscratch,j=x.srcRs=N._private.rscratch;x.dirCounts={north:0,west:0,south:0,east:0,northwest:0,southwest:0,northeast:0,southeast:0};for(var _=0;_<x.eles.length;_++){var V=x.eles[_],H=V[0]._private.rscratch,Q=V.pstyle("curve-style").value,ne=Q==="unbundled-bezier"||Q.endsWith("segments")||Q.endsWith("taxi"),ce=!N.same(V.source());if(!x.calculatedIntersection&&N!==O&&(x.hasBezier||x.hasUnbundled)){x.calculatedIntersection=!0;var te=F.intersectLine(R.x,R.y,P,B,k.x,k.y,0,Y,j),se=x.srcIntn=te,ue=U.intersectLine(k.x,k.y,z,G,R.x,R.y,0,W,K),ve=x.tgtIntn=ue,fe=x.intersectionPts={x1:te[0],x2:ue[0],y1:te[1],y2:ue[1]},pe=x.posPts={x1:R.x,x2:k.x,y1:R.y,y2:k.y},Ae=ue[1]-te[1],xe=ue[0]-te[0],we=Math.sqrt(xe*xe+Ae*Ae),De=x.vector={x:xe,y:Ae},ee=x.vectorNorm={x:De.x/we,y:De.y/we},I={x:-ee.y,y:ee.x};x.nodesOverlap=!ie(we)||U.checkPoint(te[0],te[1],0,z,G,k.x,k.y,W,K)||F.checkPoint(ue[0],ue[1],0,P,B,R.x,R.y,Y,j),x.vectorNormInverse=I,D={nodesOverlap:x.nodesOverlap,dirCounts:x.dirCounts,calculatedIntersection:!0,hasBezier:x.hasBezier,hasUnbundled:x.hasUnbundled,eles:x.eles,srcPos:k,tgtPos:R,srcW:z,srcH:G,tgtW:P,tgtH:B,srcIntn:ve,tgtIntn:se,srcShape:U,tgtShape:F,posPts:{x1:pe.x2,y1:pe.y2,x2:pe.x1,y2:pe.y1},intersectionPts:{x1:fe.x2,y1:fe.y2,x2:fe.x1,y2:fe.y1},vector:{x:-De.x,y:-De.y},vectorNorm:{x:-ee.x,y:-ee.y},vectorNormInverse:{x:-I.x,y:-I.y}}}var $=ce?D:x;H.nodesOverlap=$.nodesOverlap,H.srcIntn=$.srcIntn,H.tgtIntn=$.tgtIntn,H.isRound=Q.startsWith("round"),n&&(N.isParent()||N.isChild()||O.isParent()||O.isChild())&&(N.parents().anySame(O)||O.parents().anySame(N)||N.same(O)&&N.isParent())?e.findCompoundLoopPoints(V,$,_,ne):N===O?e.findLoopPoints(V,$,_,ne):Q.endsWith("segments")?e.findSegmentsPoints(V,$):Q.endsWith("taxi")?e.findTaxiPoints(V,$):Q==="straight"||!ne&&x.eles.length%2===1&&_===Math.floor(x.eles.length/2)?e.findStraightEdgePoints(V):e.findBezierPoints(V,$,_,ne,ce),e.findEndpoints(V),e.tryToCorrectInvalidPoints(V,$),e.checkForInvalidEdgeWarning(V),e.storeAllpts(V),e.storeEdgeProjections(V),e.calculateArrowAngles(V),e.recalculateEdgeLabelProjections(V),e.calculateLabelAngles(V)}},C=0;C<s.length;C++)T(C);this.findHaystackPoints(o)}};function xu(t){var e=[];if(t!=null){for(var r=0;r<t.length;r+=2){var a=t[r],n=t[r+1];e.push({x:a,y:n})}return e}}ut.getSegmentPoints=function(t){var e=t[0]._private.rscratch,r=e.edgeType;if(r==="segments")return this.recalculateRenderedStyle(t),xu(e.segpts)};ut.getControlPoints=function(t){var e=t[0]._private.rscratch,r=e.edgeType;if(r==="bezier"||r==="multibezier"||r==="self"||r==="compound")return this.recalculateRenderedStyle(t),xu(e.ctrlpts)};ut.getEdgeMidpoint=function(t){var e=t[0]._private.rscratch;return this.recalculateRenderedStyle(t),{x:e.midX,y:e.midY}};var Aa={};Aa.manualEndptToPx=function(t,e){var r=this,a=t.position(),n=t.outerWidth(),i=t.outerHeight(),s=t._private.rscratch;if(e.value.length===2){var o=[e.pfValue[0],e.pfValue[1]];return e.units[0]==="%"&&(o[0]=o[0]*n),e.units[1]==="%"&&(o[1]=o[1]*i),o[0]+=a.x,o[1]+=a.y,o}else{var u=e.pfValue[0];u=-Math.PI/2+u;var l=2*Math.max(n,i),f=[a.x+Math.cos(u)*l,a.y+Math.sin(u)*l];return r.nodeShapes[this.getNodeShape(t)].intersectLine(a.x,a.y,n,i,f[0],f[1],0,t.pstyle("corner-radius").value==="auto"?"auto":t.pstyle("corner-radius").pfValue,s)}};Aa.findEndpoints=function(t){var e=this,r,a=t.source()[0],n=t.target()[0],i=a.position(),s=n.position(),o=t.pstyle("target-arrow-shape").value,u=t.pstyle("source-arrow-shape").value,l=t.pstyle("target-distance-from-node").pfValue,f=t.pstyle("source-distance-from-node").pfValue,h=a._private.rscratch,d=n._private.rscratch,c=t.pstyle("curve-style").value,v=t._private.rscratch,p=v.edgeType,g=c==="taxi",y=p==="self"||p==="compound",b=p==="bezier"||p==="multibezier"||y,m=p!=="bezier",T=p==="straight"||p==="segments",C=p==="segments",S=b||m||T,E=y||g,w=t.pstyle("source-endpoint"),x=E?"outside-to-node":w.value,D=a.pstyle("corner-radius").value==="auto"?"auto":a.pstyle("corner-radius").pfValue,L=t.pstyle("target-endpoint"),A=E?"outside-to-node":L.value,N=n.pstyle("corner-radius").value==="auto"?"auto":n.pstyle("corner-radius").pfValue;v.srcManEndpt=w,v.tgtManEndpt=L;var O,M,R,k;if(b){var P=[v.ctrlpts[0],v.ctrlpts[1]],B=m?[v.ctrlpts[v.ctrlpts.length-2],v.ctrlpts[v.ctrlpts.length-1]]:P;O=B,M=P}else if(T){var z=C?v.segpts.slice(0,2):[s.x,s.y],G=C?v.segpts.slice(v.segpts.length-2):[i.x,i.y];O=G,M=z}if(A==="inside-to-node")r=[s.x,s.y];else if(L.units)r=this.manualEndptToPx(n,L);else if(A==="outside-to-line")r=v.tgtIntn;else if(A==="outside-to-node"||A==="outside-to-node-or-label"?R=O:(A==="outside-to-line"||A==="outside-to-line-or-label")&&(R=[i.x,i.y]),r=e.nodeShapes[this.getNodeShape(n)].intersectLine(s.x,s.y,n.outerWidth(),n.outerHeight(),R[0],R[1],0,N,d),A==="outside-to-node-or-label"||A==="outside-to-line-or-label"){var F=n._private.rscratch,U=F.labelWidth,Y=F.labelHeight,W=F.labelX,K=F.labelY,j=U/2,_=Y/2,V=n.pstyle("text-valign").value;V==="top"?K-=_:V==="bottom"&&(K+=_);var H=n.pstyle("text-halign").value;H==="left"?W-=j:H==="right"&&(W+=j);var Q=pa(R[0],R[1],[W-j,K-_,W+j,K-_,W+j,K+_,W-j,K+_],s.x,s.y);if(Q.length>0){var ne=i,ce=ur(ne,Ir(r)),te=ur(ne,Ir(Q)),se=ce;if(te<ce&&(r=Q,se=te),Q.length>2){var ue=ur(ne,{x:Q[2],y:Q[3]});ue<se&&(r=[Q[2],Q[3]])}}}var ve=ka(r,O,e.arrowShapes[o].spacing(t)+l),fe=ka(r,O,e.arrowShapes[o].gap(t)+l);if(v.endX=fe[0],v.endY=fe[1],v.arrowEndX=ve[0],v.arrowEndY=ve[1],x==="inside-to-node")r=[i.x,i.y];else if(w.units)r=this.manualEndptToPx(a,w);else if(x==="outside-to-line")r=v.srcIntn;else if(x==="outside-to-node"||x==="outside-to-node-or-label"?k=M:(x==="outside-to-line"||x==="outside-to-line-or-label")&&(k=[s.x,s.y]),r=e.nodeShapes[this.getNodeShape(a)].intersectLine(i.x,i.y,a.outerWidth(),a.outerHeight(),k[0],k[1],0,D,h),x==="outside-to-node-or-label"||x==="outside-to-line-or-label"){var pe=a._private.rscratch,Ae=pe.labelWidth,xe=pe.labelHeight,we=pe.labelX,De=pe.labelY,ee=Ae/2,I=xe/2,$=a.pstyle("text-valign").value;$==="top"?De-=I:$==="bottom"&&(De+=I);var J=a.pstyle("text-halign").value;J==="left"?we-=ee:J==="right"&&(we+=ee);var q=pa(k[0],k[1],[we-ee,De-I,we+ee,De-I,we+ee,De+I,we-ee,De+I],i.x,i.y);if(q.length>0){var X=s,ae=ur(X,Ir(r)),Z=ur(X,Ir(q)),re=ae;if(Z<ae&&(r=[q[0],q[1]],re=Z),q.length>2){var ye=ur(X,{x:q[2],y:q[3]});ye<re&&(r=[q[2],q[3]])}}}var me=ka(r,M,e.arrowShapes[u].spacing(t)+f),he=ka(r,M,e.arrowShapes[u].gap(t)+f);v.startX=he[0],v.startY=he[1],v.arrowStartX=me[0],v.arrowStartY=me[1],S&&(!ie(v.startX)||!ie(v.startY)||!ie(v.endX)||!ie(v.endY)?v.badLine=!0:v.badLine=!1)};Aa.getSourceEndpoint=function(t){var e=t[0]._private.rscratch;switch(this.recalculateRenderedStyle(t),e.edgeType){case"haystack":return{x:e.haystackPts[0],y:e.haystackPts[1]};default:return{x:e.arrowStartX,y:e.arrowStartY}}};Aa.getTargetEndpoint=function(t){var e=t[0]._private.rscratch;switch(this.recalculateRenderedStyle(t),e.edgeType){case"haystack":return{x:e.haystackPts[2],y:e.haystackPts[3]};default:return{x:e.arrowEndX,y:e.arrowEndY}}};var Pi={};function Zg(t,e,r){for(var a=function(l,f,h,d){return Ke(l,f,h,d)},n=e._private,i=n.rstyle.bezierPts,s=0;s<t.bezierProjPcts.length;s++){var o=t.bezierProjPcts[s];i.push({x:a(r[0],r[2],r[4],o),y:a(r[1],r[3],r[5],o)})}}Pi.storeEdgeProjections=function(t){var e=t._private,r=e.rscratch,a=r.edgeType;if(e.rstyle.bezierPts=null,e.rstyle.linePts=null,e.rstyle.haystackPts=null,a==="multibezier"||a==="bezier"||a==="self"||a==="compound"){e.rstyle.bezierPts=[];for(var n=0;n+5<r.allpts.length;n+=4)Zg(this,t,r.allpts.slice(n,n+6))}else if(a==="segments")for(var i=e.rstyle.linePts=[],n=0;n+1<r.allpts.length;n+=2)i.push({x:r.allpts[n],y:r.allpts[n+1]});else if(a==="haystack"){var s=r.haystackPts;e.rstyle.haystackPts=[{x:s[0],y:s[1]},{x:s[2],y:s[3]}]}e.rstyle.arrowWidth=this.getArrowWidth(t.pstyle("width").pfValue,t.pstyle("arrow-scale").value)*this.arrowShapeWidth};Pi.recalculateEdgeProjections=function(t){this.findEdgeControlPoints(t)};var Gt={};Gt.recalculateNodeLabelProjection=function(t){var e=t.pstyle("label").strValue;if(!jt(e)){var r,a,n=t._private,i=t.width(),s=t.height(),o=t.padding(),u=t.position(),l=t.pstyle("text-halign").strValue,f=t.pstyle("text-valign").strValue,h=n.rscratch,d=n.rstyle;switch(l){case"left":r=u.x-i/2-o;break;case"right":r=u.x+i/2+o;break;default:r=u.x}switch(f){case"top":a=u.y-s/2-o;break;case"bottom":a=u.y+s/2+o;break;default:a=u.y}h.labelX=r,h.labelY=a,d.labelX=r,d.labelY=a,this.calculateLabelAngles(t),this.applyLabelDimensions(t)}};var Tu=function(e,r){var a=Math.atan(r/e);return e===0&&a<0&&(a=a*-1),a},Cu=function(e,r){var a=r.x-e.x,n=r.y-e.y;return Tu(a,n)},Qg=function(e,r,a,n){var i=ga(0,n-.001,1),s=ga(0,n+.001,1),o=Rr(e,r,a,i),u=Rr(e,r,a,s);return Cu(o,u)};Gt.recalculateEdgeLabelProjections=function(t){var e,r=t._private,a=r.rscratch,n=this,i={mid:t.pstyle("label").strValue,source:t.pstyle("source-label").strValue,target:t.pstyle("target-label").strValue};if(i.mid||i.source||i.target){e={x:a.midX,y:a.midY};var s=function(h,d,c){Kt(r.rscratch,h,d,c),Kt(r.rstyle,h,d,c)};s("labelX",null,e.x),s("labelY",null,e.y);var o=Tu(a.midDispX,a.midDispY);s("labelAutoAngle",null,o);var u=function f(){if(f.cache)return f.cache;for(var h=[],d=0;d+5<a.allpts.length;d+=4){var c={x:a.allpts[d],y:a.allpts[d+1]},v={x:a.allpts[d+2],y:a.allpts[d+3]},p={x:a.allpts[d+4],y:a.allpts[d+5]};h.push({p0:c,p1:v,p2:p,startDist:0,length:0,segments:[]})}var g=r.rstyle.bezierPts,y=n.bezierProjPcts.length;function b(E,w,x,D,L){var A=gr(w,x),N=E.segments[E.segments.length-1],O={p0:w,p1:x,t0:D,t1:L,startDist:N?N.startDist+N.length:0,length:A};E.segments.push(O),E.length+=A}for(var m=0;m<h.length;m++){var T=h[m],C=h[m-1];C&&(T.startDist=C.startDist+C.length),b(T,T.p0,g[m*y],0,n.bezierProjPcts[0]);for(var S=0;S<y-1;S++)b(T,g[m*y+S],g[m*y+S+1],n.bezierProjPcts[S],n.bezierProjPcts[S+1]);b(T,g[m*y+y-1],T.p2,n.bezierProjPcts[y-1],1)}return f.cache=h},l=function(h){var d,c=h==="source";if(i[h]){var v=t.pstyle(h+"-text-offset").pfValue;switch(a.edgeType){case"self":case"compound":case"bezier":case"multibezier":{for(var p=u(),g,y=0,b=0,m=0;m<p.length;m++){for(var T=p[c?m:p.length-1-m],C=0;C<T.segments.length;C++){var S=T.segments[c?C:T.segments.length-1-C],E=m===p.length-1&&C===T.segments.length-1;if(y=b,b+=S.length,b>=v||E){g={cp:T,segment:S};break}}if(g)break}var w=g.cp,x=g.segment,D=(v-y)/x.length,L=x.t1-x.t0,A=c?x.t0+L*D:x.t1-L*D;A=ga(0,A,1),e=Rr(w.p0,w.p1,w.p2,A),d=Qg(w.p0,w.p1,w.p2,A);break}case"straight":case"segments":case"haystack":{for(var N=0,O,M,R,k,P=a.allpts.length,B=0;B+3<P&&(c?(R={x:a.allpts[B],y:a.allpts[B+1]},k={x:a.allpts[B+2],y:a.allpts[B+3]}):(R={x:a.allpts[P-2-B],y:a.allpts[P-1-B]},k={x:a.allpts[P-4-B],y:a.allpts[P-3-B]}),O=gr(R,k),M=N,N+=O,!(N>=v));B+=2);var z=v-M,G=z/O;G=ga(0,G,1),e=rh(R,k,G),d=Cu(R,k);break}}s("labelX",h,e.x),s("labelY",h,e.y),s("labelAutoAngle",h,d)}};l("source"),l("target"),this.applyLabelDimensions(t)}};Gt.applyLabelDimensions=function(t){this.applyPrefixedLabelDimensions(t),t.isEdge()&&(this.applyPrefixedLabelDimensions(t,"source"),this.applyPrefixedLabelDimensions(t,"target"))};Gt.applyPrefixedLabelDimensions=function(t,e){var r=t._private,a=this.getLabelText(t,e),n=this.calculateLabelDimensions(t,a),i=t.pstyle("line-height").pfValue,s=t.pstyle("text-wrap").strValue,o=At(r.rscratch,"labelWrapCachedLines",e)||[],u=s!=="wrap"?1:Math.max(o.length,1),l=n.height/u,f=l*i,h=n.width,d=n.height+(u-1)*(i-1)*l;Kt(r.rstyle,"labelWidth",e,h),Kt(r.rscratch,"labelWidth",e,h),Kt(r.rstyle,"labelHeight",e,d),Kt(r.rscratch,"labelHeight",e,d),Kt(r.rscratch,"labelLineHeight",e,f)};Gt.getLabelText=function(t,e){var r=t._private,a=e?e+"-":"",n=t.pstyle(a+"label").strValue,i=t.pstyle("text-transform").value,s=function(U,Y){return Y?(Kt(r.rscratch,U,e,Y),Y):At(r.rscratch,U,e)};if(!n)return"";i=="none"||(i=="uppercase"?n=n.toUpperCase():i=="lowercase"&&(n=n.toLowerCase()));var o=t.pstyle("text-wrap").value;if(o==="wrap"){var u=s("labelKey");if(u!=null&&s("labelWrapKey")===u)return s("labelWrapCachedText");for(var l="​",f=n.split(`
`),h=t.pstyle("text-max-width").pfValue,d=t.pstyle("text-overflow-wrap").value,c=d==="anywhere",v=[],p=/[\s\u200b]+|$/g,g=0;g<f.length;g++){var y=f[g],b=this.calculateLabelDimensions(t,y),m=b.width;if(c){var T=y.split("").join(l);y=T}if(m>h){var C=y.matchAll(p),S="",E=0,w=io(C),x;try{for(w.s();!(x=w.n()).done;){var D=x.value,L=D[0],A=y.substring(E,D.index);E=D.index+L.length;var N=S.length===0?A:S+A+L,O=this.calculateLabelDimensions(t,N),M=O.width;M<=h?S+=A+L:(S&&v.push(S),S=A+L)}}catch(F){w.e(F)}finally{w.f()}S.match(/^[\s\u200b]+$/)||v.push(S)}else v.push(y)}s("labelWrapCachedLines",v),n=s("labelWrapCachedText",v.join(`
`)),s("labelWrapKey",u)}else if(o==="ellipsis"){var R=t.pstyle("text-max-width").pfValue,k="",P="…",B=!1;if(this.calculateLabelDimensions(t,n).width<R)return n;for(var z=0;z<n.length;z++){var G=this.calculateLabelDimensions(t,k+n[z]+P).width;if(G>R)break;k+=n[z],z===n.length-1&&(B=!0)}return B||(k+=P),k}return n};Gt.getLabelJustification=function(t){var e=t.pstyle("text-justification").strValue,r=t.pstyle("text-halign").strValue;if(e==="auto")if(t.isNode())switch(r){case"left":return"right";case"right":return"left";default:return"center"}else return"center";else return e};Gt.calculateLabelDimensions=function(t,e){var r=this,a=r.cy.window(),n=a.document,i=dr(e,t._private.labelDimsKey),s=r.labelDimCache||(r.labelDimCache=[]),o=s[i];if(o!=null)return o;var u=0,l=t.pstyle("font-style").strValue,f=t.pstyle("font-size").pfValue,h=t.pstyle("font-family").strValue,d=t.pstyle("font-weight").strValue,c=this.labelCalcCanvas,v=this.labelCalcCanvasContext;if(!c){c=this.labelCalcCanvas=n.createElement("canvas"),v=this.labelCalcCanvasContext=c.getContext("2d");var p=c.style;p.position="absolute",p.left="-9999px",p.top="-9999px",p.zIndex="-1",p.visibility="hidden",p.pointerEvents="none"}v.font="".concat(l," ").concat(d," ").concat(f,"px ").concat(h);for(var g=0,y=0,b=e.split(`
`),m=0;m<b.length;m++){var T=b[m],C=v.measureText(T),S=Math.ceil(C.width),E=f;g=Math.max(S,g),y+=E}return g+=u,y+=u,s[i]={width:g,height:y}};Gt.calculateLabelAngle=function(t,e){var r=t._private,a=r.rscratch,n=t.isEdge(),i=e?e+"-":"",s=t.pstyle(i+"text-rotation"),o=s.strValue;return o==="none"?0:n&&o==="autorotate"?a.labelAutoAngle:o==="autorotate"?0:s.pfValue};Gt.calculateLabelAngles=function(t){var e=this,r=t.isEdge(),a=t._private,n=a.rscratch;n.labelAngle=e.calculateLabelAngle(t),r&&(n.sourceLabelAngle=e.calculateLabelAngle(t,"source"),n.targetLabelAngle=e.calculateLabelAngle(t,"target"))};var Du={},_s=28,Hs=!1;Du.getNodeShape=function(t){var e=this,r=t.pstyle("shape").value;if(r==="cutrectangle"&&(t.width()<_s||t.height()<_s))return Hs||(Ne("The `cutrectangle` node shape can not be used at small sizes so `rectangle` is used instead"),Hs=!0),"rectangle";if(t.isParent())return r==="rectangle"||r==="roundrectangle"||r==="round-rectangle"||r==="cutrectangle"||r==="cut-rectangle"||r==="barrel"?r:"rectangle";if(r==="polygon"){var a=t.pstyle("shape-polygon-points").value;return e.nodeShapes.makePolygon(a).name}return r};var On={};On.registerCalculationListeners=function(){var t=this.cy,e=t.collection(),r=this,a=function(s){var o=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!0;if(e.merge(s),o)for(var u=0;u<s.length;u++){var l=s[u],f=l._private,h=f.rstyle;h.clean=!1,h.cleanConnected=!1}};r.binder(t).on("bounds.* dirty.*",function(s){var o=s.target;a(o)}).on("style.* background.*",function(s){var o=s.target;a(o,!1)});var n=function(s){if(s){var o=r.onUpdateEleCalcsFns;e.cleanStyle();for(var u=0;u<e.length;u++){var l=e[u],f=l._private.rstyle;l.isNode()&&!f.cleanConnected&&(a(l.connectedEdges()),f.cleanConnected=!0)}if(o)for(var h=0;h<o.length;h++){var d=o[h];d(s,e)}r.recalculateRenderedStyle(e),e=t.collection()}};r.flushRenderedStyleQueue=function(){n(!0)},r.beforeRender(n,r.beforeRenderPriorities.eleCalcs)};On.onUpdateEleCalcs=function(t){var e=this.onUpdateEleCalcsFns=this.onUpdateEleCalcsFns||[];e.push(t)};On.recalculateRenderedStyle=function(t,e){var r=function(T){return T._private.rstyle.cleanConnected},a=[],n=[];if(!this.destroyed){e===void 0&&(e=!0);for(var i=0;i<t.length;i++){var s=t[i],o=s._private,u=o.rstyle;s.isEdge()&&(!r(s.source())||!r(s.target()))&&(u.clean=!1),!(e&&u.clean||s.removed())&&s.pstyle("display").value!=="none"&&(o.group==="nodes"?n.push(s):a.push(s),u.clean=!0)}for(var l=0;l<n.length;l++){var f=n[l],h=f._private,d=h.rstyle,c=f.position();this.recalculateNodeLabelProjection(f),d.nodeX=c.x,d.nodeY=c.y,d.nodeW=f.pstyle("width").pfValue,d.nodeH=f.pstyle("height").pfValue}this.recalculateEdgeProjections(a);for(var v=0;v<a.length;v++){var p=a[v],g=p._private,y=g.rstyle,b=g.rscratch;y.srcX=b.arrowStartX,y.srcY=b.arrowStartY,y.tgtX=b.arrowEndX,y.tgtY=b.arrowEndY,y.midX=b.midX,y.midY=b.midY,y.labelAngle=b.labelAngle,y.sourceLabelAngle=b.sourceLabelAngle,y.targetLabelAngle=b.targetLabelAngle}}};var Nn={};Nn.updateCachedGrabbedEles=function(){var t=this.cachedZSortedEles;if(t){t.drag=[],t.nondrag=[];for(var e=[],r=0;r<t.length;r++){var a=t[r],n=a._private.rscratch;a.grabbed()&&!a.isParent()?e.push(a):n.inDragLayer?t.drag.push(a):t.nondrag.push(a)}for(var r=0;r<e.length;r++){var a=e[r];t.drag.push(a)}}};Nn.invalidateCachedZSortedEles=function(){this.cachedZSortedEles=null};Nn.getCachedZSortedEles=function(t){if(t||!this.cachedZSortedEles){var e=this.cy.mutableElements().toArray();e.sort(uu),e.interactive=e.filter(function(r){return r.interactive()}),this.cachedZSortedEles=e,this.updateCachedGrabbedEles()}else e=this.cachedZSortedEles;return e};var Su={};[wr,hn,ut,Aa,Pi,Gt,Du,On,Nn].forEach(function(t){be(Su,t)});var Lu={};Lu.getCachedImage=function(t,e,r){var a=this,n=a.imageCache=a.imageCache||{},i=n[t];if(i)return i.image.complete||i.image.addEventListener("load",r),i.image;i=n[t]=n[t]||{};var s=i.image=new Image;s.addEventListener("load",r),s.addEventListener("error",function(){s.error=!0});var o="data:",u=t.substring(0,o.length).toLowerCase()===o;return u||(e=e==="null"?null:e,s.crossOrigin=e),s.src=t,s};var Wr={};Wr.registerBinding=function(t,e,r,a){var n=Array.prototype.slice.apply(arguments,[1]),i=this.binder(t);return i.on.apply(i,n)};Wr.binder=function(t){var e=this,r=e.cy.window(),a=t===r||t===r.document||t===r.document.body||wl(t);if(e.supportsPassiveEvents==null){var n=!1;try{var i=Object.defineProperty({},"passive",{get:function(){return n=!0,!0}});r.addEventListener("test",null,i)}catch{}e.supportsPassiveEvents=n}var s=function(u,l,f){var h=Array.prototype.slice.call(arguments);return a&&e.supportsPassiveEvents&&(h[2]={capture:f??!1,passive:!1,once:!1}),e.bindings.push({target:t,args:h}),(t.addEventListener||t.on).apply(t,h),this};return{on:s,addEventListener:s,addListener:s,bind:s}};Wr.nodeIsDraggable=function(t){return t&&t.isNode()&&!t.locked()&&t.grabbable()};Wr.nodeIsGrabbable=function(t){return this.nodeIsDraggable(t)&&t.interactive()};Wr.load=function(){var t=this,e=t.cy.window(),r=function(I){return I.selected()},a=function(I,$,J,q){I==null&&(I=t.cy);for(var X=0;X<$.length;X++){var ae=$[X];I.emit({originalEvent:J,type:ae,position:q})}},n=function(I){return I.shiftKey||I.metaKey||I.ctrlKey},i=function(I,$){var J=!0;if(t.cy.hasCompoundNodes()&&I&&I.pannable())for(var q=0;$&&q<$.length;q++){var I=$[q];if(I.isNode()&&I.isParent()&&!I.pannable()){J=!1;break}}else J=!0;return J},s=function(I){I[0]._private.grabbed=!0},o=function(I){I[0]._private.grabbed=!1},u=function(I){I[0]._private.rscratch.inDragLayer=!0},l=function(I){I[0]._private.rscratch.inDragLayer=!1},f=function(I){I[0]._private.rscratch.isGrabTarget=!0},h=function(I){I[0]._private.rscratch.isGrabTarget=!1},d=function(I,$){var J=$.addToList,q=J.has(I);!q&&I.grabbable()&&!I.locked()&&(J.merge(I),s(I))},c=function(I,$){if(I.cy().hasCompoundNodes()&&!($.inDragLayer==null&&$.addToList==null)){var J=I.descendants();$.inDragLayer&&(J.forEach(u),J.connectedEdges().forEach(u)),$.addToList&&d(J,$)}},v=function(I,$){$=$||{};var J=I.cy().hasCompoundNodes();$.inDragLayer&&(I.forEach(u),I.neighborhood().stdFilter(function(q){return!J||q.isEdge()}).forEach(u)),$.addToList&&I.forEach(function(q){d(q,$)}),c(I,$),y(I,{inDragLayer:$.inDragLayer}),t.updateCachedGrabbedEles()},p=v,g=function(I){I&&(t.getCachedZSortedEles().forEach(function($){o($),l($),h($)}),t.updateCachedGrabbedEles())},y=function(I,$){if(!($.inDragLayer==null&&$.addToList==null)&&I.cy().hasCompoundNodes()){var J=I.ancestors().orphans();if(!J.same(I)){var q=J.descendants().spawnSelf().merge(J).unmerge(I).unmerge(I.descendants()),X=q.connectedEdges();$.inDragLayer&&(X.forEach(u),q.forEach(u)),$.addToList&&q.forEach(function(ae){d(ae,$)})}}},b=function(){document.activeElement!=null&&document.activeElement.blur!=null&&document.activeElement.blur()},m=typeof MutationObserver<"u",T=typeof ResizeObserver<"u";m?(t.removeObserver=new MutationObserver(function(ee){for(var I=0;I<ee.length;I++){var $=ee[I],J=$.removedNodes;if(J)for(var q=0;q<J.length;q++){var X=J[q];if(X===t.container){t.destroy();break}}}}),t.container.parentNode&&t.removeObserver.observe(t.container.parentNode,{childList:!0})):t.registerBinding(t.container,"DOMNodeRemoved",function(ee){t.destroy()});var C=gn(function(){t.cy.resize()},100);m&&(t.styleObserver=new MutationObserver(C),t.styleObserver.observe(t.container,{attributes:!0})),t.registerBinding(e,"resize",C),T&&(t.resizeObserver=new ResizeObserver(C),t.resizeObserver.observe(t.container));var S=function(I,$){for(;I!=null;)$(I),I=I.parentNode},E=function(){t.invalidateContainerClientCoordsCache()};S(t.container,function(ee){t.registerBinding(ee,"transitionend",E),t.registerBinding(ee,"animationend",E),t.registerBinding(ee,"scroll",E)}),t.registerBinding(t.container,"contextmenu",function(ee){ee.preventDefault()});var w=function(){return t.selection[4]!==0},x=function(I){for(var $=t.findContainerClientCoords(),J=$[0],q=$[1],X=$[2],ae=$[3],Z=I.touches?I.touches:[I],re=!1,ye=0;ye<Z.length;ye++){var me=Z[ye];if(J<=me.clientX&&me.clientX<=J+X&&q<=me.clientY&&me.clientY<=q+ae){re=!0;break}}if(!re)return!1;for(var he=t.container,Ee=I.target,le=Ee.parentNode,ge=!1;le;){if(le===he){ge=!0;break}le=le.parentNode}return!!ge};t.registerBinding(t.container,"mousedown",function(I){if(x(I)&&!(t.hoverData.which===1&&I.which!==1)){I.preventDefault(),b(),t.hoverData.capture=!0,t.hoverData.which=I.which;var $=t.cy,J=[I.clientX,I.clientY],q=t.projectIntoViewport(J[0],J[1]),X=t.selection,ae=t.findNearestElements(q[0],q[1],!0,!1),Z=ae[0],re=t.dragData.possibleDragElements;t.hoverData.mdownPos=q,t.hoverData.mdownGPos=J;var ye=function(){t.hoverData.tapholdCancelled=!1,clearTimeout(t.hoverData.tapholdTimeout),t.hoverData.tapholdTimeout=setTimeout(function(){if(!t.hoverData.tapholdCancelled){var Fe=t.hoverData.down;Fe?Fe.emit({originalEvent:I,type:"taphold",position:{x:q[0],y:q[1]}}):$.emit({originalEvent:I,type:"taphold",position:{x:q[0],y:q[1]}})}},t.tapholdDuration)};if(I.which==3){t.hoverData.cxtStarted=!0;var me={originalEvent:I,type:"cxttapstart",position:{x:q[0],y:q[1]}};Z?(Z.activate(),Z.emit(me),t.hoverData.down=Z):$.emit(me),t.hoverData.downTime=new Date().getTime(),t.hoverData.cxtDragged=!1}else if(I.which==1){Z&&Z.activate();{if(Z!=null&&t.nodeIsGrabbable(Z)){var he=function(Fe){return{originalEvent:I,type:Fe,position:{x:q[0],y:q[1]}}},Ee=function(Fe){Fe.emit(he("grab"))};if(f(Z),!Z.selected())re=t.dragData.possibleDragElements=$.collection(),p(Z,{addToList:re}),Z.emit(he("grabon")).emit(he("grab"));else{re=t.dragData.possibleDragElements=$.collection();var le=$.$(function(ge){return ge.isNode()&&ge.selected()&&t.nodeIsGrabbable(ge)});v(le,{addToList:re}),Z.emit(he("grabon")),le.forEach(Ee)}t.redrawHint("eles",!0),t.redrawHint("drag",!0)}t.hoverData.down=Z,t.hoverData.downs=ae,t.hoverData.downTime=new Date().getTime()}a(Z,["mousedown","tapstart","vmousedown"],I,{x:q[0],y:q[1]}),Z==null?(X[4]=1,t.data.bgActivePosistion={x:q[0],y:q[1]},t.redrawHint("select",!0),t.redraw()):Z.pannable()&&(X[4]=1),ye()}X[0]=X[2]=q[0],X[1]=X[3]=q[1]}},!1),t.registerBinding(e,"mousemove",function(I){var $=t.hoverData.capture;if(!(!$&&!x(I))){var J=!1,q=t.cy,X=q.zoom(),ae=[I.clientX,I.clientY],Z=t.projectIntoViewport(ae[0],ae[1]),re=t.hoverData.mdownPos,ye=t.hoverData.mdownGPos,me=t.selection,he=null;!t.hoverData.draggingEles&&!t.hoverData.dragging&&!t.hoverData.selecting&&(he=t.findNearestElement(Z[0],Z[1],!0,!1));var Ee=t.hoverData.last,le=t.hoverData.down,ge=[Z[0]-me[2],Z[1]-me[3]],Fe=t.dragData.possibleDragElements,Me;if(ye){var lt=ae[0]-ye[0],Ze=lt*lt,Ue=ae[1]-ye[1],ct=Ue*Ue,Qe=Ze+ct;t.hoverData.isOverThresholdDrag=Me=Qe>=t.desktopTapThreshold2}var ft=n(I);Me&&(t.hoverData.tapholdCancelled=!0);var xt=function(){var Mt=t.hoverData.dragDelta=t.hoverData.dragDelta||[];Mt.length===0?(Mt.push(ge[0]),Mt.push(ge[1])):(Mt[0]+=ge[0],Mt[1]+=ge[1])};J=!0,a(he,["mousemove","vmousemove","tapdrag"],I,{x:Z[0],y:Z[1]});var mt=function(){t.data.bgActivePosistion=void 0,t.hoverData.selecting||q.emit({originalEvent:I,type:"boxstart",position:{x:Z[0],y:Z[1]}}),me[4]=1,t.hoverData.selecting=!0,t.redrawHint("select",!0),t.redraw()};if(t.hoverData.which===3){if(Me){var vt={originalEvent:I,type:"cxtdrag",position:{x:Z[0],y:Z[1]}};le?le.emit(vt):q.emit(vt),t.hoverData.cxtDragged=!0,(!t.hoverData.cxtOver||he!==t.hoverData.cxtOver)&&(t.hoverData.cxtOver&&t.hoverData.cxtOver.emit({originalEvent:I,type:"cxtdragout",position:{x:Z[0],y:Z[1]}}),t.hoverData.cxtOver=he,he&&he.emit({originalEvent:I,type:"cxtdragover",position:{x:Z[0],y:Z[1]}}))}}else if(t.hoverData.dragging){if(J=!0,q.panningEnabled()&&q.userPanningEnabled()){var It;if(t.hoverData.justStartedPan){var Vt=t.hoverData.mdownPos;It={x:(Z[0]-Vt[0])*X,y:(Z[1]-Vt[1])*X},t.hoverData.justStartedPan=!1}else It={x:ge[0]*X,y:ge[1]*X};q.panBy(It),q.emit("dragpan"),t.hoverData.dragged=!0}Z=t.projectIntoViewport(I.clientX,I.clientY)}else if(me[4]==1&&(le==null||le.pannable())){if(Me){if(!t.hoverData.dragging&&q.boxSelectionEnabled()&&(ft||!q.panningEnabled()||!q.userPanningEnabled()))mt();else if(!t.hoverData.selecting&&q.panningEnabled()&&q.userPanningEnabled()){var Tt=i(le,t.hoverData.downs);Tt&&(t.hoverData.dragging=!0,t.hoverData.justStartedPan=!0,me[4]=0,t.data.bgActivePosistion=Ir(re),t.redrawHint("select",!0),t.redraw())}le&&le.pannable()&&le.active()&&le.unactivate()}}else{if(le&&le.pannable()&&le.active()&&le.unactivate(),(!le||!le.grabbed())&&he!=Ee&&(Ee&&a(Ee,["mouseout","tapdragout"],I,{x:Z[0],y:Z[1]}),he&&a(he,["mouseover","tapdragover"],I,{x:Z[0],y:Z[1]}),t.hoverData.last=he),le)if(Me){if(q.boxSelectionEnabled()&&ft)le&&le.grabbed()&&(g(Fe),le.emit("freeon"),Fe.emit("free"),t.dragData.didDrag&&(le.emit("dragfreeon"),Fe.emit("dragfree"))),mt();else if(le&&le.grabbed()&&t.nodeIsDraggable(le)){var $e=!t.dragData.didDrag;$e&&t.redrawHint("eles",!0),t.dragData.didDrag=!0,t.hoverData.draggingEles||v(Fe,{inDragLayer:!0});var We={x:0,y:0};if(ie(ge[0])&&ie(ge[1])&&(We.x+=ge[0],We.y+=ge[1],$e)){var at=t.hoverData.dragDelta;at&&ie(at[0])&&ie(at[1])&&(We.x+=at[0],We.y+=at[1])}t.hoverData.draggingEles=!0,Fe.silentShift(We).emit("position drag"),t.redrawHint("drag",!0),t.redraw()}}else xt();J=!0}if(me[2]=Z[0],me[3]=Z[1],J)return I.stopPropagation&&I.stopPropagation(),I.preventDefault&&I.preventDefault(),!1}},!1);var D,L,A;t.registerBinding(e,"mouseup",function(I){if(!(t.hoverData.which===1&&I.which!==1&&t.hoverData.capture)){var $=t.hoverData.capture;if($){t.hoverData.capture=!1;var J=t.cy,q=t.projectIntoViewport(I.clientX,I.clientY),X=t.selection,ae=t.findNearestElement(q[0],q[1],!0,!1),Z=t.dragData.possibleDragElements,re=t.hoverData.down,ye=n(I);if(t.data.bgActivePosistion&&(t.redrawHint("select",!0),t.redraw()),t.hoverData.tapholdCancelled=!0,t.data.bgActivePosistion=void 0,re&&re.unactivate(),t.hoverData.which===3){var me={originalEvent:I,type:"cxttapend",position:{x:q[0],y:q[1]}};if(re?re.emit(me):J.emit(me),!t.hoverData.cxtDragged){var he={originalEvent:I,type:"cxttap",position:{x:q[0],y:q[1]}};re?re.emit(he):J.emit(he)}t.hoverData.cxtDragged=!1,t.hoverData.which=null}else if(t.hoverData.which===1){if(a(ae,["mouseup","tapend","vmouseup"],I,{x:q[0],y:q[1]}),!t.dragData.didDrag&&!t.hoverData.dragged&&!t.hoverData.selecting&&!t.hoverData.isOverThresholdDrag&&(a(re,["click","tap","vclick"],I,{x:q[0],y:q[1]}),L=!1,I.timeStamp-A<=J.multiClickDebounceTime()?(D&&clearTimeout(D),L=!0,A=null,a(re,["dblclick","dbltap","vdblclick"],I,{x:q[0],y:q[1]})):(D=setTimeout(function(){L||a(re,["oneclick","onetap","voneclick"],I,{x:q[0],y:q[1]})},J.multiClickDebounceTime()),A=I.timeStamp)),re==null&&!t.dragData.didDrag&&!t.hoverData.selecting&&!t.hoverData.dragged&&!n(I)&&(J.$(r).unselect(["tapunselect"]),Z.length>0&&t.redrawHint("eles",!0),t.dragData.possibleDragElements=Z=J.collection()),ae==re&&!t.dragData.didDrag&&!t.hoverData.selecting&&ae!=null&&ae._private.selectable&&(t.hoverData.dragging||(J.selectionType()==="additive"||ye?ae.selected()?ae.unselect(["tapunselect"]):ae.select(["tapselect"]):ye||(J.$(r).unmerge(ae).unselect(["tapunselect"]),ae.select(["tapselect"]))),t.redrawHint("eles",!0)),t.hoverData.selecting){var Ee=J.collection(t.getAllInBox(X[0],X[1],X[2],X[3]));t.redrawHint("select",!0),Ee.length>0&&t.redrawHint("eles",!0),J.emit({type:"boxend",originalEvent:I,position:{x:q[0],y:q[1]}});var le=function(Me){return Me.selectable()&&!Me.selected()};J.selectionType()==="additive"||ye||J.$(r).unmerge(Ee).unselect(),Ee.emit("box").stdFilter(le).select().emit("boxselect"),t.redraw()}if(t.hoverData.dragging&&(t.hoverData.dragging=!1,t.redrawHint("select",!0),t.redrawHint("eles",!0),t.redraw()),!X[4]){t.redrawHint("drag",!0),t.redrawHint("eles",!0);var ge=re&&re.grabbed();g(Z),ge&&(re.emit("freeon"),Z.emit("free"),t.dragData.didDrag&&(re.emit("dragfreeon"),Z.emit("dragfree")))}}X[4]=0,t.hoverData.down=null,t.hoverData.cxtStarted=!1,t.hoverData.draggingEles=!1,t.hoverData.selecting=!1,t.hoverData.isOverThresholdDrag=!1,t.dragData.didDrag=!1,t.hoverData.dragged=!1,t.hoverData.dragDelta=[],t.hoverData.mdownPos=null,t.hoverData.mdownGPos=null,t.hoverData.which=null}}},!1);var N=function(I){if(!t.scrollingPage){var $=t.cy,J=$.zoom(),q=$.pan(),X=t.projectIntoViewport(I.clientX,I.clientY),ae=[X[0]*J+q.x,X[1]*J+q.y];if(t.hoverData.draggingEles||t.hoverData.dragging||t.hoverData.cxtStarted||w()){I.preventDefault();return}if($.panningEnabled()&&$.userPanningEnabled()&&$.zoomingEnabled()&&$.userZoomingEnabled()){I.preventDefault(),t.data.wheelZooming=!0,clearTimeout(t.data.wheelTimeout),t.data.wheelTimeout=setTimeout(function(){t.data.wheelZooming=!1,t.redrawHint("eles",!0),t.redraw()},150);var Z;I.deltaY!=null?Z=I.deltaY/-250:I.wheelDeltaY!=null?Z=I.wheelDeltaY/1e3:Z=I.wheelDelta/1e3,Z=Z*t.wheelSensitivity;var re=I.deltaMode===1;re&&(Z*=33);var ye=$.zoom()*Math.pow(10,Z);I.type==="gesturechange"&&(ye=t.gestureStartZoom*I.scale),$.zoom({level:ye,renderedPosition:{x:ae[0],y:ae[1]}}),$.emit(I.type==="gesturechange"?"pinchzoom":"scrollzoom")}}};t.registerBinding(t.container,"wheel",N,!0),t.registerBinding(e,"scroll",function(I){t.scrollingPage=!0,clearTimeout(t.scrollingPageTimeout),t.scrollingPageTimeout=setTimeout(function(){t.scrollingPage=!1},250)},!0),t.registerBinding(t.container,"gesturestart",function(I){t.gestureStartZoom=t.cy.zoom(),t.hasTouchStarted||I.preventDefault()},!0),t.registerBinding(t.container,"gesturechange",function(ee){t.hasTouchStarted||N(ee)},!0),t.registerBinding(t.container,"mouseout",function(I){var $=t.projectIntoViewport(I.clientX,I.clientY);t.cy.emit({originalEvent:I,type:"mouseout",position:{x:$[0],y:$[1]}})},!1),t.registerBinding(t.container,"mouseover",function(I){var $=t.projectIntoViewport(I.clientX,I.clientY);t.cy.emit({originalEvent:I,type:"mouseover",position:{x:$[0],y:$[1]}})},!1);var O,M,R,k,P,B,z,G,F,U,Y,W,K,j=function(I,$,J,q){return Math.sqrt((J-I)*(J-I)+(q-$)*(q-$))},_=function(I,$,J,q){return(J-I)*(J-I)+(q-$)*(q-$)},V;t.registerBinding(t.container,"touchstart",V=function(I){if(t.hasTouchStarted=!0,!!x(I)){b(),t.touchData.capture=!0,t.data.bgActivePosistion=void 0;var $=t.cy,J=t.touchData.now,q=t.touchData.earlier;if(I.touches[0]){var X=t.projectIntoViewport(I.touches[0].clientX,I.touches[0].clientY);J[0]=X[0],J[1]=X[1]}if(I.touches[1]){var X=t.projectIntoViewport(I.touches[1].clientX,I.touches[1].clientY);J[2]=X[0],J[3]=X[1]}if(I.touches[2]){var X=t.projectIntoViewport(I.touches[2].clientX,I.touches[2].clientY);J[4]=X[0],J[5]=X[1]}if(I.touches[1]){t.touchData.singleTouchMoved=!0,g(t.dragData.touchDragEles);var ae=t.findContainerClientCoords();F=ae[0],U=ae[1],Y=ae[2],W=ae[3],O=I.touches[0].clientX-F,M=I.touches[0].clientY-U,R=I.touches[1].clientX-F,k=I.touches[1].clientY-U,K=0<=O&&O<=Y&&0<=R&&R<=Y&&0<=M&&M<=W&&0<=k&&k<=W;var Z=$.pan(),re=$.zoom();P=j(O,M,R,k),B=_(O,M,R,k),z=[(O+R)/2,(M+k)/2],G=[(z[0]-Z.x)/re,(z[1]-Z.y)/re];var ye=200,me=ye*ye;if(B<me&&!I.touches[2]){var he=t.findNearestElement(J[0],J[1],!0,!0),Ee=t.findNearestElement(J[2],J[3],!0,!0);he&&he.isNode()?(he.activate().emit({originalEvent:I,type:"cxttapstart",position:{x:J[0],y:J[1]}}),t.touchData.start=he):Ee&&Ee.isNode()?(Ee.activate().emit({originalEvent:I,type:"cxttapstart",position:{x:J[0],y:J[1]}}),t.touchData.start=Ee):$.emit({originalEvent:I,type:"cxttapstart",position:{x:J[0],y:J[1]}}),t.touchData.start&&(t.touchData.start._private.grabbed=!1),t.touchData.cxt=!0,t.touchData.cxtDragged=!1,t.data.bgActivePosistion=void 0,t.redraw();return}}if(I.touches[2])$.boxSelectionEnabled()&&I.preventDefault();else if(!I.touches[1]){if(I.touches[0]){var le=t.findNearestElements(J[0],J[1],!0,!0),ge=le[0];if(ge!=null&&(ge.activate(),t.touchData.start=ge,t.touchData.starts=le,t.nodeIsGrabbable(ge))){var Fe=t.dragData.touchDragEles=$.collection(),Me=null;t.redrawHint("eles",!0),t.redrawHint("drag",!0),ge.selected()?(Me=$.$(function(Qe){return Qe.selected()&&t.nodeIsGrabbable(Qe)}),v(Me,{addToList:Fe})):p(ge,{addToList:Fe}),f(ge);var lt=function(ft){return{originalEvent:I,type:ft,position:{x:J[0],y:J[1]}}};ge.emit(lt("grabon")),Me?Me.forEach(function(Qe){Qe.emit(lt("grab"))}):ge.emit(lt("grab"))}a(ge,["touchstart","tapstart","vmousedown"],I,{x:J[0],y:J[1]}),ge==null&&(t.data.bgActivePosistion={x:X[0],y:X[1]},t.redrawHint("select",!0),t.redraw()),t.touchData.singleTouchMoved=!1,t.touchData.singleTouchStartTime=+new Date,clearTimeout(t.touchData.tapholdTimeout),t.touchData.tapholdTimeout=setTimeout(function(){t.touchData.singleTouchMoved===!1&&!t.pinching&&!t.touchData.selecting&&a(t.touchData.start,["taphold"],I,{x:J[0],y:J[1]})},t.tapholdDuration)}}if(I.touches.length>=1){for(var Ze=t.touchData.startPosition=[null,null,null,null,null,null],Ue=0;Ue<J.length;Ue++)Ze[Ue]=q[Ue]=J[Ue];var ct=I.touches[0];t.touchData.startGPosition=[ct.clientX,ct.clientY]}}},!1);var H;t.registerBinding(e,"touchmove",H=function(I){var $=t.touchData.capture;if(!(!$&&!x(I))){var J=t.selection,q=t.cy,X=t.touchData.now,ae=t.touchData.earlier,Z=q.zoom();if(I.touches[0]){var re=t.projectIntoViewport(I.touches[0].clientX,I.touches[0].clientY);X[0]=re[0],X[1]=re[1]}if(I.touches[1]){var re=t.projectIntoViewport(I.touches[1].clientX,I.touches[1].clientY);X[2]=re[0],X[3]=re[1]}if(I.touches[2]){var re=t.projectIntoViewport(I.touches[2].clientX,I.touches[2].clientY);X[4]=re[0],X[5]=re[1]}var ye=t.touchData.startGPosition,me;if($&&I.touches[0]&&ye){for(var he=[],Ee=0;Ee<X.length;Ee++)he[Ee]=X[Ee]-ae[Ee];var le=I.touches[0].clientX-ye[0],ge=le*le,Fe=I.touches[0].clientY-ye[1],Me=Fe*Fe,lt=ge+Me;me=lt>=t.touchTapThreshold2}if($&&t.touchData.cxt){I.preventDefault();var Ze=I.touches[0].clientX-F,Ue=I.touches[0].clientY-U,ct=I.touches[1].clientX-F,Qe=I.touches[1].clientY-U,ft=_(Ze,Ue,ct,Qe),xt=ft/B,mt=150,vt=mt*mt,It=1.5,Vt=It*It;if(xt>=Vt||ft>=vt){t.touchData.cxt=!1,t.data.bgActivePosistion=void 0,t.redrawHint("select",!0);var Tt={originalEvent:I,type:"cxttapend",position:{x:X[0],y:X[1]}};t.touchData.start?(t.touchData.start.unactivate().emit(Tt),t.touchData.start=null):q.emit(Tt)}}if($&&t.touchData.cxt){var Tt={originalEvent:I,type:"cxtdrag",position:{x:X[0],y:X[1]}};t.data.bgActivePosistion=void 0,t.redrawHint("select",!0),t.touchData.start?t.touchData.start.emit(Tt):q.emit(Tt),t.touchData.start&&(t.touchData.start._private.grabbed=!1),t.touchData.cxtDragged=!0;var $e=t.findNearestElement(X[0],X[1],!0,!0);(!t.touchData.cxtOver||$e!==t.touchData.cxtOver)&&(t.touchData.cxtOver&&t.touchData.cxtOver.emit({originalEvent:I,type:"cxtdragout",position:{x:X[0],y:X[1]}}),t.touchData.cxtOver=$e,$e&&$e.emit({originalEvent:I,type:"cxtdragover",position:{x:X[0],y:X[1]}}))}else if($&&I.touches[2]&&q.boxSelectionEnabled())I.preventDefault(),t.data.bgActivePosistion=void 0,this.lastThreeTouch=+new Date,t.touchData.selecting||q.emit({originalEvent:I,type:"boxstart",position:{x:X[0],y:X[1]}}),t.touchData.selecting=!0,t.touchData.didSelect=!0,J[4]=1,!J||J.length===0||J[0]===void 0?(J[0]=(X[0]+X[2]+X[4])/3,J[1]=(X[1]+X[3]+X[5])/3,J[2]=(X[0]+X[2]+X[4])/3+1,J[3]=(X[1]+X[3]+X[5])/3+1):(J[2]=(X[0]+X[2]+X[4])/3,J[3]=(X[1]+X[3]+X[5])/3),t.redrawHint("select",!0),t.redraw();else if($&&I.touches[1]&&!t.touchData.didSelect&&q.zoomingEnabled()&&q.panningEnabled()&&q.userZoomingEnabled()&&q.userPanningEnabled()){I.preventDefault(),t.data.bgActivePosistion=void 0,t.redrawHint("select",!0);var We=t.dragData.touchDragEles;if(We){t.redrawHint("drag",!0);for(var at=0;at<We.length;at++){var Tr=We[at]._private;Tr.grabbed=!1,Tr.rscratch.inDragLayer=!1}}var Mt=t.touchData.start,Ze=I.touches[0].clientX-F,Ue=I.touches[0].clientY-U,ct=I.touches[1].clientX-F,Qe=I.touches[1].clientY-U,Gi=j(Ze,Ue,ct,Qe),qu=Gi/P;if(K){var Wu=Ze-O,Ku=Ue-M,Zu=ct-R,Qu=Qe-k,Ju=(Wu+Zu)/2,ju=(Ku+Qu)/2,Qr=q.zoom(),In=Qr*qu,Ia=q.pan(),zi=G[0]*Qr+Ia.x,Vi=G[1]*Qr+Ia.y,el={x:-In/Qr*(zi-Ia.x-Ju)+zi,y:-In/Qr*(Vi-Ia.y-ju)+Vi};if(Mt&&Mt.active()){var We=t.dragData.touchDragEles;g(We),t.redrawHint("drag",!0),t.redrawHint("eles",!0),Mt.unactivate().emit("freeon"),We.emit("free"),t.dragData.didDrag&&(Mt.emit("dragfreeon"),We.emit("dragfree"))}q.viewport({zoom:In,pan:el,cancelOnFailedZoom:!0}),q.emit("pinchzoom"),P=Gi,O=Ze,M=Ue,R=ct,k=Qe,t.pinching=!0}if(I.touches[0]){var re=t.projectIntoViewport(I.touches[0].clientX,I.touches[0].clientY);X[0]=re[0],X[1]=re[1]}if(I.touches[1]){var re=t.projectIntoViewport(I.touches[1].clientX,I.touches[1].clientY);X[2]=re[0],X[3]=re[1]}if(I.touches[2]){var re=t.projectIntoViewport(I.touches[2].clientX,I.touches[2].clientY);X[4]=re[0],X[5]=re[1]}}else if(I.touches[0]&&!t.touchData.didSelect){var Ct=t.touchData.start,Mn=t.touchData.last,$e;if(!t.hoverData.draggingEles&&!t.swipePanning&&($e=t.findNearestElement(X[0],X[1],!0,!0)),$&&Ct!=null&&I.preventDefault(),$&&Ct!=null&&t.nodeIsDraggable(Ct))if(me){var We=t.dragData.touchDragEles,Ui=!t.dragData.didDrag;Ui&&v(We,{inDragLayer:!0}),t.dragData.didDrag=!0;var Jr={x:0,y:0};if(ie(he[0])&&ie(he[1])&&(Jr.x+=he[0],Jr.y+=he[1],Ui)){t.redrawHint("eles",!0);var Dt=t.touchData.dragDelta;Dt&&ie(Dt[0])&&ie(Dt[1])&&(Jr.x+=Dt[0],Jr.y+=Dt[1])}t.hoverData.draggingEles=!0,We.silentShift(Jr).emit("position drag"),t.redrawHint("drag",!0),t.touchData.startPosition[0]==ae[0]&&t.touchData.startPosition[1]==ae[1]&&t.redrawHint("eles",!0),t.redraw()}else{var Dt=t.touchData.dragDelta=t.touchData.dragDelta||[];Dt.length===0?(Dt.push(he[0]),Dt.push(he[1])):(Dt[0]+=he[0],Dt[1]+=he[1])}if(a(Ct||$e,["touchmove","tapdrag","vmousemove"],I,{x:X[0],y:X[1]}),(!Ct||!Ct.grabbed())&&$e!=Mn&&(Mn&&Mn.emit({originalEvent:I,type:"tapdragout",position:{x:X[0],y:X[1]}}),$e&&$e.emit({originalEvent:I,type:"tapdragover",position:{x:X[0],y:X[1]}})),t.touchData.last=$e,$)for(var at=0;at<X.length;at++)X[at]&&t.touchData.startPosition[at]&&me&&(t.touchData.singleTouchMoved=!0);if($&&(Ct==null||Ct.pannable())&&q.panningEnabled()&&q.userPanningEnabled()){var tl=i(Ct,t.touchData.starts);tl&&(I.preventDefault(),t.data.bgActivePosistion||(t.data.bgActivePosistion=Ir(t.touchData.startPosition)),t.swipePanning?(q.panBy({x:he[0]*Z,y:he[1]*Z}),q.emit("dragpan")):me&&(t.swipePanning=!0,q.panBy({x:le*Z,y:Fe*Z}),q.emit("dragpan"),Ct&&(Ct.unactivate(),t.redrawHint("select",!0),t.touchData.start=null)));var re=t.projectIntoViewport(I.touches[0].clientX,I.touches[0].clientY);X[0]=re[0],X[1]=re[1]}}for(var Ee=0;Ee<X.length;Ee++)ae[Ee]=X[Ee];$&&I.touches.length>0&&!t.hoverData.draggingEles&&!t.swipePanning&&t.data.bgActivePosistion!=null&&(t.data.bgActivePosistion=void 0,t.redrawHint("select",!0),t.redraw())}},!1);var Q;t.registerBinding(e,"touchcancel",Q=function(I){var $=t.touchData.start;t.touchData.capture=!1,$&&$.unactivate()});var ne,ce,te,se;if(t.registerBinding(e,"touchend",ne=function(I){var $=t.touchData.start,J=t.touchData.capture;if(J)I.touches.length===0&&(t.touchData.capture=!1),I.preventDefault();else return;var q=t.selection;t.swipePanning=!1,t.hoverData.draggingEles=!1;var X=t.cy,ae=X.zoom(),Z=t.touchData.now,re=t.touchData.earlier;if(I.touches[0]){var ye=t.projectIntoViewport(I.touches[0].clientX,I.touches[0].clientY);Z[0]=ye[0],Z[1]=ye[1]}if(I.touches[1]){var ye=t.projectIntoViewport(I.touches[1].clientX,I.touches[1].clientY);Z[2]=ye[0],Z[3]=ye[1]}if(I.touches[2]){var ye=t.projectIntoViewport(I.touches[2].clientX,I.touches[2].clientY);Z[4]=ye[0],Z[5]=ye[1]}$&&$.unactivate();var me;if(t.touchData.cxt){if(me={originalEvent:I,type:"cxttapend",position:{x:Z[0],y:Z[1]}},$?$.emit(me):X.emit(me),!t.touchData.cxtDragged){var he={originalEvent:I,type:"cxttap",position:{x:Z[0],y:Z[1]}};$?$.emit(he):X.emit(he)}t.touchData.start&&(t.touchData.start._private.grabbed=!1),t.touchData.cxt=!1,t.touchData.start=null,t.redraw();return}if(!I.touches[2]&&X.boxSelectionEnabled()&&t.touchData.selecting){t.touchData.selecting=!1;var Ee=X.collection(t.getAllInBox(q[0],q[1],q[2],q[3]));q[0]=void 0,q[1]=void 0,q[2]=void 0,q[3]=void 0,q[4]=0,t.redrawHint("select",!0),X.emit({type:"boxend",originalEvent:I,position:{x:Z[0],y:Z[1]}});var le=function(vt){return vt.selectable()&&!vt.selected()};Ee.emit("box").stdFilter(le).select().emit("boxselect"),Ee.nonempty()&&t.redrawHint("eles",!0),t.redraw()}if($!=null&&$.unactivate(),I.touches[2])t.data.bgActivePosistion=void 0,t.redrawHint("select",!0);else if(!I.touches[1]){if(!I.touches[0]){if(!I.touches[0]){t.data.bgActivePosistion=void 0,t.redrawHint("select",!0);var ge=t.dragData.touchDragEles;if($!=null){var Fe=$._private.grabbed;g(ge),t.redrawHint("drag",!0),t.redrawHint("eles",!0),Fe&&($.emit("freeon"),ge.emit("free"),t.dragData.didDrag&&($.emit("dragfreeon"),ge.emit("dragfree"))),a($,["touchend","tapend","vmouseup","tapdragout"],I,{x:Z[0],y:Z[1]}),$.unactivate(),t.touchData.start=null}else{var Me=t.findNearestElement(Z[0],Z[1],!0,!0);a(Me,["touchend","tapend","vmouseup","tapdragout"],I,{x:Z[0],y:Z[1]})}var lt=t.touchData.startPosition[0]-Z[0],Ze=lt*lt,Ue=t.touchData.startPosition[1]-Z[1],ct=Ue*Ue,Qe=Ze+ct,ft=Qe*ae*ae;t.touchData.singleTouchMoved||($||X.$(":selected").unselect(["tapunselect"]),a($,["tap","vclick"],I,{x:Z[0],y:Z[1]}),ce=!1,I.timeStamp-se<=X.multiClickDebounceTime()?(te&&clearTimeout(te),ce=!0,se=null,a($,["dbltap","vdblclick"],I,{x:Z[0],y:Z[1]})):(te=setTimeout(function(){ce||a($,["onetap","voneclick"],I,{x:Z[0],y:Z[1]})},X.multiClickDebounceTime()),se=I.timeStamp)),$!=null&&!t.dragData.didDrag&&$._private.selectable&&ft<t.touchTapThreshold2&&!t.pinching&&(X.selectionType()==="single"?(X.$(r).unmerge($).unselect(["tapunselect"]),$.select(["tapselect"])):$.selected()?$.unselect(["tapunselect"]):$.select(["tapselect"]),t.redrawHint("eles",!0)),t.touchData.singleTouchMoved=!0}}}for(var xt=0;xt<Z.length;xt++)re[xt]=Z[xt];t.dragData.didDrag=!1,I.touches.length===0&&(t.touchData.dragDelta=[],t.touchData.startPosition=[null,null,null,null,null,null],t.touchData.startGPosition=null,t.touchData.didSelect=!1),I.touches.length<2&&(I.touches.length===1&&(t.touchData.startGPosition=[I.touches[0].clientX,I.touches[0].clientY]),t.pinching=!1,t.redrawHint("eles",!0),t.redraw())},!1),typeof TouchEvent>"u"){var ue=[],ve=function(I){return{clientX:I.clientX,clientY:I.clientY,force:1,identifier:I.pointerId,pageX:I.pageX,pageY:I.pageY,radiusX:I.width/2,radiusY:I.height/2,screenX:I.screenX,screenY:I.screenY,target:I.target}},fe=function(I){return{event:I,touch:ve(I)}},pe=function(I){ue.push(fe(I))},Ae=function(I){for(var $=0;$<ue.length;$++){var J=ue[$];if(J.event.pointerId===I.pointerId){ue.splice($,1);return}}},xe=function(I){var $=ue.filter(function(J){return J.event.pointerId===I.pointerId})[0];$.event=I,$.touch=ve(I)},we=function(I){I.touches=ue.map(function($){return $.touch})},De=function(I){return I.pointerType==="mouse"||I.pointerType===4};t.registerBinding(t.container,"pointerdown",function(ee){De(ee)||(ee.preventDefault(),pe(ee),we(ee),V(ee))}),t.registerBinding(t.container,"pointerup",function(ee){De(ee)||(Ae(ee),we(ee),ne(ee))}),t.registerBinding(t.container,"pointercancel",function(ee){De(ee)||(Ae(ee),we(ee),Q(ee))}),t.registerBinding(t.container,"pointermove",function(ee){De(ee)||(ee.preventDefault(),xe(ee),we(ee),H(ee))})}};var Ht={};Ht.generatePolygon=function(t,e){return this.nodeShapes[t]={renderer:this,name:t,points:e,draw:function(a,n,i,s,o,u){this.renderer.nodeShapeImpl("polygon",a,n,i,s,o,this.points)},intersectLine:function(a,n,i,s,o,u,l,f){return pa(o,u,this.points,a,n,i/2,s/2,l)},checkPoint:function(a,n,i,s,o,u,l,f){return Yt(a,n,this.points,u,l,s,o,[0,-1],i)}}};Ht.generateEllipse=function(){return this.nodeShapes.ellipse={renderer:this,name:"ellipse",draw:function(e,r,a,n,i,s){this.renderer.nodeShapeImpl(this.name,e,r,a,n,i)},intersectLine:function(e,r,a,n,i,s,o,u){return gh(i,s,e,r,a/2+o,n/2+o)},checkPoint:function(e,r,a,n,i,s,o,u){return cr(e,r,n,i,s,o,a)}}};Ht.generateRoundPolygon=function(t,e){return this.nodeShapes[t]={renderer:this,name:t,points:e,getOrCreateCorners:function(a,n,i,s,o,u,l){if(u[l]!==void 0&&u[l+"-cx"]===a&&u[l+"-cy"]===n)return u[l];u[l]=new Array(e.length/2),u[l+"-cx"]=a,u[l+"-cy"]=n;var f=i/2,h=s/2;o=o==="auto"?No(i,s):o;for(var d=new Array(e.length/2),c=0;c<e.length/2;c++)d[c]={x:a+f*e[c*2],y:n+h*e[c*2+1]};var v,p,g,y,b=d.length;for(p=d[b-1],v=0;v<b;v++)g=d[v%b],y=d[(v+1)%b],u[l][v]=ki(p,g,y,o),p=g,g=y;return u[l]},draw:function(a,n,i,s,o,u,l){this.renderer.nodeShapeImpl("round-polygon",a,n,i,s,o,this.points,this.getOrCreateCorners(n,i,s,o,u,l,"drawCorners"))},intersectLine:function(a,n,i,s,o,u,l,f,h){return ph(o,u,this.points,a,n,i,s,l,this.getOrCreateCorners(a,n,i,s,f,h,"corners"))},checkPoint:function(a,n,i,s,o,u,l,f,h){return dh(a,n,this.points,u,l,s,o,this.getOrCreateCorners(u,l,s,o,f,h,"corners"))}}};Ht.generateRoundRectangle=function(){return this.nodeShapes["round-rectangle"]=this.nodeShapes.roundrectangle={renderer:this,name:"round-rectangle",points:ht(4,0),draw:function(e,r,a,n,i,s){this.renderer.nodeShapeImpl(this.name,e,r,a,n,i,this.points,s)},intersectLine:function(e,r,a,n,i,s,o,u){return Ao(i,s,e,r,a,n,o,u)},checkPoint:function(e,r,a,n,i,s,o,u){var l=n/2,f=i/2;u=u==="auto"?pr(n,i):u,u=Math.min(l,f,u);var h=u*2;return!!(Yt(e,r,this.points,s,o,n,i-h,[0,-1],a)||Yt(e,r,this.points,s,o,n-h,i,[0,-1],a)||cr(e,r,h,h,s-l+u,o-f+u,a)||cr(e,r,h,h,s+l-u,o-f+u,a)||cr(e,r,h,h,s+l-u,o+f-u,a)||cr(e,r,h,h,s-l+u,o+f-u,a))}}};Ht.generateCutRectangle=function(){return this.nodeShapes["cut-rectangle"]=this.nodeShapes.cutrectangle={renderer:this,name:"cut-rectangle",cornerLength:xi(),points:ht(4,0),draw:function(e,r,a,n,i,s){this.renderer.nodeShapeImpl(this.name,e,r,a,n,i,null,s)},generateCutTrianglePts:function(e,r,a,n,i){var s=i==="auto"?this.cornerLength:i,o=r/2,u=e/2,l=a-u,f=a+u,h=n-o,d=n+o;return{topLeft:[l,h+s,l+s,h,l+s,h+s],topRight:[f-s,h,f,h+s,f-s,h+s],bottomRight:[f,d-s,f-s,d,f-s,d-s],bottomLeft:[l+s,d,l,d-s,l+s,d-s]}},intersectLine:function(e,r,a,n,i,s,o,u){var l=this.generateCutTrianglePts(a+2*o,n+2*o,e,r,u),f=[].concat.apply([],[l.topLeft.splice(0,4),l.topRight.splice(0,4),l.bottomRight.splice(0,4),l.bottomLeft.splice(0,4)]);return pa(i,s,f,e,r)},checkPoint:function(e,r,a,n,i,s,o,u){var l=u==="auto"?this.cornerLength:u;if(Yt(e,r,this.points,s,o,n,i-2*l,[0,-1],a)||Yt(e,r,this.points,s,o,n-2*l,i,[0,-1],a))return!0;var f=this.generateCutTrianglePts(n,i,s,o);return dt(e,r,f.topLeft)||dt(e,r,f.topRight)||dt(e,r,f.bottomRight)||dt(e,r,f.bottomLeft)}}};Ht.generateBarrel=function(){return this.nodeShapes.barrel={renderer:this,name:"barrel",points:ht(4,0),draw:function(e,r,a,n,i,s){this.renderer.nodeShapeImpl(this.name,e,r,a,n,i)},intersectLine:function(e,r,a,n,i,s,o,u){var l=.15,f=.5,h=.85,d=this.generateBarrelBezierPts(a+2*o,n+2*o,e,r),c=function(g){var y=Rr({x:g[0],y:g[1]},{x:g[2],y:g[3]},{x:g[4],y:g[5]},l),b=Rr({x:g[0],y:g[1]},{x:g[2],y:g[3]},{x:g[4],y:g[5]},f),m=Rr({x:g[0],y:g[1]},{x:g[2],y:g[3]},{x:g[4],y:g[5]},h);return[g[0],g[1],y.x,y.y,b.x,b.y,m.x,m.y,g[4],g[5]]},v=[].concat(c(d.topLeft),c(d.topRight),c(d.bottomRight),c(d.bottomLeft));return pa(i,s,v,e,r)},generateBarrelBezierPts:function(e,r,a,n){var i=r/2,s=e/2,o=a-s,u=a+s,l=n-i,f=n+i,h=Kn(e,r),d=h.heightOffset,c=h.widthOffset,v=h.ctrlPtOffsetPct*e,p={topLeft:[o,l+d,o+v,l,o+c,l],topRight:[u-c,l,u-v,l,u,l+d],bottomRight:[u,f-d,u-v,f,u-c,f],bottomLeft:[o+c,f,o+v,f,o,f-d]};return p.topLeft.isTop=!0,p.topRight.isTop=!0,p.bottomLeft.isBottom=!0,p.bottomRight.isBottom=!0,p},checkPoint:function(e,r,a,n,i,s,o,u){var l=Kn(n,i),f=l.heightOffset,h=l.widthOffset;if(Yt(e,r,this.points,s,o,n,i-2*f,[0,-1],a)||Yt(e,r,this.points,s,o,n-2*h,i,[0,-1],a))return!0;for(var d=this.generateBarrelBezierPts(n,i,s,o),c=function(w,x,D){var L=D[4],A=D[2],N=D[0],O=D[5],M=D[1],R=Math.min(L,N),k=Math.max(L,N),P=Math.min(O,M),B=Math.max(O,M);if(R<=w&&w<=k&&P<=x&&x<=B){var z=yh(L,A,N),G=fh(z[0],z[1],z[2],w),F=G.filter(function(U){return 0<=U&&U<=1});if(F.length>0)return F[0]}return null},v=Object.keys(d),p=0;p<v.length;p++){var g=v[p],y=d[g],b=c(e,r,y);if(b!=null){var m=y[5],T=y[3],C=y[1],S=Ke(m,T,C,b);if(y.isTop&&S<=r||y.isBottom&&r<=S)return!0}}return!1}}};Ht.generateBottomRoundrectangle=function(){return this.nodeShapes["bottom-round-rectangle"]=this.nodeShapes.bottomroundrectangle={renderer:this,name:"bottom-round-rectangle",points:ht(4,0),draw:function(e,r,a,n,i,s){this.renderer.nodeShapeImpl(this.name,e,r,a,n,i,this.points,s)},intersectLine:function(e,r,a,n,i,s,o,u){var l=e-(a/2+o),f=r-(n/2+o),h=f,d=e+(a/2+o),c=Zt(i,s,e,r,l,f,d,h,!1);return c.length>0?c:Ao(i,s,e,r,a,n,o,u)},checkPoint:function(e,r,a,n,i,s,o,u){u=u==="auto"?pr(n,i):u;var l=2*u;if(Yt(e,r,this.points,s,o,n,i-l,[0,-1],a)||Yt(e,r,this.points,s,o,n-l,i,[0,-1],a))return!0;var f=n/2+2*a,h=i/2+2*a,d=[s-f,o-h,s-f,o,s+f,o,s+f,o-h];return!!(dt(e,r,d)||cr(e,r,l,l,s+n/2-u,o+i/2-u,a)||cr(e,r,l,l,s-n/2+u,o+i/2-u,a))}}};Ht.registerNodeShapes=function(){var t=this.nodeShapes={},e=this;this.generateEllipse(),this.generatePolygon("triangle",ht(3,0)),this.generateRoundPolygon("round-triangle",ht(3,0)),this.generatePolygon("rectangle",ht(4,0)),t.square=t.rectangle,this.generateRoundRectangle(),this.generateCutRectangle(),this.generateBarrel(),this.generateBottomRoundrectangle();{var r=[0,1,1,0,0,-1,-1,0];this.generatePolygon("diamond",r),this.generateRoundPolygon("round-diamond",r)}this.generatePolygon("pentagon",ht(5,0)),this.generateRoundPolygon("round-pentagon",ht(5,0)),this.generatePolygon("hexagon",ht(6,0)),this.generateRoundPolygon("round-hexagon",ht(6,0)),this.generatePolygon("heptagon",ht(7,0)),this.generateRoundPolygon("round-heptagon",ht(7,0)),this.generatePolygon("octagon",ht(8,0)),this.generateRoundPolygon("round-octagon",ht(8,0));var a=new Array(20);{var n=Wn(5,0),i=Wn(5,Math.PI/5),s=.5*(3-Math.sqrt(5));s*=1.57;for(var o=0;o<i.length/2;o++)i[o*2]*=s,i[o*2+1]*=s;for(var o=0;o<20/4;o++)a[o*4]=n[o*2],a[o*4+1]=n[o*2+1],a[o*4+2]=i[o*2],a[o*4+3]=i[o*2+1]}a=Oo(a),this.generatePolygon("star",a),this.generatePolygon("vee",[-1,-1,0,-.333,1,-1,0,1]),this.generatePolygon("rhomboid",[-1,-1,.333,-1,1,1,-.333,1]),this.generatePolygon("right-rhomboid",[-.333,-1,1,-1,.333,1,-1,1]),this.nodeShapes.concavehexagon=this.generatePolygon("concave-hexagon",[-1,-.95,-.75,0,-1,.95,1,.95,.75,0,1,-.95]);{var u=[-1,-1,.25,-1,1,0,.25,1,-1,1];this.generatePolygon("tag",u),this.generateRoundPolygon("round-tag",u)}t.makePolygon=function(l){var f=l.join("$"),h="polygon-"+f,d;return(d=this[h])?d:e.generatePolygon(h,l)}};var Oa={};Oa.timeToRender=function(){return this.redrawTotalTime/this.redrawCount};Oa.redraw=function(t){t=t||To();var e=this;e.averageRedrawTime===void 0&&(e.averageRedrawTime=0),e.lastRedrawTime===void 0&&(e.lastRedrawTime=0),e.lastDrawTime===void 0&&(e.lastDrawTime=0),e.requestedFrame=!0,e.renderOptions=t};Oa.beforeRender=function(t,e){if(!this.destroyed){e==null&&ze("Priority is not optional for beforeRender");var r=this.beforeRenderCallbacks;r.push({fn:t,priority:e}),r.sort(function(a,n){return n.priority-a.priority})}};var Xs=function(e,r,a){for(var n=e.beforeRenderCallbacks,i=0;i<n.length;i++)n[i].fn(r,a)};Oa.startRenderLoop=function(){var t=this,e=t.cy;if(!t.renderLoopStarted){t.renderLoopStarted=!0;var r=function a(n){if(!t.destroyed){if(!e.batching())if(t.requestedFrame&&!t.skipFrame){Xs(t,!0,n);var i=$t();t.render(t.renderOptions);var s=t.lastDrawTime=$t();t.averageRedrawTime===void 0&&(t.averageRedrawTime=s-i),t.redrawCount===void 0&&(t.redrawCount=0),t.redrawCount++,t.redrawTotalTime===void 0&&(t.redrawTotalTime=0);var o=s-i;t.redrawTotalTime+=o,t.lastRedrawTime=o,t.averageRedrawTime=t.averageRedrawTime/2+o/2,t.requestedFrame=!1}else Xs(t,!1,n);t.skipFrame=!1,rn(a)}};rn(r)}};var Jg=function(e){this.init(e)},Au=Jg,Kr=Au.prototype;Kr.clientFunctions=["redrawHint","render","renderTo","matchCanvasSize","nodeShapeImpl","arrowShapeImpl"];Kr.init=function(t){var e=this;e.options=t,e.cy=t.cy;var r=e.container=t.cy.container(),a=e.cy.window();if(a){var n=a.document,i=n.head,s="__________cytoscape_stylesheet",o="__________cytoscape_container",u=n.getElementById(s)!=null;if(r.className.indexOf(o)<0&&(r.className=(r.className||"")+" "+o),!u){var l=n.createElement("style");l.id=s,l.textContent="."+o+" { position: relative; }",i.insertBefore(l,i.children[0])}var f=a.getComputedStyle(r),h=f.getPropertyValue("position");h==="static"&&Ne("A Cytoscape container has style position:static and so can not use UI extensions properly")}e.selection=[void 0,void 0,void 0,void 0,0],e.bezierProjPcts=[.05,.225,.4,.5,.6,.775,.95],e.hoverData={down:null,last:null,downTime:null,triggerMode:null,dragging:!1,initialPan:[null,null],capture:!1},e.dragData={possibleDragElements:[]},e.touchData={start:null,capture:!1,startPosition:[null,null,null,null,null,null],singleTouchStartTime:null,singleTouchMoved:!0,now:[null,null,null,null,null,null],earlier:[null,null,null,null,null,null]},e.redraws=0,e.showFps=t.showFps,e.debug=t.debug,e.hideEdgesOnViewport=t.hideEdgesOnViewport,e.textureOnViewport=t.textureOnViewport,e.wheelSensitivity=t.wheelSensitivity,e.motionBlurEnabled=t.motionBlur,e.forcedPixelRatio=ie(t.pixelRatio)?t.pixelRatio:null,e.motionBlur=t.motionBlur,e.motionBlurOpacity=t.motionBlurOpacity,e.motionBlurTransparency=1-e.motionBlurOpacity,e.motionBlurPxRatio=1,e.mbPxRBlurry=1,e.minMbLowQualFrames=4,e.fullQualityMb=!1,e.clearedForMotionBlur=[],e.desktopTapThreshold=t.desktopTapThreshold,e.desktopTapThreshold2=t.desktopTapThreshold*t.desktopTapThreshold,e.touchTapThreshold=t.touchTapThreshold,e.touchTapThreshold2=t.touchTapThreshold*t.touchTapThreshold,e.tapholdDuration=500,e.bindings=[],e.beforeRenderCallbacks=[],e.beforeRenderPriorities={animations:400,eleCalcs:300,eleTxrDeq:200,lyrTxrDeq:150,lyrTxrSkip:100},e.registerNodeShapes(),e.registerArrowShapes(),e.registerCalculationListeners()};Kr.notify=function(t,e){var r=this,a=r.cy;if(!this.destroyed){if(t==="init"){r.load();return}if(t==="destroy"){r.destroy();return}(t==="add"||t==="remove"||t==="move"&&a.hasCompoundNodes()||t==="load"||t==="zorder"||t==="mount")&&r.invalidateCachedZSortedEles(),t==="viewport"&&r.redrawHint("select",!0),(t==="load"||t==="resize"||t==="mount")&&(r.invalidateContainerClientCoordsCache(),r.matchCanvasSize(r.container)),r.redrawHint("eles",!0),r.redrawHint("drag",!0),this.startRenderLoop(),this.redraw()}};Kr.destroy=function(){var t=this;t.destroyed=!0,t.cy.stopAnimationLoop();for(var e=0;e<t.bindings.length;e++){var r=t.bindings[e],a=r,n=a.target;(n.off||n.removeEventListener).apply(n,a.args)}if(t.bindings=[],t.beforeRenderCallbacks=[],t.onUpdateEleCalcsFns=[],t.removeObserver&&t.removeObserver.disconnect(),t.styleObserver&&t.styleObserver.disconnect(),t.resizeObserver&&t.resizeObserver.disconnect(),t.labelCalcDiv)try{document.body.removeChild(t.labelCalcDiv)}catch{}};Kr.isHeadless=function(){return!1};[Ri,Su,Lu,Wr,Ht,Oa].forEach(function(t){be(Kr,t)});var Yn=1e3/60,Ou={setupDequeueing:function(e){return function(){var a=this,n=this.renderer;if(!a.dequeueingSetup){a.dequeueingSetup=!0;var i=gn(function(){n.redrawHint("eles",!0),n.redrawHint("drag",!0),n.redraw()},e.deqRedrawThreshold),s=function(l,f){var h=$t(),d=n.averageRedrawTime,c=n.lastRedrawTime,v=[],p=n.cy.extent(),g=n.getPixelRatio();for(l||n.flushRenderedStyleQueue();;){var y=$t(),b=y-h,m=y-f;if(c<Yn){var T=Yn-(l?d:0);if(m>=e.deqFastCost*T)break}else if(l){if(b>=e.deqCost*c||b>=e.deqAvgCost*d)break}else if(m>=e.deqNoDrawCost*Yn)break;var C=e.deq(a,g,p);if(C.length>0)for(var S=0;S<C.length;S++)v.push(C[S]);else break}v.length>0&&(e.onDeqd(a,v),!l&&e.shouldRedraw(a,v,g,p)&&i())},o=e.priority||mi;n.beforeRender(s,o(a))}}}},jg=function(){function t(e){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:an;vi(this,t),this.idsByKey=new Bt,this.keyForId=new Bt,this.cachesByLvl=new Bt,this.lvls=[],this.getKey=e,this.doesEleInvalidateKey=r}return di(t,[{key:"getIdsFor",value:function(r){r==null&&ze("Can not get id list for null key");var a=this.idsByKey,n=this.idsByKey.get(r);return n||(n=new Ur,a.set(r,n)),n}},{key:"addIdForKey",value:function(r,a){r!=null&&this.getIdsFor(r).add(a)}},{key:"deleteIdForKey",value:function(r,a){r!=null&&this.getIdsFor(r).delete(a)}},{key:"getNumberOfIdsForKey",value:function(r){return r==null?0:this.getIdsFor(r).size}},{key:"updateKeyMappingFor",value:function(r){var a=r.id(),n=this.keyForId.get(a),i=this.getKey(r);this.deleteIdForKey(n,a),this.addIdForKey(i,a),this.keyForId.set(a,i)}},{key:"deleteKeyMappingFor",value:function(r){var a=r.id(),n=this.keyForId.get(a);this.deleteIdForKey(n,a),this.keyForId.delete(a)}},{key:"keyHasChangedFor",value:function(r){var a=r.id(),n=this.keyForId.get(a),i=this.getKey(r);return n!==i}},{key:"isInvalid",value:function(r){return this.keyHasChangedFor(r)||this.doesEleInvalidateKey(r)}},{key:"getCachesAt",value:function(r){var a=this.cachesByLvl,n=this.lvls,i=a.get(r);return i||(i=new Bt,a.set(r,i),n.push(r)),i}},{key:"getCache",value:function(r,a){return this.getCachesAt(a).get(r)}},{key:"get",value:function(r,a){var n=this.getKey(r),i=this.getCache(n,a);return i!=null&&this.updateKeyMappingFor(r),i}},{key:"getForCachedKey",value:function(r,a){var n=this.keyForId.get(r.id()),i=this.getCache(n,a);return i}},{key:"hasCache",value:function(r,a){return this.getCachesAt(a).has(r)}},{key:"has",value:function(r,a){var n=this.getKey(r);return this.hasCache(n,a)}},{key:"setCache",value:function(r,a,n){n.key=r,this.getCachesAt(a).set(r,n)}},{key:"set",value:function(r,a,n){var i=this.getKey(r);this.setCache(i,a,n),this.updateKeyMappingFor(r)}},{key:"deleteCache",value:function(r,a){this.getCachesAt(a).delete(r)}},{key:"delete",value:function(r,a){var n=this.getKey(r);this.deleteCache(n,a)}},{key:"invalidateKey",value:function(r){var a=this;this.lvls.forEach(function(n){return a.deleteCache(r,n)})}},{key:"invalidate",value:function(r){var a=r.id(),n=this.keyForId.get(a);this.deleteKeyMappingFor(r);var i=this.doesEleInvalidateKey(r);return i&&this.invalidateKey(n),i||this.getNumberOfIdsForKey(n)===0}}]),t}(),qs=25,Ya=50,Ja=-4,si=3,ep=7.99,tp=8,rp=1024,ap=1024,np=1024,ip=.2,sp=.8,op=10,up=.15,lp=.1,fp=.9,hp=.9,cp=100,vp=1,Mr={dequeue:"dequeue",downscale:"downscale",highQuality:"highQuality"},dp=tt({getKey:null,doesEleInvalidateKey:an,drawElement:null,getBoundingBox:null,getRotationPoint:null,getRotationOffset:null,isVisible:Eo,allowEdgeTxrCaching:!0,allowParentTxrCaching:!0}),ua=function(e,r){var a=this;a.renderer=e,a.onDequeues=[];var n=dp(r);be(a,n),a.lookup=new jg(n.getKey,n.doesEleInvalidateKey),a.setupDequeueing()},qe=ua.prototype;qe.reasons=Mr;qe.getTextureQueue=function(t){var e=this;return e.eleImgCaches=e.eleImgCaches||{},e.eleImgCaches[t]=e.eleImgCaches[t]||[]};qe.getRetiredTextureQueue=function(t){var e=this,r=e.eleImgCaches.retired=e.eleImgCaches.retired||{},a=r[t]=r[t]||[];return a};qe.getElementQueue=function(){var t=this,e=t.eleCacheQueue=t.eleCacheQueue||new Da(function(r,a){return a.reqs-r.reqs});return e};qe.getElementKeyToQueue=function(){var t=this,e=t.eleKeyToCacheQueue=t.eleKeyToCacheQueue||{};return e};qe.getElement=function(t,e,r,a,n){var i=this,s=this.renderer,o=s.cy.zoom(),u=this.lookup;if(!e||e.w===0||e.h===0||isNaN(e.w)||isNaN(e.h)||!t.visible()||t.removed()||!i.allowEdgeTxrCaching&&t.isEdge()||!i.allowParentTxrCaching&&t.isParent())return null;if(a==null&&(a=Math.ceil(Ei(o*r))),a<Ja)a=Ja;else if(o>=ep||a>si)return null;var l=Math.pow(2,a),f=e.h*l,h=e.w*l,d=s.eleTextBiggerThanMin(t,l);if(!this.isVisible(t,d))return null;var c=u.get(t,a);if(c&&c.invalidated&&(c.invalidated=!1,c.texture.invalidatedWidth-=c.width),c)return c;var v;if(f<=qs?v=qs:f<=Ya?v=Ya:v=Math.ceil(f/Ya)*Ya,f>np||h>ap)return null;var p=i.getTextureQueue(v),g=p[p.length-2],y=function(){return i.recycleTexture(v,h)||i.addTexture(v,h)};g||(g=p[p.length-1]),g||(g=y()),g.width-g.usedWidth<h&&(g=y());for(var b=function(R){return R&&R.scaledLabelShown===d},m=n&&n===Mr.dequeue,T=n&&n===Mr.highQuality,C=n&&n===Mr.downscale,S,E=a+1;E<=si;E++){var w=u.get(t,E);if(w){S=w;break}}var x=S&&S.level===a+1?S:null,D=function(){g.context.drawImage(x.texture.canvas,x.x,0,x.width,x.height,g.usedWidth,0,h,f)};if(g.context.setTransform(1,0,0,1,0,0),g.context.clearRect(g.usedWidth,0,h,v),b(x))D();else if(b(S))if(T){for(var L=S.level;L>a;L--)x=i.getElement(t,e,r,L,Mr.downscale);D()}else return i.queueElement(t,S.level-1),S;else{var A;if(!m&&!T&&!C)for(var N=a-1;N>=Ja;N--){var O=u.get(t,N);if(O){A=O;break}}if(b(A))return i.queueElement(t,a),A;g.context.translate(g.usedWidth,0),g.context.scale(l,l),this.drawElement(g.context,t,e,d,!1),g.context.scale(1/l,1/l),g.context.translate(-g.usedWidth,0)}return c={x:g.usedWidth,texture:g,level:a,scale:l,width:h,height:f,scaledLabelShown:d},g.usedWidth+=Math.ceil(h+tp),g.eleCaches.push(c),u.set(t,a,c),i.checkTextureFullness(g),c};qe.invalidateElements=function(t){for(var e=0;e<t.length;e++)this.invalidateElement(t[e])};qe.invalidateElement=function(t){var e=this,r=e.lookup,a=[],n=r.isInvalid(t);if(n){for(var i=Ja;i<=si;i++){var s=r.getForCachedKey(t,i);s&&a.push(s)}var o=r.invalidate(t);if(o)for(var u=0;u<a.length;u++){var l=a[u],f=l.texture;f.invalidatedWidth+=l.width,l.invalidated=!0,e.checkTextureUtility(f)}e.removeFromQueue(t)}};qe.checkTextureUtility=function(t){t.invalidatedWidth>=ip*t.width&&this.retireTexture(t)};qe.checkTextureFullness=function(t){var e=this,r=e.getTextureQueue(t.height);t.usedWidth/t.width>sp&&t.fullnessChecks>=op?er(r,t):t.fullnessChecks++};qe.retireTexture=function(t){var e=this,r=t.height,a=e.getTextureQueue(r),n=this.lookup;er(a,t),t.retired=!0;for(var i=t.eleCaches,s=0;s<i.length;s++){var o=i[s];n.deleteCache(o.key,o.level)}bi(i);var u=e.getRetiredTextureQueue(r);u.push(t)};qe.addTexture=function(t,e){var r=this,a=r.getTextureQueue(t),n={};return a.push(n),n.eleCaches=[],n.height=t,n.width=Math.max(rp,e),n.usedWidth=0,n.invalidatedWidth=0,n.fullnessChecks=0,n.canvas=r.renderer.makeOffscreenCanvas(n.width,n.height),n.context=n.canvas.getContext("2d"),n};qe.recycleTexture=function(t,e){for(var r=this,a=r.getTextureQueue(t),n=r.getRetiredTextureQueue(t),i=0;i<n.length;i++){var s=n[i];if(s.width>=e)return s.retired=!1,s.usedWidth=0,s.invalidatedWidth=0,s.fullnessChecks=0,bi(s.eleCaches),s.context.setTransform(1,0,0,1,0,0),s.context.clearRect(0,0,s.width,s.height),er(n,s),a.push(s),s}};qe.queueElement=function(t,e){var r=this,a=r.getElementQueue(),n=r.getElementKeyToQueue(),i=this.getKey(t),s=n[i];if(s)s.level=Math.max(s.level,e),s.eles.merge(t),s.reqs++,a.updateItem(s);else{var o={eles:t.spawn().merge(t),level:e,reqs:1,key:i};a.push(o),n[i]=o}};qe.dequeue=function(t){for(var e=this,r=e.getElementQueue(),a=e.getElementKeyToQueue(),n=[],i=e.lookup,s=0;s<vp&&r.size()>0;s++){var o=r.pop(),u=o.key,l=o.eles[0],f=i.hasCache(l,o.level);if(a[u]=null,f)continue;n.push(o);var h=e.getBoundingBox(l);e.getElement(l,h,t,o.level,Mr.dequeue)}return n};qe.removeFromQueue=function(t){var e=this,r=e.getElementQueue(),a=e.getElementKeyToQueue(),n=this.getKey(t),i=a[n];i!=null&&(i.eles.length===1?(i.reqs=yi,r.updateItem(i),r.pop(),a[n]=null):i.eles.unmerge(t))};qe.onDequeue=function(t){this.onDequeues.push(t)};qe.offDequeue=function(t){er(this.onDequeues,t)};qe.setupDequeueing=Ou.setupDequeueing({deqRedrawThreshold:cp,deqCost:up,deqAvgCost:lp,deqNoDrawCost:fp,deqFastCost:hp,deq:function(e,r,a){return e.dequeue(r,a)},onDeqd:function(e,r){for(var a=0;a<e.onDequeues.length;a++){var n=e.onDequeues[a];n(r)}},shouldRedraw:function(e,r,a,n){for(var i=0;i<r.length;i++)for(var s=r[i].eles,o=0;o<s.length;o++){var u=s[o].boundingBox();if(wi(u,n))return!0}return!1},priority:function(e){return e.renderer.beforeRenderPriorities.eleTxrDeq}});var gp=1,fa=-4,cn=2,pp=3.99,yp=50,mp=50,bp=.15,Ep=.1,wp=.9,xp=.9,Tp=1,Ws=250,Cp=4e3*4e3,Dp=!0,Nu=function(e){var r=this,a=r.renderer=e,n=a.cy;r.layersByLevel={},r.firstGet=!0,r.lastInvalidationTime=$t()-2*Ws,r.skipping=!1,r.eleTxrDeqs=n.collection(),r.scheduleElementRefinement=gn(function(){r.refineElementTextures(r.eleTxrDeqs),r.eleTxrDeqs.unmerge(r.eleTxrDeqs)},mp),a.beforeRender(function(s,o){o-r.lastInvalidationTime<=Ws?r.skipping=!0:r.skipping=!1},a.beforeRenderPriorities.lyrTxrSkip);var i=function(o,u){return u.reqs-o.reqs};r.layersQueue=new Da(i),r.setupDequeueing()},rt=Nu.prototype,Ks=0,Sp=Math.pow(2,53)-1;rt.makeLayer=function(t,e){var r=Math.pow(2,e),a=Math.ceil(t.w*r),n=Math.ceil(t.h*r),i=this.renderer.makeOffscreenCanvas(a,n),s={id:Ks=++Ks%Sp,bb:t,level:e,width:a,height:n,canvas:i,context:i.getContext("2d"),eles:[],elesQueue:[],reqs:0},o=s.context,u=-s.bb.x1,l=-s.bb.y1;return o.scale(r,r),o.translate(u,l),s};rt.getLayers=function(t,e,r){var a=this,n=a.renderer,i=n.cy,s=i.zoom(),o=a.firstGet;if(a.firstGet=!1,r==null){if(r=Math.ceil(Ei(s*e)),r<fa)r=fa;else if(s>=pp||r>cn)return null}a.validateLayersElesOrdering(r,t);var u=a.layersByLevel,l=Math.pow(2,r),f=u[r]=u[r]||[],h,d=a.levelIsComplete(r,t),c,v=function(){var D=function(M){if(a.validateLayersElesOrdering(M,t),a.levelIsComplete(M,t))return c=u[M],!0},L=function(M){if(!c)for(var R=r+M;fa<=R&&R<=cn&&!D(R);R+=M);};L(1),L(-1);for(var A=f.length-1;A>=0;A--){var N=f[A];N.invalid&&er(f,N)}};if(!d)v();else return f;var p=function(){if(!h){h=gt();for(var D=0;D<t.length;D++)So(h,t[D].boundingBox())}return h},g=function(D){D=D||{};var L=D.after;p();var A=h.w*l*(h.h*l);if(A>Cp)return null;var N=a.makeLayer(h,r);if(L!=null){var O=f.indexOf(L)+1;f.splice(O,0,N)}else(D.insert===void 0||D.insert)&&f.unshift(N);return N};if(a.skipping&&!o)return null;for(var y=null,b=t.length/gp,m=!o,T=0;T<t.length;T++){var C=t[T],S=C._private.rscratch,E=S.imgLayerCaches=S.imgLayerCaches||{},w=E[r];if(w){y=w;continue}if((!y||y.eles.length>=b||!Lo(y.bb,C.boundingBox()))&&(y=g({insert:!0,after:y}),!y))return null;c||m?a.queueLayer(y,C):a.drawEleInLayer(y,C,r,e),y.eles.push(C),E[r]=y}return c||(m?null:f)};rt.getEleLevelForLayerLevel=function(t,e){return t};rt.drawEleInLayer=function(t,e,r,a){var n=this,i=this.renderer,s=t.context,o=e.boundingBox();o.w===0||o.h===0||!e.visible()||(r=n.getEleLevelForLayerLevel(r,a),i.setImgSmoothing(s,!1),i.drawCachedElement(s,e,null,null,r,Dp),i.setImgSmoothing(s,!0))};rt.levelIsComplete=function(t,e){var r=this,a=r.layersByLevel[t];if(!a||a.length===0)return!1;for(var n=0,i=0;i<a.length;i++){var s=a[i];if(s.reqs>0||s.invalid)return!1;n+=s.eles.length}return n===e.length};rt.validateLayersElesOrdering=function(t,e){var r=this.layersByLevel[t];if(r)for(var a=0;a<r.length;a++){for(var n=r[a],i=-1,s=0;s<e.length;s++)if(n.eles[0]===e[s]){i=s;break}if(i<0){this.invalidateLayer(n);continue}for(var o=i,s=0;s<n.eles.length;s++)if(n.eles[s]!==e[o+s]){this.invalidateLayer(n);break}}};rt.updateElementsInLayers=function(t,e){for(var r=this,a=Ta(t[0]),n=0;n<t.length;n++)for(var i=a?null:t[n],s=a?t[n]:t[n].ele,o=s._private.rscratch,u=o.imgLayerCaches=o.imgLayerCaches||{},l=fa;l<=cn;l++){var f=u[l];f&&(i&&r.getEleLevelForLayerLevel(f.level)!==i.level||e(f,s,i))}};rt.haveLayers=function(){for(var t=this,e=!1,r=fa;r<=cn;r++){var a=t.layersByLevel[r];if(a&&a.length>0){e=!0;break}}return e};rt.invalidateElements=function(t){var e=this;t.length!==0&&(e.lastInvalidationTime=$t(),!(t.length===0||!e.haveLayers())&&e.updateElementsInLayers(t,function(a,n,i){e.invalidateLayer(a)}))};rt.invalidateLayer=function(t){if(this.lastInvalidationTime=$t(),!t.invalid){var e=t.level,r=t.eles,a=this.layersByLevel[e];er(a,t),t.elesQueue=[],t.invalid=!0,t.replacement&&(t.replacement.invalid=!0);for(var n=0;n<r.length;n++){var i=r[n]._private.rscratch.imgLayerCaches;i&&(i[e]=null)}}};rt.refineElementTextures=function(t){var e=this;e.updateElementsInLayers(t,function(a,n,i){var s=a.replacement;if(s||(s=a.replacement=e.makeLayer(a.bb,a.level),s.replaces=a,s.eles=a.eles),!s.reqs)for(var o=0;o<s.eles.length;o++)e.queueLayer(s,s.eles[o])})};rt.enqueueElementRefinement=function(t){this.eleTxrDeqs.merge(t),this.scheduleElementRefinement()};rt.queueLayer=function(t,e){var r=this,a=r.layersQueue,n=t.elesQueue,i=n.hasId=n.hasId||{};if(!t.replacement){if(e){if(i[e.id()])return;n.push(e),i[e.id()]=!0}t.reqs?(t.reqs++,a.updateItem(t)):(t.reqs=1,a.push(t))}};rt.dequeue=function(t){for(var e=this,r=e.layersQueue,a=[],n=0;n<Tp&&r.size()!==0;){var i=r.peek();if(i.replacement){r.pop();continue}if(i.replaces&&i!==i.replaces.replacement){r.pop();continue}if(i.invalid){r.pop();continue}var s=i.elesQueue.shift();s&&(e.drawEleInLayer(i,s,i.level,t),n++),a.length===0&&a.push(!0),i.elesQueue.length===0&&(r.pop(),i.reqs=0,i.replaces&&e.applyLayerReplacement(i),e.requestRedraw())}return a};rt.applyLayerReplacement=function(t){var e=this,r=e.layersByLevel[t.level],a=t.replaces,n=r.indexOf(a);if(!(n<0||a.invalid)){r[n]=t;for(var i=0;i<t.eles.length;i++){var s=t.eles[i]._private,o=s.imgLayerCaches=s.imgLayerCaches||{};o&&(o[t.level]=t)}e.requestRedraw()}};rt.requestRedraw=gn(function(){var t=this.renderer;t.redrawHint("eles",!0),t.redrawHint("drag",!0),t.redraw()},100);rt.setupDequeueing=Ou.setupDequeueing({deqRedrawThreshold:yp,deqCost:bp,deqAvgCost:Ep,deqNoDrawCost:wp,deqFastCost:xp,deq:function(e,r){return e.dequeue(r)},onDeqd:mi,shouldRedraw:Eo,priority:function(e){return e.renderer.beforeRenderPriorities.lyrTxrDeq}});var Iu={},Zs;function Lp(t,e){for(var r=0;r<e.length;r++){var a=e[r];t.lineTo(a.x,a.y)}}function Ap(t,e,r){for(var a,n=0;n<e.length;n++){var i=e[n];n===0&&(a=i),t.lineTo(i.x,i.y)}t.quadraticCurveTo(r.x,r.y,a.x,a.y)}function Qs(t,e,r){t.beginPath&&t.beginPath();for(var a=e,n=0;n<a.length;n++){var i=a[n];t.lineTo(i.x,i.y)}var s=r,o=r[0];t.moveTo(o.x,o.y);for(var n=1;n<s.length;n++){var i=s[n];t.lineTo(i.x,i.y)}t.closePath&&t.closePath()}function Op(t,e,r,a,n){t.beginPath&&t.beginPath(),t.arc(r,a,n,0,Math.PI*2,!1);var i=e,s=i[0];t.moveTo(s.x,s.y);for(var o=0;o<i.length;o++){var u=i[o];t.lineTo(u.x,u.y)}t.closePath&&t.closePath()}function Np(t,e,r,a){t.arc(e,r,a,0,Math.PI*2,!1)}Iu.arrowShapeImpl=function(t){return(Zs||(Zs={polygon:Lp,"triangle-backcurve":Ap,"triangle-tee":Qs,"circle-triangle":Op,"triangle-cross":Qs,circle:Np}))[t]};var zt={};zt.drawElement=function(t,e,r,a,n,i){var s=this;e.isNode()?s.drawNode(t,e,r,a,n,i):s.drawEdge(t,e,r,a,n,i)};zt.drawElementOverlay=function(t,e){var r=this;e.isNode()?r.drawNodeOverlay(t,e):r.drawEdgeOverlay(t,e)};zt.drawElementUnderlay=function(t,e){var r=this;e.isNode()?r.drawNodeUnderlay(t,e):r.drawEdgeUnderlay(t,e)};zt.drawCachedElementPortion=function(t,e,r,a,n,i,s,o){var u=this,l=r.getBoundingBox(e);if(!(l.w===0||l.h===0)){var f=r.getElement(e,l,a,n,i);if(f!=null){var h=o(u,e);if(h===0)return;var d=s(u,e),c=l.x1,v=l.y1,p=l.w,g=l.h,y,b,m,T,C;if(d!==0){var S=r.getRotationPoint(e);m=S.x,T=S.y,t.translate(m,T),t.rotate(d),C=u.getImgSmoothing(t),C||u.setImgSmoothing(t,!0);var E=r.getRotationOffset(e);y=E.x,b=E.y}else y=c,b=v;var w;h!==1&&(w=t.globalAlpha,t.globalAlpha=w*h),t.drawImage(f.texture.canvas,f.x,0,f.width,f.height,y,b,p,g),h!==1&&(t.globalAlpha=w),d!==0&&(t.rotate(-d),t.translate(-m,-T),C||u.setImgSmoothing(t,!1))}else r.drawElement(t,e)}};var Ip=function(){return 0},Mp=function(e,r){return e.getTextAngle(r,null)},Rp=function(e,r){return e.getTextAngle(r,"source")},kp=function(e,r){return e.getTextAngle(r,"target")},Pp=function(e,r){return r.effectiveOpacity()},_n=function(e,r){return r.pstyle("text-opacity").pfValue*r.effectiveOpacity()};zt.drawCachedElement=function(t,e,r,a,n,i){var s=this,o=s.data,u=o.eleTxrCache,l=o.lblTxrCache,f=o.slbTxrCache,h=o.tlbTxrCache,d=e.boundingBox(),c=i===!0?u.reasons.highQuality:null;if(!(d.w===0||d.h===0||!e.visible())&&(!a||wi(d,a))){var v=e.isEdge(),p=e.element()._private.rscratch.badLine;s.drawElementUnderlay(t,e),s.drawCachedElementPortion(t,e,u,r,n,c,Ip,Pp),(!v||!p)&&s.drawCachedElementPortion(t,e,l,r,n,c,Mp,_n),v&&!p&&(s.drawCachedElementPortion(t,e,f,r,n,c,Rp,_n),s.drawCachedElementPortion(t,e,h,r,n,c,kp,_n)),s.drawElementOverlay(t,e)}};zt.drawElements=function(t,e){for(var r=this,a=0;a<e.length;a++){var n=e[a];r.drawElement(t,n)}};zt.drawCachedElements=function(t,e,r,a){for(var n=this,i=0;i<e.length;i++){var s=e[i];n.drawCachedElement(t,s,r,a)}};zt.drawCachedNodes=function(t,e,r,a){for(var n=this,i=0;i<e.length;i++){var s=e[i];s.isNode()&&n.drawCachedElement(t,s,r,a)}};zt.drawLayeredElements=function(t,e,r,a){var n=this,i=n.data.lyrTxrCache.getLayers(e,r);if(i)for(var s=0;s<i.length;s++){var o=i[s],u=o.bb;u.w===0||u.h===0||t.drawImage(o.canvas,u.x1,u.y1,u.w,u.h)}else n.drawCachedElements(t,e,r,a)};var Xt={};Xt.drawEdge=function(t,e,r){var a=arguments.length>3&&arguments[3]!==void 0?arguments[3]:!0,n=arguments.length>4&&arguments[4]!==void 0?arguments[4]:!0,i=arguments.length>5&&arguments[5]!==void 0?arguments[5]:!0,s=this,o=e._private.rscratch;if(!(i&&!e.visible())&&!(o.badLine||o.allpts==null||isNaN(o.allpts[0]))){var u;r&&(u=r,t.translate(-u.x1,-u.y1));var l=i?e.pstyle("opacity").value:1,f=i?e.pstyle("line-opacity").value:1,h=e.pstyle("curve-style").value,d=e.pstyle("line-style").value,c=e.pstyle("width").pfValue,v=e.pstyle("line-cap").value,p=e.pstyle("line-outline-width").value,g=e.pstyle("line-outline-color").value,y=l*f,b=l*f,m=function(){var M=arguments.length>0&&arguments[0]!==void 0?arguments[0]:y;h==="straight-triangle"?(s.eleStrokeStyle(t,e,M),s.drawEdgeTrianglePath(e,t,o.allpts)):(t.lineWidth=c,t.lineCap=v,s.eleStrokeStyle(t,e,M),s.drawEdgePath(e,t,o.allpts,d),t.lineCap="butt")},T=function(){var M=arguments.length>0&&arguments[0]!==void 0?arguments[0]:y;if(t.lineWidth=c+p,t.lineCap=v,p>0)s.colorStrokeStyle(t,g[0],g[1],g[2],M);else{t.lineCap="butt";return}h==="straight-triangle"?s.drawEdgeTrianglePath(e,t,o.allpts):(s.drawEdgePath(e,t,o.allpts,d),t.lineCap="butt")},C=function(){n&&s.drawEdgeOverlay(t,e)},S=function(){n&&s.drawEdgeUnderlay(t,e)},E=function(){var M=arguments.length>0&&arguments[0]!==void 0?arguments[0]:b;s.drawArrowheads(t,e,M)},w=function(){s.drawElementText(t,e,null,a)};t.lineJoin="round";var x=e.pstyle("ghost").value==="yes";if(x){var D=e.pstyle("ghost-offset-x").pfValue,L=e.pstyle("ghost-offset-y").pfValue,A=e.pstyle("ghost-opacity").value,N=y*A;t.translate(D,L),m(N),E(N),t.translate(-D,-L)}else T();S(),m(),E(),C(),w(),r&&t.translate(u.x1,u.y1)}};var Mu=function(e){if(!["overlay","underlay"].includes(e))throw new Error("Invalid state");return function(r,a){if(a.visible()){var n=a.pstyle("".concat(e,"-opacity")).value;if(n!==0){var i=this,s=i.usePaths(),o=a._private.rscratch,u=a.pstyle("".concat(e,"-padding")).pfValue,l=2*u,f=a.pstyle("".concat(e,"-color")).value;r.lineWidth=l,o.edgeType==="self"&&!s?r.lineCap="butt":r.lineCap="round",i.colorStrokeStyle(r,f[0],f[1],f[2],n),i.drawEdgePath(a,r,o.allpts,"solid")}}}};Xt.drawEdgeOverlay=Mu("overlay");Xt.drawEdgeUnderlay=Mu("underlay");Xt.drawEdgePath=function(t,e,r,a){var n=t._private.rscratch,i=e,s,o=!1,u=this.usePaths(),l=t.pstyle("line-dash-pattern").pfValue,f=t.pstyle("line-dash-offset").pfValue;if(u){var h=r.join("$"),d=n.pathCacheKey&&n.pathCacheKey===h;d?(s=e=n.pathCache,o=!0):(s=e=new Path2D,n.pathCacheKey=h,n.pathCache=s)}if(i.setLineDash)switch(a){case"dotted":i.setLineDash([1,1]);break;case"dashed":i.setLineDash(l),i.lineDashOffset=f;break;case"solid":i.setLineDash([]);break}if(!o&&!n.badLine)switch(e.beginPath&&e.beginPath(),e.moveTo(r[0],r[1]),n.edgeType){case"bezier":case"self":case"compound":case"multibezier":for(var c=2;c+3<r.length;c+=4)e.quadraticCurveTo(r[c],r[c+1],r[c+2],r[c+3]);break;case"straight":case"haystack":for(var v=2;v+1<r.length;v+=2)e.lineTo(r[v],r[v+1]);break;case"segments":if(n.isRound){var p=io(n.roundCorners),g;try{for(p.s();!(g=p.n()).done;){var y=g.value;wu(e,y)}}catch(m){p.e(m)}finally{p.f()}e.lineTo(r[r.length-2],r[r.length-1])}else for(var b=2;b+1<r.length;b+=2)e.lineTo(r[b],r[b+1]);break}e=i,u?e.stroke(s):e.stroke(),e.setLineDash&&e.setLineDash([])};Xt.drawEdgeTrianglePath=function(t,e,r){e.fillStyle=e.strokeStyle;for(var a=t.pstyle("width").pfValue,n=0;n+1<r.length;n+=2){var i=[r[n+2]-r[n],r[n+3]-r[n+1]],s=Math.sqrt(i[0]*i[0]+i[1]*i[1]),o=[i[1]/s,-i[0]/s],u=[o[0]*a/2,o[1]*a/2];e.beginPath(),e.moveTo(r[n]-u[0],r[n+1]-u[1]),e.lineTo(r[n]+u[0],r[n+1]+u[1]),e.lineTo(r[n+2],r[n+3]),e.closePath(),e.fill()}};Xt.drawArrowheads=function(t,e,r){var a=e._private.rscratch,n=a.edgeType==="haystack";n||this.drawArrowhead(t,e,"source",a.arrowStartX,a.arrowStartY,a.srcArrowAngle,r),this.drawArrowhead(t,e,"mid-target",a.midX,a.midY,a.midtgtArrowAngle,r),this.drawArrowhead(t,e,"mid-source",a.midX,a.midY,a.midsrcArrowAngle,r),n||this.drawArrowhead(t,e,"target",a.arrowEndX,a.arrowEndY,a.tgtArrowAngle,r)};Xt.drawArrowhead=function(t,e,r,a,n,i,s){if(!(isNaN(a)||a==null||isNaN(n)||n==null||isNaN(i)||i==null)){var o=this,u=e.pstyle(r+"-arrow-shape").value;if(u!=="none"){var l=e.pstyle(r+"-arrow-fill").value==="hollow"?"both":"filled",f=e.pstyle(r+"-arrow-fill").value,h=e.pstyle("width").pfValue,d=e.pstyle(r+"-arrow-width"),c=d.value==="match-line"?h:d.pfValue;d.units==="%"&&(c*=h);var v=e.pstyle("opacity").value;s===void 0&&(s=v);var p=t.globalCompositeOperation;(s!==1||f==="hollow")&&(t.globalCompositeOperation="destination-out",o.colorFillStyle(t,255,255,255,1),o.colorStrokeStyle(t,255,255,255,1),o.drawArrowShape(e,t,l,h,u,c,a,n,i),t.globalCompositeOperation=p);var g=e.pstyle(r+"-arrow-color").value;o.colorFillStyle(t,g[0],g[1],g[2],s),o.colorStrokeStyle(t,g[0],g[1],g[2],s),o.drawArrowShape(e,t,f,h,u,c,a,n,i)}}};Xt.drawArrowShape=function(t,e,r,a,n,i,s,o,u){var l=this,f=this.usePaths()&&n!=="triangle-cross",h=!1,d,c=e,v={x:s,y:o},p=t.pstyle("arrow-scale").value,g=this.getArrowWidth(a,p),y=l.arrowShapes[n];if(f){var b=l.arrowPathCache=l.arrowPathCache||[],m=dr(n),T=b[m];T!=null?(d=e=T,h=!0):(d=e=new Path2D,b[m]=d)}h||(e.beginPath&&e.beginPath(),f?y.draw(e,1,0,{x:0,y:0},1):y.draw(e,g,u,v,a),e.closePath&&e.closePath()),e=c,f&&(e.translate(s,o),e.rotate(u),e.scale(g,g)),(r==="filled"||r==="both")&&(f?e.fill(d):e.fill()),(r==="hollow"||r==="both")&&(e.lineWidth=i/(f?g:1),e.lineJoin="miter",f?e.stroke(d):e.stroke()),f&&(e.scale(1/g,1/g),e.rotate(-u),e.translate(-s,-o))};var Bi={};Bi.safeDrawImage=function(t,e,r,a,n,i,s,o,u,l){if(!(n<=0||i<=0||u<=0||l<=0))try{t.drawImage(e,r,a,n,i,s,o,u,l)}catch(f){Ne(f)}};Bi.drawInscribedImage=function(t,e,r,a,n){var i=this,s=r.position(),o=s.x,u=s.y,l=r.cy().style(),f=l.getIndexedStyle.bind(l),h=f(r,"background-fit","value",a),d=f(r,"background-repeat","value",a),c=r.width(),v=r.height(),p=r.padding()*2,g=c+(f(r,"background-width-relative-to","value",a)==="inner"?0:p),y=v+(f(r,"background-height-relative-to","value",a)==="inner"?0:p),b=r._private.rscratch,m=f(r,"background-clip","value",a),T=m==="node",C=f(r,"background-image-opacity","value",a)*n,S=f(r,"background-image-smoothing","value",a),E=r.pstyle("corner-radius").value;E!=="auto"&&(E=r.pstyle("corner-radius").pfValue);var w=e.width||e.cachedW,x=e.height||e.cachedH;(w==null||x==null)&&(document.body.appendChild(e),w=e.cachedW=e.width||e.offsetWidth,x=e.cachedH=e.height||e.offsetHeight,document.body.removeChild(e));var D=w,L=x;if(f(r,"background-width","value",a)!=="auto"&&(f(r,"background-width","units",a)==="%"?D=f(r,"background-width","pfValue",a)*g:D=f(r,"background-width","pfValue",a)),f(r,"background-height","value",a)!=="auto"&&(f(r,"background-height","units",a)==="%"?L=f(r,"background-height","pfValue",a)*y:L=f(r,"background-height","pfValue",a)),!(D===0||L===0)){if(h==="contain"){var A=Math.min(g/D,y/L);D*=A,L*=A}else if(h==="cover"){var A=Math.max(g/D,y/L);D*=A,L*=A}var N=o-g/2,O=f(r,"background-position-x","units",a),M=f(r,"background-position-x","pfValue",a);O==="%"?N+=(g-D)*M:N+=M;var R=f(r,"background-offset-x","units",a),k=f(r,"background-offset-x","pfValue",a);R==="%"?N+=(g-D)*k:N+=k;var P=u-y/2,B=f(r,"background-position-y","units",a),z=f(r,"background-position-y","pfValue",a);B==="%"?P+=(y-L)*z:P+=z;var G=f(r,"background-offset-y","units",a),F=f(r,"background-offset-y","pfValue",a);G==="%"?P+=(y-L)*F:P+=F,b.pathCache&&(N-=o,P-=u,o=0,u=0);var U=t.globalAlpha;t.globalAlpha=C;var Y=i.getImgSmoothing(t),W=!1;if(S==="no"&&Y?(i.setImgSmoothing(t,!1),W=!0):S==="yes"&&!Y&&(i.setImgSmoothing(t,!0),W=!0),d==="no-repeat")T&&(t.save(),b.pathCache?t.clip(b.pathCache):(i.nodeShapes[i.getNodeShape(r)].draw(t,o,u,g,y,E,b),t.clip())),i.safeDrawImage(t,e,0,0,w,x,N,P,D,L),T&&t.restore();else{var K=t.createPattern(e,d);t.fillStyle=K,i.nodeShapes[i.getNodeShape(r)].draw(t,o,u,g,y,E,b),t.translate(N,P),t.fill(),t.translate(-N,-P)}t.globalAlpha=U,W&&i.setImgSmoothing(t,Y)}};var xr={};xr.eleTextBiggerThanMin=function(t,e){if(!e){var r=t.cy().zoom(),a=this.getPixelRatio(),n=Math.ceil(Ei(r*a));e=Math.pow(2,n)}var i=t.pstyle("font-size").pfValue*e,s=t.pstyle("min-zoomed-font-size").pfValue;return!(i<s)};xr.drawElementText=function(t,e,r,a,n){var i=arguments.length>5&&arguments[5]!==void 0?arguments[5]:!0,s=this;if(a==null){if(i&&!s.eleTextBiggerThanMin(e))return}else if(a===!1)return;if(e.isNode()){var o=e.pstyle("label");if(!o||!o.value)return;var u=s.getLabelJustification(e);t.textAlign=u,t.textBaseline="bottom"}else{var l=e.element()._private.rscratch.badLine,f=e.pstyle("label"),h=e.pstyle("source-label"),d=e.pstyle("target-label");if(l||(!f||!f.value)&&(!h||!h.value)&&(!d||!d.value))return;t.textAlign="center",t.textBaseline="bottom"}var c=!r,v;r&&(v=r,t.translate(-v.x1,-v.y1)),n==null?(s.drawText(t,e,null,c,i),e.isEdge()&&(s.drawText(t,e,"source",c,i),s.drawText(t,e,"target",c,i))):s.drawText(t,e,n,c,i),r&&t.translate(v.x1,v.y1)};xr.getFontCache=function(t){var e;this.fontCaches=this.fontCaches||[];for(var r=0;r<this.fontCaches.length;r++)if(e=this.fontCaches[r],e.context===t)return e;return e={context:t},this.fontCaches.push(e),e};xr.setupTextStyle=function(t,e){var r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!0,a=e.pstyle("font-style").strValue,n=e.pstyle("font-size").pfValue+"px",i=e.pstyle("font-family").strValue,s=e.pstyle("font-weight").strValue,o=r?e.effectiveOpacity()*e.pstyle("text-opacity").value:1,u=e.pstyle("text-outline-opacity").value*o,l=e.pstyle("color").value,f=e.pstyle("text-outline-color").value;t.font=a+" "+s+" "+n+" "+i,t.lineJoin="round",this.colorFillStyle(t,l[0],l[1],l[2],o),this.colorStrokeStyle(t,f[0],f[1],f[2],u)};function Hn(t,e,r,a,n){var i=arguments.length>5&&arguments[5]!==void 0?arguments[5]:5,s=arguments.length>6?arguments[6]:void 0;t.beginPath(),t.moveTo(e+i,r),t.lineTo(e+a-i,r),t.quadraticCurveTo(e+a,r,e+a,r+i),t.lineTo(e+a,r+n-i),t.quadraticCurveTo(e+a,r+n,e+a-i,r+n),t.lineTo(e+i,r+n),t.quadraticCurveTo(e,r+n,e,r+n-i),t.lineTo(e,r+i),t.quadraticCurveTo(e,r,e+i,r),t.closePath(),s?t.stroke():t.fill()}xr.getTextAngle=function(t,e){var r,a=t._private,n=a.rscratch,i=e?e+"-":"",s=t.pstyle(i+"text-rotation"),o=At(n,"labelAngle",e);return s.strValue==="autorotate"?r=t.isEdge()?o:0:s.strValue==="none"?r=0:r=s.pfValue,r};xr.drawText=function(t,e,r){var a=arguments.length>3&&arguments[3]!==void 0?arguments[3]:!0,n=arguments.length>4&&arguments[4]!==void 0?arguments[4]:!0,i=e._private,s=i.rscratch,o=n?e.effectiveOpacity():1;if(!(n&&(o===0||e.pstyle("text-opacity").value===0))){r==="main"&&(r=null);var u=At(s,"labelX",r),l=At(s,"labelY",r),f,h,d=this.getLabelText(e,r);if(d!=null&&d!==""&&!isNaN(u)&&!isNaN(l)){this.setupTextStyle(t,e,n);var c=r?r+"-":"",v=At(s,"labelWidth",r),p=At(s,"labelHeight",r),g=e.pstyle(c+"text-margin-x").pfValue,y=e.pstyle(c+"text-margin-y").pfValue,b=e.isEdge(),m=e.pstyle("text-halign").value,T=e.pstyle("text-valign").value;b&&(m="center",T="center"),u+=g,l+=y;var C;switch(a?C=this.getTextAngle(e,r):C=0,C!==0&&(f=u,h=l,t.translate(f,h),t.rotate(C),u=0,l=0),T){case"top":break;case"center":l+=p/2;break;case"bottom":l+=p;break}var S=e.pstyle("text-background-opacity").value,E=e.pstyle("text-border-opacity").value,w=e.pstyle("text-border-width").pfValue,x=e.pstyle("text-background-padding").pfValue,D=e.pstyle("text-background-shape").strValue,L=D.indexOf("round")===0,A=2;if(S>0||w>0&&E>0){var N=u-x;switch(m){case"left":N-=v;break;case"center":N-=v/2;break}var O=l-p-x,M=v+2*x,R=p+2*x;if(S>0){var k=t.fillStyle,P=e.pstyle("text-background-color").value;t.fillStyle="rgba("+P[0]+","+P[1]+","+P[2]+","+S*o+")",L?Hn(t,N,O,M,R,A):t.fillRect(N,O,M,R),t.fillStyle=k}if(w>0&&E>0){var B=t.strokeStyle,z=t.lineWidth,G=e.pstyle("text-border-color").value,F=e.pstyle("text-border-style").value;if(t.strokeStyle="rgba("+G[0]+","+G[1]+","+G[2]+","+E*o+")",t.lineWidth=w,t.setLineDash)switch(F){case"dotted":t.setLineDash([1,1]);break;case"dashed":t.setLineDash([4,2]);break;case"double":t.lineWidth=w/4,t.setLineDash([]);break;case"solid":t.setLineDash([]);break}if(L?Hn(t,N,O,M,R,A,"stroke"):t.strokeRect(N,O,M,R),F==="double"){var U=w/2;L?Hn(t,N+U,O+U,M-U*2,R-U*2,A,"stroke"):t.strokeRect(N+U,O+U,M-U*2,R-U*2)}t.setLineDash&&t.setLineDash([]),t.lineWidth=z,t.strokeStyle=B}}var Y=2*e.pstyle("text-outline-width").pfValue;if(Y>0&&(t.lineWidth=Y),e.pstyle("text-wrap").value==="wrap"){var W=At(s,"labelWrapCachedLines",r),K=At(s,"labelLineHeight",r),j=v/2,_=this.getLabelJustification(e);switch(_==="auto"||(m==="left"?_==="left"?u+=-v:_==="center"&&(u+=-j):m==="center"?_==="left"?u+=-j:_==="right"&&(u+=j):m==="right"&&(_==="center"?u+=j:_==="right"&&(u+=v))),T){case"top":l-=(W.length-1)*K;break;case"center":case"bottom":l-=(W.length-1)*K;break}for(var V=0;V<W.length;V++)Y>0&&t.strokeText(W[V],u,l),t.fillText(W[V],u,l),l+=K}else Y>0&&t.strokeText(d,u,l),t.fillText(d,u,l);C!==0&&(t.rotate(-C),t.translate(-f,-h))}}};var Zr={};Zr.drawNode=function(t,e,r){var a=arguments.length>3&&arguments[3]!==void 0?arguments[3]:!0,n=arguments.length>4&&arguments[4]!==void 0?arguments[4]:!0,i=arguments.length>5&&arguments[5]!==void 0?arguments[5]:!0,s=this,o,u,l=e._private,f=l.rscratch,h=e.position();if(!(!ie(h.x)||!ie(h.y))&&!(i&&!e.visible())){var d=i?e.effectiveOpacity():1,c=s.usePaths(),v,p=!1,g=e.padding();o=e.width()+2*g,u=e.height()+2*g;var y;r&&(y=r,t.translate(-y.x1,-y.y1));for(var b=e.pstyle("background-image"),m=b.value,T=new Array(m.length),C=new Array(m.length),S=0,E=0;E<m.length;E++){var w=m[E],x=T[E]=w!=null&&w!=="none";if(x){var D=e.cy().style().getIndexedStyle(e,"background-image-crossorigin","value",E);S++,C[E]=s.getCachedImage(w,D,function(){l.backgroundTimestamp=Date.now(),e.emitAndNotify("background")})}}var L=e.pstyle("background-blacken").value,A=e.pstyle("border-width").pfValue,N=e.pstyle("background-opacity").value*d,O=e.pstyle("border-color").value,M=e.pstyle("border-style").value,R=e.pstyle("border-join").value,k=e.pstyle("border-cap").value,P=e.pstyle("border-position").value,B=e.pstyle("border-dash-pattern").pfValue,z=e.pstyle("border-dash-offset").pfValue,G=e.pstyle("border-opacity").value*d,F=e.pstyle("outline-width").pfValue,U=e.pstyle("outline-color").value,Y=e.pstyle("outline-style").value,W=e.pstyle("outline-opacity").value*d,K=e.pstyle("outline-offset").value,j=e.pstyle("corner-radius").value;j!=="auto"&&(j=e.pstyle("corner-radius").pfValue);var _=function(){var ae=arguments.length>0&&arguments[0]!==void 0?arguments[0]:N;s.eleFillStyle(t,e,ae)},V=function(){var ae=arguments.length>0&&arguments[0]!==void 0?arguments[0]:G;s.colorStrokeStyle(t,O[0],O[1],O[2],ae)},H=function(){var ae=arguments.length>0&&arguments[0]!==void 0?arguments[0]:W;s.colorStrokeStyle(t,U[0],U[1],U[2],ae)},Q=function(ae,Z,re,ye){var me=s.nodePathCache=s.nodePathCache||[],he=bo(re==="polygon"?re+","+ye.join(","):re,""+Z,""+ae,""+j),Ee=me[he],le,ge=!1;return Ee!=null?(le=Ee,ge=!0,f.pathCache=le):(le=new Path2D,me[he]=f.pathCache=le),{path:le,cacheHit:ge}},ne=e.pstyle("shape").strValue,ce=e.pstyle("shape-polygon-points").pfValue;if(c){t.translate(h.x,h.y);var te=Q(o,u,ne,ce);v=te.path,p=te.cacheHit}var se=function(){if(!p){var ae=h;c&&(ae={x:0,y:0}),s.nodeShapes[s.getNodeShape(e)].draw(v||t,ae.x,ae.y,o,u,j,f)}c?t.fill(v):t.fill()},ue=function(){for(var ae=arguments.length>0&&arguments[0]!==void 0?arguments[0]:d,Z=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!0,re=l.backgrounding,ye=0,me=0;me<C.length;me++){var he=e.cy().style().getIndexedStyle(e,"background-image-containment","value",me);if(Z&&he==="over"||!Z&&he==="inside"){ye++;continue}T[me]&&C[me].complete&&!C[me].error&&(ye++,s.drawInscribedImage(t,C[me],e,me,ae))}l.backgrounding=ye!==S,re!==l.backgrounding&&e.updateStyle(!1)},ve=function(){var ae=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!1,Z=arguments.length>1&&arguments[1]!==void 0?arguments[1]:d;s.hasPie(e)&&(s.drawPie(t,e,Z),ae&&(c||s.nodeShapes[s.getNodeShape(e)].draw(t,h.x,h.y,o,u,j,f)))},fe=function(){var ae=arguments.length>0&&arguments[0]!==void 0?arguments[0]:d,Z=(L>0?L:-L)*ae,re=L>0?0:255;L!==0&&(s.colorFillStyle(t,re,re,re,Z),c?t.fill(v):t.fill())},pe=function(){if(A>0){if(t.lineWidth=A,t.lineCap=k,t.lineJoin=R,t.setLineDash)switch(M){case"dotted":t.setLineDash([1,1]);break;case"dashed":t.setLineDash(B),t.lineDashOffset=z;break;case"solid":case"double":t.setLineDash([]);break}if(P!=="center"){if(t.save(),t.lineWidth*=2,P==="inside")c?t.clip(v):t.clip();else{var ae=new Path2D;ae.rect(-o/2-A,-u/2-A,o+2*A,u+2*A),ae.addPath(v),t.clip(ae,"evenodd")}c?t.stroke(v):t.stroke(),t.restore()}else c?t.stroke(v):t.stroke();if(M==="double"){t.lineWidth=A/3;var Z=t.globalCompositeOperation;t.globalCompositeOperation="destination-out",c?t.stroke(v):t.stroke(),t.globalCompositeOperation=Z}t.setLineDash&&t.setLineDash([])}},Ae=function(){if(F>0){if(t.lineWidth=F,t.lineCap="butt",t.setLineDash)switch(Y){case"dotted":t.setLineDash([1,1]);break;case"dashed":t.setLineDash([4,2]);break;case"solid":case"double":t.setLineDash([]);break}var ae=h;c&&(ae={x:0,y:0});var Z=s.getNodeShape(e),re=A;P==="inside"&&(re=0),P==="outside"&&(re*=2);var ye=(o+re+(F+K))/o,me=(u+re+(F+K))/u,he=o*ye,Ee=u*me,le=s.nodeShapes[Z].points,ge;if(c){var Fe=Q(he,Ee,Z,le);ge=Fe.path}if(Z==="ellipse")s.drawEllipsePath(ge||t,ae.x,ae.y,he,Ee);else if(["round-diamond","round-heptagon","round-hexagon","round-octagon","round-pentagon","round-polygon","round-triangle","round-tag"].includes(Z)){var Me=0,lt=0,Ze=0;Z==="round-diamond"?Me=(re+K+F)*1.4:Z==="round-heptagon"?(Me=(re+K+F)*1.075,Ze=-(re/2+K+F)/35):Z==="round-hexagon"?Me=(re+K+F)*1.12:Z==="round-pentagon"?(Me=(re+K+F)*1.13,Ze=-(re/2+K+F)/15):Z==="round-tag"?(Me=(re+K+F)*1.12,lt=(re/2+F+K)*.07):Z==="round-triangle"&&(Me=(re+K+F)*(Math.PI/2),Ze=-(re+K/2+F)/Math.PI),Me!==0&&(ye=(o+Me)/o,he=o*ye,["round-hexagon","round-tag"].includes(Z)||(me=(u+Me)/u,Ee=u*me)),j=j==="auto"?No(he,Ee):j;for(var Ue=he/2,ct=Ee/2,Qe=j+(re+F+K)/2,ft=new Array(le.length/2),xt=new Array(le.length/2),mt=0;mt<le.length/2;mt++)ft[mt]={x:ae.x+lt+Ue*le[mt*2],y:ae.y+Ze+ct*le[mt*2+1]};var vt,It,Vt,Tt,$e=ft.length;for(It=ft[$e-1],vt=0;vt<$e;vt++)Vt=ft[vt%$e],Tt=ft[(vt+1)%$e],xt[vt]=ki(It,Vt,Tt,Qe),It=Vt,Vt=Tt;s.drawRoundPolygonPath(ge||t,ae.x+lt,ae.y+Ze,o*ye,u*me,le,xt)}else if(["roundrectangle","round-rectangle"].includes(Z))j=j==="auto"?pr(he,Ee):j,s.drawRoundRectanglePath(ge||t,ae.x,ae.y,he,Ee,j+(re+F+K)/2);else if(["cutrectangle","cut-rectangle"].includes(Z))j=j==="auto"?xi():j,s.drawCutRectanglePath(ge||t,ae.x,ae.y,he,Ee,null,j+(re+F+K)/4);else if(["bottomroundrectangle","bottom-round-rectangle"].includes(Z))j=j==="auto"?pr(he,Ee):j,s.drawBottomRoundRectanglePath(ge||t,ae.x,ae.y,he,Ee,j+(re+F+K)/2);else if(Z==="barrel")s.drawBarrelPath(ge||t,ae.x,ae.y,he,Ee);else if(Z.startsWith("polygon")||["rhomboid","right-rhomboid","round-tag","tag","vee"].includes(Z)){var We=(re+F+K)/o;le=nn(sn(le,We)),s.drawPolygonPath(ge||t,ae.x,ae.y,o,u,le)}else{var at=(re+F+K)/o;le=nn(sn(le,-at)),s.drawPolygonPath(ge||t,ae.x,ae.y,o,u,le)}if(c?t.stroke(ge):t.stroke(),Y==="double"){t.lineWidth=re/3;var Tr=t.globalCompositeOperation;t.globalCompositeOperation="destination-out",c?t.stroke(ge):t.stroke(),t.globalCompositeOperation=Tr}t.setLineDash&&t.setLineDash([])}},xe=function(){n&&s.drawNodeOverlay(t,e,h,o,u)},we=function(){n&&s.drawNodeUnderlay(t,e,h,o,u)},De=function(){s.drawElementText(t,e,null,a)},ee=e.pstyle("ghost").value==="yes";if(ee){var I=e.pstyle("ghost-offset-x").pfValue,$=e.pstyle("ghost-offset-y").pfValue,J=e.pstyle("ghost-opacity").value,q=J*d;t.translate(I,$),H(),Ae(),_(J*N),se(),ue(q,!0),V(J*G),pe(),ve(L!==0||A!==0),ue(q,!1),fe(q),t.translate(-I,-$)}c&&t.translate(-h.x,-h.y),we(),c&&t.translate(h.x,h.y),H(),Ae(),_(),se(),ue(d,!0),V(),pe(),ve(L!==0||A!==0),ue(d,!1),fe(),c&&t.translate(-h.x,-h.y),De(),xe(),r&&t.translate(y.x1,y.y1)}};var Ru=function(e){if(!["overlay","underlay"].includes(e))throw new Error("Invalid state");return function(r,a,n,i,s){var o=this;if(a.visible()){var u=a.pstyle("".concat(e,"-padding")).pfValue,l=a.pstyle("".concat(e,"-opacity")).value,f=a.pstyle("".concat(e,"-color")).value,h=a.pstyle("".concat(e,"-shape")).value,d=a.pstyle("".concat(e,"-corner-radius")).value;if(l>0){if(n=n||a.position(),i==null||s==null){var c=a.padding();i=a.width()+2*c,s=a.height()+2*c}o.colorFillStyle(r,f[0],f[1],f[2],l),o.nodeShapes[h].draw(r,n.x,n.y,i+u*2,s+u*2,d),r.fill()}}}};Zr.drawNodeOverlay=Ru("overlay");Zr.drawNodeUnderlay=Ru("underlay");Zr.hasPie=function(t){return t=t[0],t._private.hasPie};Zr.drawPie=function(t,e,r,a){e=e[0],a=a||e.position();var n=e.cy().style(),i=e.pstyle("pie-size"),s=a.x,o=a.y,u=e.width(),l=e.height(),f=Math.min(u,l)/2,h=0,d=this.usePaths();d&&(s=0,o=0),i.units==="%"?f=f*i.pfValue:i.pfValue!==void 0&&(f=i.pfValue/2);for(var c=1;c<=n.pieBackgroundN;c++){var v=e.pstyle("pie-"+c+"-background-size").value,p=e.pstyle("pie-"+c+"-background-color").value,g=e.pstyle("pie-"+c+"-background-opacity").value*r,y=v/100;y+h>1&&(y=1-h);var b=1.5*Math.PI+2*Math.PI*h,m=2*Math.PI*y,T=b+m;v===0||h>=1||h+y>1||(t.beginPath(),t.moveTo(s,o),t.arc(s,o,f,b,T),t.closePath(),this.colorFillStyle(t,p[0],p[1],p[2],g),t.fill(),h+=y)}};var yt={},Bp=100;yt.getPixelRatio=function(){var t=this.data.contexts[0];if(this.forcedPixelRatio!=null)return this.forcedPixelRatio;var e=this.cy.window(),r=t.backingStorePixelRatio||t.webkitBackingStorePixelRatio||t.mozBackingStorePixelRatio||t.msBackingStorePixelRatio||t.oBackingStorePixelRatio||t.backingStorePixelRatio||1;return(e.devicePixelRatio||1)/r};yt.paintCache=function(t){for(var e=this.paintCaches=this.paintCaches||[],r=!0,a,n=0;n<e.length;n++)if(a=e[n],a.context===t){r=!1;break}return r&&(a={context:t},e.push(a)),a};yt.createGradientStyleFor=function(t,e,r,a,n){var i,s=this.usePaths(),o=r.pstyle(e+"-gradient-stop-colors").value,u=r.pstyle(e+"-gradient-stop-positions").pfValue;if(a==="radial-gradient")if(r.isEdge()){var l=r.sourceEndpoint(),f=r.targetEndpoint(),h=r.midpoint(),d=gr(l,h),c=gr(f,h);i=t.createRadialGradient(h.x,h.y,0,h.x,h.y,Math.max(d,c))}else{var v=s?{x:0,y:0}:r.position(),p=r.paddedWidth(),g=r.paddedHeight();i=t.createRadialGradient(v.x,v.y,0,v.x,v.y,Math.max(p,g))}else if(r.isEdge()){var y=r.sourceEndpoint(),b=r.targetEndpoint();i=t.createLinearGradient(y.x,y.y,b.x,b.y)}else{var m=s?{x:0,y:0}:r.position(),T=r.paddedWidth(),C=r.paddedHeight(),S=T/2,E=C/2,w=r.pstyle("background-gradient-direction").value;switch(w){case"to-bottom":i=t.createLinearGradient(m.x,m.y-E,m.x,m.y+E);break;case"to-top":i=t.createLinearGradient(m.x,m.y+E,m.x,m.y-E);break;case"to-left":i=t.createLinearGradient(m.x+S,m.y,m.x-S,m.y);break;case"to-right":i=t.createLinearGradient(m.x-S,m.y,m.x+S,m.y);break;case"to-bottom-right":case"to-right-bottom":i=t.createLinearGradient(m.x-S,m.y-E,m.x+S,m.y+E);break;case"to-top-right":case"to-right-top":i=t.createLinearGradient(m.x-S,m.y+E,m.x+S,m.y-E);break;case"to-bottom-left":case"to-left-bottom":i=t.createLinearGradient(m.x+S,m.y-E,m.x-S,m.y+E);break;case"to-top-left":case"to-left-top":i=t.createLinearGradient(m.x+S,m.y+E,m.x-S,m.y-E);break}}if(!i)return null;for(var x=u.length===o.length,D=o.length,L=0;L<D;L++)i.addColorStop(x?u[L]:L/(D-1),"rgba("+o[L][0]+","+o[L][1]+","+o[L][2]+","+n+")");return i};yt.gradientFillStyle=function(t,e,r,a){var n=this.createGradientStyleFor(t,"background",e,r,a);if(!n)return null;t.fillStyle=n};yt.colorFillStyle=function(t,e,r,a,n){t.fillStyle="rgba("+e+","+r+","+a+","+n+")"};yt.eleFillStyle=function(t,e,r){var a=e.pstyle("background-fill").value;if(a==="linear-gradient"||a==="radial-gradient")this.gradientFillStyle(t,e,a,r);else{var n=e.pstyle("background-color").value;this.colorFillStyle(t,n[0],n[1],n[2],r)}};yt.gradientStrokeStyle=function(t,e,r,a){var n=this.createGradientStyleFor(t,"line",e,r,a);if(!n)return null;t.strokeStyle=n};yt.colorStrokeStyle=function(t,e,r,a,n){t.strokeStyle="rgba("+e+","+r+","+a+","+n+")"};yt.eleStrokeStyle=function(t,e,r){var a=e.pstyle("line-fill").value;if(a==="linear-gradient"||a==="radial-gradient")this.gradientStrokeStyle(t,e,a,r);else{var n=e.pstyle("line-color").value;this.colorStrokeStyle(t,n[0],n[1],n[2],r)}};yt.matchCanvasSize=function(t){var e=this,r=e.data,a=e.findContainerClientCoords(),n=a[2],i=a[3],s=e.getPixelRatio(),o=e.motionBlurPxRatio;(t===e.data.bufferCanvases[e.MOTIONBLUR_BUFFER_NODE]||t===e.data.bufferCanvases[e.MOTIONBLUR_BUFFER_DRAG])&&(s=o);var u=n*s,l=i*s,f;if(!(u===e.canvasWidth&&l===e.canvasHeight)){e.fontCaches=null;var h=r.canvasContainer;h.style.width=n+"px",h.style.height=i+"px";for(var d=0;d<e.CANVAS_LAYERS;d++)f=r.canvases[d],f.width=u,f.height=l,f.style.width=n+"px",f.style.height=i+"px";for(var d=0;d<e.BUFFER_COUNT;d++)f=r.bufferCanvases[d],f.width=u,f.height=l,f.style.width=n+"px",f.style.height=i+"px";e.textureMult=1,s<=1&&(f=r.bufferCanvases[e.TEXTURE_BUFFER],e.textureMult=2,f.width=u*e.textureMult,f.height=l*e.textureMult),e.canvasWidth=u,e.canvasHeight=l}};yt.renderTo=function(t,e,r,a){this.render({forcedContext:t,forcedZoom:e,forcedPan:r,drawAllLayers:!0,forcedPxRatio:a})};yt.render=function(t){t=t||To();var e=t.forcedContext,r=t.drawAllLayers,a=t.drawOnlyNodeLayer,n=t.forcedZoom,i=t.forcedPan,s=this,o=t.forcedPxRatio===void 0?this.getPixelRatio():t.forcedPxRatio,u=s.cy,l=s.data,f=l.canvasNeedsRedraw,h=s.textureOnViewport&&!e&&(s.pinching||s.hoverData.dragging||s.swipePanning||s.data.wheelZooming),d=t.motionBlur!==void 0?t.motionBlur:s.motionBlur,c=s.motionBlurPxRatio,v=u.hasCompoundNodes(),p=s.hoverData.draggingEles,g=!!(s.hoverData.selecting||s.touchData.selecting);d=d&&!e&&s.motionBlurEnabled&&!g;var y=d;e||(s.prevPxRatio!==o&&(s.invalidateContainerClientCoordsCache(),s.matchCanvasSize(s.container),s.redrawHint("eles",!0),s.redrawHint("drag",!0)),s.prevPxRatio=o),!e&&s.motionBlurTimeout&&clearTimeout(s.motionBlurTimeout),d&&(s.mbFrames==null&&(s.mbFrames=0),s.mbFrames++,s.mbFrames<3&&(y=!1),s.mbFrames>s.minMbLowQualFrames&&(s.motionBlurPxRatio=s.mbPxRBlurry)),s.clearingMotionBlur&&(s.motionBlurPxRatio=1),s.textureDrawLastFrame&&!h&&(f[s.NODE]=!0,f[s.SELECT_BOX]=!0);var b=u.style(),m=u.zoom(),T=n!==void 0?n:m,C=u.pan(),S={x:C.x,y:C.y},E={zoom:m,pan:{x:C.x,y:C.y}},w=s.prevViewport,x=w===void 0||E.zoom!==w.zoom||E.pan.x!==w.pan.x||E.pan.y!==w.pan.y;!x&&!(p&&!v)&&(s.motionBlurPxRatio=1),i&&(S=i),T*=o,S.x*=o,S.y*=o;var D=s.getCachedZSortedEles();function L(te,se,ue,ve,fe){var pe=te.globalCompositeOperation;te.globalCompositeOperation="destination-out",s.colorFillStyle(te,255,255,255,s.motionBlurTransparency),te.fillRect(se,ue,ve,fe),te.globalCompositeOperation=pe}function A(te,se){var ue,ve,fe,pe;!s.clearingMotionBlur&&(te===l.bufferContexts[s.MOTIONBLUR_BUFFER_NODE]||te===l.bufferContexts[s.MOTIONBLUR_BUFFER_DRAG])?(ue={x:C.x*c,y:C.y*c},ve=m*c,fe=s.canvasWidth*c,pe=s.canvasHeight*c):(ue=S,ve=T,fe=s.canvasWidth,pe=s.canvasHeight),te.setTransform(1,0,0,1,0,0),se==="motionBlur"?L(te,0,0,fe,pe):!e&&(se===void 0||se)&&te.clearRect(0,0,fe,pe),r||(te.translate(ue.x,ue.y),te.scale(ve,ve)),i&&te.translate(i.x,i.y),n&&te.scale(n,n)}if(h||(s.textureDrawLastFrame=!1),h){if(s.textureDrawLastFrame=!0,!s.textureCache){s.textureCache={},s.textureCache.bb=u.mutableElements().boundingBox(),s.textureCache.texture=s.data.bufferCanvases[s.TEXTURE_BUFFER];var N=s.data.bufferContexts[s.TEXTURE_BUFFER];N.setTransform(1,0,0,1,0,0),N.clearRect(0,0,s.canvasWidth*s.textureMult,s.canvasHeight*s.textureMult),s.render({forcedContext:N,drawOnlyNodeLayer:!0,forcedPxRatio:o*s.textureMult});var E=s.textureCache.viewport={zoom:u.zoom(),pan:u.pan(),width:s.canvasWidth,height:s.canvasHeight};E.mpan={x:(0-E.pan.x)/E.zoom,y:(0-E.pan.y)/E.zoom}}f[s.DRAG]=!1,f[s.NODE]=!1;var O=l.contexts[s.NODE],M=s.textureCache.texture,E=s.textureCache.viewport;O.setTransform(1,0,0,1,0,0),d?L(O,0,0,E.width,E.height):O.clearRect(0,0,E.width,E.height);var R=b.core("outside-texture-bg-color").value,k=b.core("outside-texture-bg-opacity").value;s.colorFillStyle(O,R[0],R[1],R[2],k),O.fillRect(0,0,E.width,E.height);var m=u.zoom();A(O,!1),O.clearRect(E.mpan.x,E.mpan.y,E.width/E.zoom/o,E.height/E.zoom/o),O.drawImage(M,E.mpan.x,E.mpan.y,E.width/E.zoom/o,E.height/E.zoom/o)}else s.textureOnViewport&&!e&&(s.textureCache=null);var P=u.extent(),B=s.pinching||s.hoverData.dragging||s.swipePanning||s.data.wheelZooming||s.hoverData.draggingEles||s.cy.animated(),z=s.hideEdgesOnViewport&&B,G=[];if(G[s.NODE]=!f[s.NODE]&&d&&!s.clearedForMotionBlur[s.NODE]||s.clearingMotionBlur,G[s.NODE]&&(s.clearedForMotionBlur[s.NODE]=!0),G[s.DRAG]=!f[s.DRAG]&&d&&!s.clearedForMotionBlur[s.DRAG]||s.clearingMotionBlur,G[s.DRAG]&&(s.clearedForMotionBlur[s.DRAG]=!0),f[s.NODE]||r||a||G[s.NODE]){var F=d&&!G[s.NODE]&&c!==1,O=e||(F?s.data.bufferContexts[s.MOTIONBLUR_BUFFER_NODE]:l.contexts[s.NODE]),U=d&&!F?"motionBlur":void 0;A(O,U),z?s.drawCachedNodes(O,D.nondrag,o,P):s.drawLayeredElements(O,D.nondrag,o,P),s.debug&&s.drawDebugPoints(O,D.nondrag),!r&&!d&&(f[s.NODE]=!1)}if(!a&&(f[s.DRAG]||r||G[s.DRAG])){var F=d&&!G[s.DRAG]&&c!==1,O=e||(F?s.data.bufferContexts[s.MOTIONBLUR_BUFFER_DRAG]:l.contexts[s.DRAG]);A(O,d&&!F?"motionBlur":void 0),z?s.drawCachedNodes(O,D.drag,o,P):s.drawCachedElements(O,D.drag,o,P),s.debug&&s.drawDebugPoints(O,D.drag),!r&&!d&&(f[s.DRAG]=!1)}if(s.showFps||!a&&f[s.SELECT_BOX]&&!r){var O=e||l.contexts[s.SELECT_BOX];if(A(O),s.selection[4]==1&&(s.hoverData.selecting||s.touchData.selecting)){var m=s.cy.zoom(),Y=b.core("selection-box-border-width").value/m;O.lineWidth=Y,O.fillStyle="rgba("+b.core("selection-box-color").value[0]+","+b.core("selection-box-color").value[1]+","+b.core("selection-box-color").value[2]+","+b.core("selection-box-opacity").value+")",O.fillRect(s.selection[0],s.selection[1],s.selection[2]-s.selection[0],s.selection[3]-s.selection[1]),Y>0&&(O.strokeStyle="rgba("+b.core("selection-box-border-color").value[0]+","+b.core("selection-box-border-color").value[1]+","+b.core("selection-box-border-color").value[2]+","+b.core("selection-box-opacity").value+")",O.strokeRect(s.selection[0],s.selection[1],s.selection[2]-s.selection[0],s.selection[3]-s.selection[1]))}if(l.bgActivePosistion&&!s.hoverData.selecting){var m=s.cy.zoom(),W=l.bgActivePosistion;O.fillStyle="rgba("+b.core("active-bg-color").value[0]+","+b.core("active-bg-color").value[1]+","+b.core("active-bg-color").value[2]+","+b.core("active-bg-opacity").value+")",O.beginPath(),O.arc(W.x,W.y,b.core("active-bg-size").pfValue/m,0,2*Math.PI),O.fill()}var K=s.lastRedrawTime;if(s.showFps&&K){K=Math.round(K);var j=Math.round(1e3/K);O.setTransform(1,0,0,1,0,0),O.fillStyle="rgba(255, 0, 0, 0.75)",O.strokeStyle="rgba(255, 0, 0, 0.75)",O.lineWidth=1,O.fillText("1 frame = "+K+" ms = "+j+" fps",0,20);var _=60;O.strokeRect(0,30,250,20),O.fillRect(0,30,250*Math.min(j/_,1),20)}r||(f[s.SELECT_BOX]=!1)}if(d&&c!==1){var V=l.contexts[s.NODE],H=s.data.bufferCanvases[s.MOTIONBLUR_BUFFER_NODE],Q=l.contexts[s.DRAG],ne=s.data.bufferCanvases[s.MOTIONBLUR_BUFFER_DRAG],ce=function(se,ue,ve){se.setTransform(1,0,0,1,0,0),ve||!y?se.clearRect(0,0,s.canvasWidth,s.canvasHeight):L(se,0,0,s.canvasWidth,s.canvasHeight);var fe=c;se.drawImage(ue,0,0,s.canvasWidth*fe,s.canvasHeight*fe,0,0,s.canvasWidth,s.canvasHeight)};(f[s.NODE]||G[s.NODE])&&(ce(V,H,G[s.NODE]),f[s.NODE]=!1),(f[s.DRAG]||G[s.DRAG])&&(ce(Q,ne,G[s.DRAG]),f[s.DRAG]=!1)}s.prevViewport=E,s.clearingMotionBlur&&(s.clearingMotionBlur=!1,s.motionBlurCleared=!0,s.motionBlur=!0),d&&(s.motionBlurTimeout=setTimeout(function(){s.motionBlurTimeout=null,s.clearedForMotionBlur[s.NODE]=!1,s.clearedForMotionBlur[s.DRAG]=!1,s.motionBlur=!1,s.clearingMotionBlur=!h,s.mbFrames=0,f[s.NODE]=!0,f[s.DRAG]=!0,s.redraw()},Bp)),e||u.emit("render")};var sr={};sr.drawPolygonPath=function(t,e,r,a,n,i){var s=a/2,o=n/2;t.beginPath&&t.beginPath(),t.moveTo(e+s*i[0],r+o*i[1]);for(var u=1;u<i.length/2;u++)t.lineTo(e+s*i[u*2],r+o*i[u*2+1]);t.closePath()};sr.drawRoundPolygonPath=function(t,e,r,a,n,i,s){s.forEach(function(o){return wu(t,o)}),t.closePath()};sr.drawRoundRectanglePath=function(t,e,r,a,n,i){var s=a/2,o=n/2,u=i==="auto"?pr(a,n):Math.min(i,o,s);t.beginPath&&t.beginPath(),t.moveTo(e,r-o),t.arcTo(e+s,r-o,e+s,r,u),t.arcTo(e+s,r+o,e,r+o,u),t.arcTo(e-s,r+o,e-s,r,u),t.arcTo(e-s,r-o,e,r-o,u),t.lineTo(e,r-o),t.closePath()};sr.drawBottomRoundRectanglePath=function(t,e,r,a,n,i){var s=a/2,o=n/2,u=i==="auto"?pr(a,n):i;t.beginPath&&t.beginPath(),t.moveTo(e,r-o),t.lineTo(e+s,r-o),t.lineTo(e+s,r),t.arcTo(e+s,r+o,e,r+o,u),t.arcTo(e-s,r+o,e-s,r,u),t.lineTo(e-s,r-o),t.lineTo(e,r-o),t.closePath()};sr.drawCutRectanglePath=function(t,e,r,a,n,i,s){var o=a/2,u=n/2,l=s==="auto"?xi():s;t.beginPath&&t.beginPath(),t.moveTo(e-o+l,r-u),t.lineTo(e+o-l,r-u),t.lineTo(e+o,r-u+l),t.lineTo(e+o,r+u-l),t.lineTo(e+o-l,r+u),t.lineTo(e-o+l,r+u),t.lineTo(e-o,r+u-l),t.lineTo(e-o,r-u+l),t.closePath()};sr.drawBarrelPath=function(t,e,r,a,n){var i=a/2,s=n/2,o=e-i,u=e+i,l=r-s,f=r+s,h=Kn(a,n),d=h.widthOffset,c=h.heightOffset,v=h.ctrlPtOffsetPct*d;t.beginPath&&t.beginPath(),t.moveTo(o,l+c),t.lineTo(o,f-c),t.quadraticCurveTo(o+v,f,o+d,f),t.lineTo(u-d,f),t.quadraticCurveTo(u-v,f,u,f-c),t.lineTo(u,l+c),t.quadraticCurveTo(u-v,l,u-d,l),t.lineTo(o+d,l),t.quadraticCurveTo(o+v,l,o,l+c),t.closePath()};var Js=Math.sin(0),js=Math.cos(0),oi={},ui={},ku=Math.PI/40;for(var Or=0*Math.PI;Or<2*Math.PI;Or+=ku)oi[Or]=Math.sin(Or),ui[Or]=Math.cos(Or);sr.drawEllipsePath=function(t,e,r,a,n){if(t.beginPath&&t.beginPath(),t.ellipse)t.ellipse(e,r,a/2,n/2,0,0,2*Math.PI);else for(var i,s,o=a/2,u=n/2,l=0*Math.PI;l<2*Math.PI;l+=ku)i=e-o*oi[l]*Js+o*ui[l]*js,s=r+u*ui[l]*Js+u*oi[l]*js,l===0?t.moveTo(i,s):t.lineTo(i,s);t.closePath()};var Na={};Na.createBuffer=function(t,e){var r=document.createElement("canvas");return r.width=t,r.height=e,[r,r.getContext("2d")]};Na.bufferCanvasImage=function(t){var e=this.cy,r=e.mutableElements(),a=r.boundingBox(),n=this.findContainerClientCoords(),i=t.full?Math.ceil(a.w):n[2],s=t.full?Math.ceil(a.h):n[3],o=ie(t.maxWidth)||ie(t.maxHeight),u=this.getPixelRatio(),l=1;if(t.scale!==void 0)i*=t.scale,s*=t.scale,l=t.scale;else if(o){var f=1/0,h=1/0;ie(t.maxWidth)&&(f=l*t.maxWidth/i),ie(t.maxHeight)&&(h=l*t.maxHeight/s),l=Math.min(f,h),i*=l,s*=l}o||(i*=u,s*=u,l*=u);var d=document.createElement("canvas");d.width=i,d.height=s,d.style.width=i+"px",d.style.height=s+"px";var c=d.getContext("2d");if(i>0&&s>0){c.clearRect(0,0,i,s),c.globalCompositeOperation="source-over";var v=this.getCachedZSortedEles();if(t.full)c.translate(-a.x1*l,-a.y1*l),c.scale(l,l),this.drawElements(c,v),c.scale(1/l,1/l),c.translate(a.x1*l,a.y1*l);else{var p=e.pan(),g={x:p.x*l,y:p.y*l};l*=e.zoom(),c.translate(g.x,g.y),c.scale(l,l),this.drawElements(c,v),c.scale(1/l,1/l),c.translate(-g.x,-g.y)}t.bg&&(c.globalCompositeOperation="destination-over",c.fillStyle=t.bg,c.rect(0,0,i,s),c.fill())}return d};function Fp(t,e){for(var r=atob(t),a=new ArrayBuffer(r.length),n=new Uint8Array(a),i=0;i<r.length;i++)n[i]=r.charCodeAt(i);return new Blob([a],{type:e})}function eo(t){var e=t.indexOf(",");return t.substr(e+1)}function Pu(t,e,r){var a=function(){return e.toDataURL(r,t.quality)};switch(t.output){case"blob-promise":return new $r(function(n,i){try{e.toBlob(function(s){s!=null?n(s):i(new Error("`canvas.toBlob()` sent a null value in its callback"))},r,t.quality)}catch(s){i(s)}});case"blob":return Fp(eo(a()),r);case"base64":return eo(a());case"base64uri":default:return a()}}Na.png=function(t){return Pu(t,this.bufferCanvasImage(t),"image/png")};Na.jpg=function(t){return Pu(t,this.bufferCanvasImage(t),"image/jpeg")};var Bu={};Bu.nodeShapeImpl=function(t,e,r,a,n,i,s,o){switch(t){case"ellipse":return this.drawEllipsePath(e,r,a,n,i);case"polygon":return this.drawPolygonPath(e,r,a,n,i,s);case"round-polygon":return this.drawRoundPolygonPath(e,r,a,n,i,s,o);case"roundrectangle":case"round-rectangle":return this.drawRoundRectanglePath(e,r,a,n,i,o);case"cutrectangle":case"cut-rectangle":return this.drawCutRectanglePath(e,r,a,n,i,s,o);case"bottomroundrectangle":case"bottom-round-rectangle":return this.drawBottomRoundRectanglePath(e,r,a,n,i,o);case"barrel":return this.drawBarrelPath(e,r,a,n,i)}};var Gp=Fu,Se=Fu.prototype;Se.CANVAS_LAYERS=3;Se.SELECT_BOX=0;Se.DRAG=1;Se.NODE=2;Se.BUFFER_COUNT=3;Se.TEXTURE_BUFFER=0;Se.MOTIONBLUR_BUFFER_NODE=1;Se.MOTIONBLUR_BUFFER_DRAG=2;function Fu(t){var e=this,r=e.cy.window(),a=r.document;e.data={canvases:new Array(Se.CANVAS_LAYERS),contexts:new Array(Se.CANVAS_LAYERS),canvasNeedsRedraw:new Array(Se.CANVAS_LAYERS),bufferCanvases:new Array(Se.BUFFER_COUNT),bufferContexts:new Array(Se.CANVAS_LAYERS)};var n="-webkit-tap-highlight-color",i="rgba(0,0,0,0)";e.data.canvasContainer=a.createElement("div");var s=e.data.canvasContainer.style;e.data.canvasContainer.style[n]=i,s.position="relative",s.zIndex="0",s.overflow="hidden";var o=t.cy.container();o.appendChild(e.data.canvasContainer),o.style[n]=i;var u={"-webkit-user-select":"none","-moz-user-select":"-moz-none","user-select":"none","-webkit-tap-highlight-color":"rgba(0,0,0,0)","outline-style":"none"};Cl()&&(u["-ms-touch-action"]="none",u["touch-action"]="none");for(var l=0;l<Se.CANVAS_LAYERS;l++){var f=e.data.canvases[l]=a.createElement("canvas");e.data.contexts[l]=f.getContext("2d"),Object.keys(u).forEach(function(_){f.style[_]=u[_]}),f.style.position="absolute",f.setAttribute("data-id","layer"+l),f.style.zIndex=String(Se.CANVAS_LAYERS-l),e.data.canvasContainer.appendChild(f),e.data.canvasNeedsRedraw[l]=!1}e.data.topCanvas=e.data.canvases[0],e.data.canvases[Se.NODE].setAttribute("data-id","layer"+Se.NODE+"-node"),e.data.canvases[Se.SELECT_BOX].setAttribute("data-id","layer"+Se.SELECT_BOX+"-selectbox"),e.data.canvases[Se.DRAG].setAttribute("data-id","layer"+Se.DRAG+"-drag");for(var l=0;l<Se.BUFFER_COUNT;l++)e.data.bufferCanvases[l]=a.createElement("canvas"),e.data.bufferContexts[l]=e.data.bufferCanvases[l].getContext("2d"),e.data.bufferCanvases[l].style.position="absolute",e.data.bufferCanvases[l].setAttribute("data-id","buffer"+l),e.data.bufferCanvases[l].style.zIndex=String(-l-1),e.data.bufferCanvases[l].style.visibility="hidden";e.pathsEnabled=!0;var h=gt(),d=function(V){return{x:(V.x1+V.x2)/2,y:(V.y1+V.y2)/2}},c=function(V){return{x:-V.w/2,y:-V.h/2}},v=function(V){var H=V[0]._private,Q=H.oldBackgroundTimestamp===H.backgroundTimestamp;return!Q},p=function(V){return V[0]._private.nodeKey},g=function(V){return V[0]._private.labelStyleKey},y=function(V){return V[0]._private.sourceLabelStyleKey},b=function(V){return V[0]._private.targetLabelStyleKey},m=function(V,H,Q,ne,ce){return e.drawElement(V,H,Q,!1,!1,ce)},T=function(V,H,Q,ne,ce){return e.drawElementText(V,H,Q,ne,"main",ce)},C=function(V,H,Q,ne,ce){return e.drawElementText(V,H,Q,ne,"source",ce)},S=function(V,H,Q,ne,ce){return e.drawElementText(V,H,Q,ne,"target",ce)},E=function(V){return V.boundingBox(),V[0]._private.bodyBounds},w=function(V){return V.boundingBox(),V[0]._private.labelBounds.main||h},x=function(V){return V.boundingBox(),V[0]._private.labelBounds.source||h},D=function(V){return V.boundingBox(),V[0]._private.labelBounds.target||h},L=function(V,H){return H},A=function(V){return d(E(V))},N=function(V,H,Q){var ne=V?V+"-":"";return{x:H.x+Q.pstyle(ne+"text-margin-x").pfValue,y:H.y+Q.pstyle(ne+"text-margin-y").pfValue}},O=function(V,H,Q){var ne=V[0]._private.rscratch;return{x:ne[H],y:ne[Q]}},M=function(V){return N("",O(V,"labelX","labelY"),V)},R=function(V){return N("source",O(V,"sourceLabelX","sourceLabelY"),V)},k=function(V){return N("target",O(V,"targetLabelX","targetLabelY"),V)},P=function(V){return c(E(V))},B=function(V){return c(x(V))},z=function(V){return c(D(V))},G=function(V){var H=w(V),Q=c(w(V));if(V.isNode()){switch(V.pstyle("text-halign").value){case"left":Q.x=-H.w;break;case"right":Q.x=0;break}switch(V.pstyle("text-valign").value){case"top":Q.y=-H.h;break;case"bottom":Q.y=0;break}}return Q},F=e.data.eleTxrCache=new ua(e,{getKey:p,doesEleInvalidateKey:v,drawElement:m,getBoundingBox:E,getRotationPoint:A,getRotationOffset:P,allowEdgeTxrCaching:!1,allowParentTxrCaching:!1}),U=e.data.lblTxrCache=new ua(e,{getKey:g,drawElement:T,getBoundingBox:w,getRotationPoint:M,getRotationOffset:G,isVisible:L}),Y=e.data.slbTxrCache=new ua(e,{getKey:y,drawElement:C,getBoundingBox:x,getRotationPoint:R,getRotationOffset:B,isVisible:L}),W=e.data.tlbTxrCache=new ua(e,{getKey:b,drawElement:S,getBoundingBox:D,getRotationPoint:k,getRotationOffset:z,isVisible:L}),K=e.data.lyrTxrCache=new Nu(e);e.onUpdateEleCalcs(function(V,H){F.invalidateElements(H),U.invalidateElements(H),Y.invalidateElements(H),W.invalidateElements(H),K.invalidateElements(H);for(var Q=0;Q<H.length;Q++){var ne=H[Q]._private;ne.oldBackgroundTimestamp=ne.backgroundTimestamp}});var j=function(V){for(var H=0;H<V.length;H++)K.enqueueElementRefinement(V[H].ele)};F.onDequeue(j),U.onDequeue(j),Y.onDequeue(j),W.onDequeue(j)}Se.redrawHint=function(t,e){var r=this;switch(t){case"eles":r.data.canvasNeedsRedraw[Se.NODE]=e;break;case"drag":r.data.canvasNeedsRedraw[Se.DRAG]=e;break;case"select":r.data.canvasNeedsRedraw[Se.SELECT_BOX]=e;break}};var zp=typeof Path2D<"u";Se.path2dEnabled=function(t){if(t===void 0)return this.pathsEnabled;this.pathsEnabled=!!t};Se.usePaths=function(){return zp&&this.pathsEnabled};Se.setImgSmoothing=function(t,e){t.imageSmoothingEnabled!=null?t.imageSmoothingEnabled=e:(t.webkitImageSmoothingEnabled=e,t.mozImageSmoothingEnabled=e,t.msImageSmoothingEnabled=e)};Se.getImgSmoothing=function(t){return t.imageSmoothingEnabled!=null?t.imageSmoothingEnabled:t.webkitImageSmoothingEnabled||t.mozImageSmoothingEnabled||t.msImageSmoothingEnabled};Se.makeOffscreenCanvas=function(t,e){var r;if((typeof OffscreenCanvas>"u"?"undefined":Xe(OffscreenCanvas))!=="undefined")r=new OffscreenCanvas(t,e);else{var a=this.cy.window(),n=a.document;r=n.createElement("canvas"),r.width=t,r.height=e}return r};[Iu,zt,Xt,Bi,xr,Zr,yt,sr,Na,Bu].forEach(function(t){be(Se,t)});var Vp=[{name:"null",impl:mu},{name:"base",impl:Au},{name:"canvas",impl:Gp}],Up=[{type:"layout",extensions:qg},{type:"renderer",extensions:Vp}],Gu={},zu={};function Vu(t,e,r){var a=r,n=function(w){Ne("Can not register `"+e+"` for `"+t+"` since `"+w+"` already exists in the prototype and can not be overridden")};if(t==="core"){if(wa.prototype[e])return n(e);wa.prototype[e]=r}else if(t==="collection"){if(et.prototype[e])return n(e);et.prototype[e]=r}else if(t==="layout"){for(var i=function(w){this.options=w,r.call(this,w),Ce(this._private)||(this._private={}),this._private.cy=w.cy,this._private.listeners=[],this.createEmitter()},s=i.prototype=Object.create(r.prototype),o=[],u=0;u<o.length;u++){var l=o[u];s[l]=s[l]||function(){return this}}s.start&&!s.run?s.run=function(){return this.start(),this}:!s.start&&s.run&&(s.start=function(){return this.run(),this});var f=r.prototype.stop;s.stop=function(){var E=this.options;if(E&&E.animate){var w=this.animations;if(w)for(var x=0;x<w.length;x++)w[x].stop()}return f?f.call(this):this.emit("layoutstop"),this},s.destroy||(s.destroy=function(){return this}),s.cy=function(){return this._private.cy};var h=function(w){return w._private.cy},d={addEventFields:function(w,x){x.layout=w,x.cy=h(w),x.target=w},bubble:function(){return!0},parent:function(w){return h(w)}};be(s,{createEmitter:function(){return this._private.emitter=new Tn(d,this),this},emitter:function(){return this._private.emitter},on:function(w,x){return this.emitter().on(w,x),this},one:function(w,x){return this.emitter().one(w,x),this},once:function(w,x){return this.emitter().one(w,x),this},removeListener:function(w,x){return this.emitter().removeListener(w,x),this},removeAllListeners:function(){return this.emitter().removeAllListeners(),this},emit:function(w,x){return this.emitter().emit(w,x),this}}),Oe.eventAliasesOn(s),a=i}else if(t==="renderer"&&e!=="null"&&e!=="base"){var c=Uu("renderer","base"),v=c.prototype,p=r,g=r.prototype,y=function(){c.apply(this,arguments),p.apply(this,arguments)},b=y.prototype;for(var m in v){var T=v[m],C=g[m]!=null;if(C)return n(m);b[m]=T}for(var S in g)b[S]=g[S];v.clientFunctions.forEach(function(E){b[E]=b[E]||function(){ze("Renderer does not implement `renderer."+E+"()` on its prototype")}}),a=y}else if(t==="__proto__"||t==="constructor"||t==="prototype")return ze(t+" is an illegal type to be registered, possibly lead to prototype pollutions");return ho({map:Gu,keys:[t,e],value:a})}function Uu(t,e){return co({map:Gu,keys:[t,e]})}function $p(t,e,r,a,n){return ho({map:zu,keys:[t,e,r,a],value:n})}function Yp(t,e,r,a){return co({map:zu,keys:[t,e,r,a]})}var li=function(){if(arguments.length===2)return Uu.apply(null,arguments);if(arguments.length===3)return Vu.apply(null,arguments);if(arguments.length===4)return Yp.apply(null,arguments);if(arguments.length===5)return $p.apply(null,arguments);ze("Invalid extension access syntax")};wa.prototype.extension=li;Up.forEach(function(t){t.extensions.forEach(function(e){Vu(t.type,e.name,e.impl)})});var $u=function t(){if(!(this instanceof t))return new t;this.length=0},br=$u.prototype;br.instanceString=function(){return"stylesheet"};br.selector=function(t){var e=this.length++;return this[e]={selector:t,properties:[]},this};br.css=function(t,e){var r=this.length-1;if(de(t))this[r].properties.push({name:t,value:e});else if(Ce(t))for(var a=t,n=Object.keys(a),i=0;i<n.length;i++){var s=n[i],o=a[s];if(o!=null){var u=nt.properties[s]||nt.properties[vn(s)];if(u!=null){var l=u.name,f=o;this[r].properties.push({name:l,value:f})}}}return this};br.style=br.css;br.generateStyle=function(t){var e=new nt(t);return this.appendToStyle(e)};br.appendToStyle=function(t){for(var e=0;e<this.length;e++){var r=this[e],a=r.selector,n=r.properties;t.selector(a);for(var i=0;i<n.length;i++){var s=n[i];t.css(s.name,s.value)}}return t};var _p="3.30.2",nr=function(e){if(e===void 0&&(e={}),Ce(e))return new wa(e);if(de(e))return li.apply(li,arguments)};nr.use=function(t){var e=Array.prototype.slice.call(arguments,1);return e.unshift(nr),t.apply(null,e),this};nr.warnings=function(t){return wo(t)};nr.version=_p;nr.stylesheet=nr.Stylesheet=$u;var Yu={exports:{}},Xn={exports:{}},qn={exports:{}},to;function Hp(){return to||(to=1,function(t,e){(function(a,n){t.exports=n()})(hi,function(){return function(r){var a={};function n(i){if(a[i])return a[i].exports;var s=a[i]={i,l:!1,exports:{}};return r[i].call(s.exports,s,s.exports,n),s.l=!0,s.exports}return n.m=r,n.c=a,n.i=function(i){return i},n.d=function(i,s,o){n.o(i,s)||Object.defineProperty(i,s,{configurable:!1,enumerable:!0,get:o})},n.n=function(i){var s=i&&i.__esModule?function(){return i.default}:function(){return i};return n.d(s,"a",s),s},n.o=function(i,s){return Object.prototype.hasOwnProperty.call(i,s)},n.p="",n(n.s=26)}([function(r,a,n){function i(){}i.QUALITY=1,i.DEFAULT_CREATE_BENDS_AS_NEEDED=!1,i.DEFAULT_INCREMENTAL=!1,i.DEFAULT_ANIMATION_ON_LAYOUT=!0,i.DEFAULT_ANIMATION_DURING_LAYOUT=!1,i.DEFAULT_ANIMATION_PERIOD=50,i.DEFAULT_UNIFORM_LEAF_NODE_SIZES=!1,i.DEFAULT_GRAPH_MARGIN=15,i.NODE_DIMENSIONS_INCLUDE_LABELS=!1,i.SIMPLE_NODE_SIZE=40,i.SIMPLE_NODE_HALF_SIZE=i.SIMPLE_NODE_SIZE/2,i.EMPTY_COMPOUND_NODE_SIZE=40,i.MIN_EDGE_LENGTH=1,i.WORLD_BOUNDARY=1e6,i.INITIAL_WORLD_BOUNDARY=i.WORLD_BOUNDARY/1e3,i.WORLD_CENTER_X=1200,i.WORLD_CENTER_Y=900,r.exports=i},function(r,a,n){var i=n(2),s=n(8),o=n(9);function u(f,h,d){i.call(this,d),this.isOverlapingSourceAndTarget=!1,this.vGraphObject=d,this.bendpoints=[],this.source=f,this.target=h}u.prototype=Object.create(i.prototype);for(var l in i)u[l]=i[l];u.prototype.getSource=function(){return this.source},u.prototype.getTarget=function(){return this.target},u.prototype.isInterGraph=function(){return this.isInterGraph},u.prototype.getLength=function(){return this.length},u.prototype.isOverlapingSourceAndTarget=function(){return this.isOverlapingSourceAndTarget},u.prototype.getBendpoints=function(){return this.bendpoints},u.prototype.getLca=function(){return this.lca},u.prototype.getSourceInLca=function(){return this.sourceInLca},u.prototype.getTargetInLca=function(){return this.targetInLca},u.prototype.getOtherEnd=function(f){if(this.source===f)return this.target;if(this.target===f)return this.source;throw"Node is not incident with this edge"},u.prototype.getOtherEndInGraph=function(f,h){for(var d=this.getOtherEnd(f),c=h.getGraphManager().getRoot();;){if(d.getOwner()==h)return d;if(d.getOwner()==c)break;d=d.getOwner().getParent()}return null},u.prototype.updateLength=function(){var f=new Array(4);this.isOverlapingSourceAndTarget=s.getIntersection(this.target.getRect(),this.source.getRect(),f),this.isOverlapingSourceAndTarget||(this.lengthX=f[0]-f[2],this.lengthY=f[1]-f[3],Math.abs(this.lengthX)<1&&(this.lengthX=o.sign(this.lengthX)),Math.abs(this.lengthY)<1&&(this.lengthY=o.sign(this.lengthY)),this.length=Math.sqrt(this.lengthX*this.lengthX+this.lengthY*this.lengthY))},u.prototype.updateLengthSimple=function(){this.lengthX=this.target.getCenterX()-this.source.getCenterX(),this.lengthY=this.target.getCenterY()-this.source.getCenterY(),Math.abs(this.lengthX)<1&&(this.lengthX=o.sign(this.lengthX)),Math.abs(this.lengthY)<1&&(this.lengthY=o.sign(this.lengthY)),this.length=Math.sqrt(this.lengthX*this.lengthX+this.lengthY*this.lengthY)},r.exports=u},function(r,a,n){function i(s){this.vGraphObject=s}r.exports=i},function(r,a,n){var i=n(2),s=n(10),o=n(13),u=n(0),l=n(16),f=n(4);function h(c,v,p,g){p==null&&g==null&&(g=v),i.call(this,g),c.graphManager!=null&&(c=c.graphManager),this.estimatedSize=s.MIN_VALUE,this.inclusionTreeDepth=s.MAX_VALUE,this.vGraphObject=g,this.edges=[],this.graphManager=c,p!=null&&v!=null?this.rect=new o(v.x,v.y,p.width,p.height):this.rect=new o}h.prototype=Object.create(i.prototype);for(var d in i)h[d]=i[d];h.prototype.getEdges=function(){return this.edges},h.prototype.getChild=function(){return this.child},h.prototype.getOwner=function(){return this.owner},h.prototype.getWidth=function(){return this.rect.width},h.prototype.setWidth=function(c){this.rect.width=c},h.prototype.getHeight=function(){return this.rect.height},h.prototype.setHeight=function(c){this.rect.height=c},h.prototype.getCenterX=function(){return this.rect.x+this.rect.width/2},h.prototype.getCenterY=function(){return this.rect.y+this.rect.height/2},h.prototype.getCenter=function(){return new f(this.rect.x+this.rect.width/2,this.rect.y+this.rect.height/2)},h.prototype.getLocation=function(){return new f(this.rect.x,this.rect.y)},h.prototype.getRect=function(){return this.rect},h.prototype.getDiagonal=function(){return Math.sqrt(this.rect.width*this.rect.width+this.rect.height*this.rect.height)},h.prototype.getHalfTheDiagonal=function(){return Math.sqrt(this.rect.height*this.rect.height+this.rect.width*this.rect.width)/2},h.prototype.setRect=function(c,v){this.rect.x=c.x,this.rect.y=c.y,this.rect.width=v.width,this.rect.height=v.height},h.prototype.setCenter=function(c,v){this.rect.x=c-this.rect.width/2,this.rect.y=v-this.rect.height/2},h.prototype.setLocation=function(c,v){this.rect.x=c,this.rect.y=v},h.prototype.moveBy=function(c,v){this.rect.x+=c,this.rect.y+=v},h.prototype.getEdgeListToNode=function(c){var v=[],p=this;return p.edges.forEach(function(g){if(g.target==c){if(g.source!=p)throw"Incorrect edge source!";v.push(g)}}),v},h.prototype.getEdgesBetween=function(c){var v=[],p=this;return p.edges.forEach(function(g){if(!(g.source==p||g.target==p))throw"Incorrect edge source and/or target";(g.target==c||g.source==c)&&v.push(g)}),v},h.prototype.getNeighborsList=function(){var c=new Set,v=this;return v.edges.forEach(function(p){if(p.source==v)c.add(p.target);else{if(p.target!=v)throw"Incorrect incidency!";c.add(p.source)}}),c},h.prototype.withChildren=function(){var c=new Set,v,p;if(c.add(this),this.child!=null)for(var g=this.child.getNodes(),y=0;y<g.length;y++)v=g[y],p=v.withChildren(),p.forEach(function(b){c.add(b)});return c},h.prototype.getNoOfChildren=function(){var c=0,v;if(this.child==null)c=1;else for(var p=this.child.getNodes(),g=0;g<p.length;g++)v=p[g],c+=v.getNoOfChildren();return c==0&&(c=1),c},h.prototype.getEstimatedSize=function(){if(this.estimatedSize==s.MIN_VALUE)throw"assert failed";return this.estimatedSize},h.prototype.calcEstimatedSize=function(){return this.child==null?this.estimatedSize=(this.rect.width+this.rect.height)/2:(this.estimatedSize=this.child.calcEstimatedSize(),this.rect.width=this.estimatedSize,this.rect.height=this.estimatedSize,this.estimatedSize)},h.prototype.scatter=function(){var c,v,p=-u.INITIAL_WORLD_BOUNDARY,g=u.INITIAL_WORLD_BOUNDARY;c=u.WORLD_CENTER_X+l.nextDouble()*(g-p)+p;var y=-u.INITIAL_WORLD_BOUNDARY,b=u.INITIAL_WORLD_BOUNDARY;v=u.WORLD_CENTER_Y+l.nextDouble()*(b-y)+y,this.rect.x=c,this.rect.y=v},h.prototype.updateBounds=function(){if(this.getChild()==null)throw"assert failed";if(this.getChild().getNodes().length!=0){var c=this.getChild();if(c.updateBounds(!0),this.rect.x=c.getLeft(),this.rect.y=c.getTop(),this.setWidth(c.getRight()-c.getLeft()),this.setHeight(c.getBottom()-c.getTop()),u.NODE_DIMENSIONS_INCLUDE_LABELS){var v=c.getRight()-c.getLeft(),p=c.getBottom()-c.getTop();this.labelWidth>v&&(this.rect.x-=(this.labelWidth-v)/2,this.setWidth(this.labelWidth)),this.labelHeight>p&&(this.labelPos=="center"?this.rect.y-=(this.labelHeight-p)/2:this.labelPos=="top"&&(this.rect.y-=this.labelHeight-p),this.setHeight(this.labelHeight))}}},h.prototype.getInclusionTreeDepth=function(){if(this.inclusionTreeDepth==s.MAX_VALUE)throw"assert failed";return this.inclusionTreeDepth},h.prototype.transform=function(c){var v=this.rect.x;v>u.WORLD_BOUNDARY?v=u.WORLD_BOUNDARY:v<-u.WORLD_BOUNDARY&&(v=-u.WORLD_BOUNDARY);var p=this.rect.y;p>u.WORLD_BOUNDARY?p=u.WORLD_BOUNDARY:p<-u.WORLD_BOUNDARY&&(p=-u.WORLD_BOUNDARY);var g=new f(v,p),y=c.inverseTransformPoint(g);this.setLocation(y.x,y.y)},h.prototype.getLeft=function(){return this.rect.x},h.prototype.getRight=function(){return this.rect.x+this.rect.width},h.prototype.getTop=function(){return this.rect.y},h.prototype.getBottom=function(){return this.rect.y+this.rect.height},h.prototype.getParent=function(){return this.owner==null?null:this.owner.getParent()},r.exports=h},function(r,a,n){function i(s,o){s==null&&o==null?(this.x=0,this.y=0):(this.x=s,this.y=o)}i.prototype.getX=function(){return this.x},i.prototype.getY=function(){return this.y},i.prototype.setX=function(s){this.x=s},i.prototype.setY=function(s){this.y=s},i.prototype.getDifference=function(s){return new DimensionD(this.x-s.x,this.y-s.y)},i.prototype.getCopy=function(){return new i(this.x,this.y)},i.prototype.translate=function(s){return this.x+=s.width,this.y+=s.height,this},r.exports=i},function(r,a,n){var i=n(2),s=n(10),o=n(0),u=n(6),l=n(3),f=n(1),h=n(13),d=n(12),c=n(11);function v(g,y,b){i.call(this,b),this.estimatedSize=s.MIN_VALUE,this.margin=o.DEFAULT_GRAPH_MARGIN,this.edges=[],this.nodes=[],this.isConnected=!1,this.parent=g,y!=null&&y instanceof u?this.graphManager=y:y!=null&&y instanceof Layout&&(this.graphManager=y.graphManager)}v.prototype=Object.create(i.prototype);for(var p in i)v[p]=i[p];v.prototype.getNodes=function(){return this.nodes},v.prototype.getEdges=function(){return this.edges},v.prototype.getGraphManager=function(){return this.graphManager},v.prototype.getParent=function(){return this.parent},v.prototype.getLeft=function(){return this.left},v.prototype.getRight=function(){return this.right},v.prototype.getTop=function(){return this.top},v.prototype.getBottom=function(){return this.bottom},v.prototype.isConnected=function(){return this.isConnected},v.prototype.add=function(g,y,b){if(y==null&&b==null){var m=g;if(this.graphManager==null)throw"Graph has no graph mgr!";if(this.getNodes().indexOf(m)>-1)throw"Node already in graph!";return m.owner=this,this.getNodes().push(m),m}else{var T=g;if(!(this.getNodes().indexOf(y)>-1&&this.getNodes().indexOf(b)>-1))throw"Source or target not in graph!";if(!(y.owner==b.owner&&y.owner==this))throw"Both owners must be this graph!";return y.owner!=b.owner?null:(T.source=y,T.target=b,T.isInterGraph=!1,this.getEdges().push(T),y.edges.push(T),b!=y&&b.edges.push(T),T)}},v.prototype.remove=function(g){var y=g;if(g instanceof l){if(y==null)throw"Node is null!";if(!(y.owner!=null&&y.owner==this))throw"Owner graph is invalid!";if(this.graphManager==null)throw"Owner graph manager is invalid!";for(var b=y.edges.slice(),m,T=b.length,C=0;C<T;C++)m=b[C],m.isInterGraph?this.graphManager.remove(m):m.source.owner.remove(m);var S=this.nodes.indexOf(y);if(S==-1)throw"Node not in owner node list!";this.nodes.splice(S,1)}else if(g instanceof f){var m=g;if(m==null)throw"Edge is null!";if(!(m.source!=null&&m.target!=null))throw"Source and/or target is null!";if(!(m.source.owner!=null&&m.target.owner!=null&&m.source.owner==this&&m.target.owner==this))throw"Source and/or target owner is invalid!";var E=m.source.edges.indexOf(m),w=m.target.edges.indexOf(m);if(!(E>-1&&w>-1))throw"Source and/or target doesn't know this edge!";m.source.edges.splice(E,1),m.target!=m.source&&m.target.edges.splice(w,1);var S=m.source.owner.getEdges().indexOf(m);if(S==-1)throw"Not in owner's edge list!";m.source.owner.getEdges().splice(S,1)}},v.prototype.updateLeftTop=function(){for(var g=s.MAX_VALUE,y=s.MAX_VALUE,b,m,T,C=this.getNodes(),S=C.length,E=0;E<S;E++){var w=C[E];b=w.getTop(),m=w.getLeft(),g>b&&(g=b),y>m&&(y=m)}return g==s.MAX_VALUE?null:(C[0].getParent().paddingLeft!=null?T=C[0].getParent().paddingLeft:T=this.margin,this.left=y-T,this.top=g-T,new d(this.left,this.top))},v.prototype.updateBounds=function(g){for(var y=s.MAX_VALUE,b=-s.MAX_VALUE,m=s.MAX_VALUE,T=-s.MAX_VALUE,C,S,E,w,x,D=this.nodes,L=D.length,A=0;A<L;A++){var N=D[A];g&&N.child!=null&&N.updateBounds(),C=N.getLeft(),S=N.getRight(),E=N.getTop(),w=N.getBottom(),y>C&&(y=C),b<S&&(b=S),m>E&&(m=E),T<w&&(T=w)}var O=new h(y,m,b-y,T-m);y==s.MAX_VALUE&&(this.left=this.parent.getLeft(),this.right=this.parent.getRight(),this.top=this.parent.getTop(),this.bottom=this.parent.getBottom()),D[0].getParent().paddingLeft!=null?x=D[0].getParent().paddingLeft:x=this.margin,this.left=O.x-x,this.right=O.x+O.width+x,this.top=O.y-x,this.bottom=O.y+O.height+x},v.calculateBounds=function(g){for(var y=s.MAX_VALUE,b=-s.MAX_VALUE,m=s.MAX_VALUE,T=-s.MAX_VALUE,C,S,E,w,x=g.length,D=0;D<x;D++){var L=g[D];C=L.getLeft(),S=L.getRight(),E=L.getTop(),w=L.getBottom(),y>C&&(y=C),b<S&&(b=S),m>E&&(m=E),T<w&&(T=w)}var A=new h(y,m,b-y,T-m);return A},v.prototype.getInclusionTreeDepth=function(){return this==this.graphManager.getRoot()?1:this.parent.getInclusionTreeDepth()},v.prototype.getEstimatedSize=function(){if(this.estimatedSize==s.MIN_VALUE)throw"assert failed";return this.estimatedSize},v.prototype.calcEstimatedSize=function(){for(var g=0,y=this.nodes,b=y.length,m=0;m<b;m++){var T=y[m];g+=T.calcEstimatedSize()}return g==0?this.estimatedSize=o.EMPTY_COMPOUND_NODE_SIZE:this.estimatedSize=g/Math.sqrt(this.nodes.length),this.estimatedSize},v.prototype.updateConnected=function(){var g=this;if(this.nodes.length==0){this.isConnected=!0;return}var y=new c,b=new Set,m=this.nodes[0],T,C,S=m.withChildren();for(S.forEach(function(A){y.push(A),b.add(A)});y.length!==0;){m=y.shift(),T=m.getEdges();for(var E=T.length,w=0;w<E;w++){var x=T[w];if(C=x.getOtherEndInGraph(m,this),C!=null&&!b.has(C)){var D=C.withChildren();D.forEach(function(A){y.push(A),b.add(A)})}}}if(this.isConnected=!1,b.size>=this.nodes.length){var L=0;b.forEach(function(A){A.owner==g&&L++}),L==this.nodes.length&&(this.isConnected=!0)}},r.exports=v},function(r,a,n){var i,s=n(1);function o(u){i=n(5),this.layout=u,this.graphs=[],this.edges=[]}o.prototype.addRoot=function(){var u=this.layout.newGraph(),l=this.layout.newNode(null),f=this.add(u,l);return this.setRootGraph(f),this.rootGraph},o.prototype.add=function(u,l,f,h,d){if(f==null&&h==null&&d==null){if(u==null)throw"Graph is null!";if(l==null)throw"Parent node is null!";if(this.graphs.indexOf(u)>-1)throw"Graph already in this graph mgr!";if(this.graphs.push(u),u.parent!=null)throw"Already has a parent!";if(l.child!=null)throw"Already has a child!";return u.parent=l,l.child=u,u}else{d=f,h=l,f=u;var c=h.getOwner(),v=d.getOwner();if(!(c!=null&&c.getGraphManager()==this))throw"Source not in this graph mgr!";if(!(v!=null&&v.getGraphManager()==this))throw"Target not in this graph mgr!";if(c==v)return f.isInterGraph=!1,c.add(f,h,d);if(f.isInterGraph=!0,f.source=h,f.target=d,this.edges.indexOf(f)>-1)throw"Edge already in inter-graph edge list!";if(this.edges.push(f),!(f.source!=null&&f.target!=null))throw"Edge source and/or target is null!";if(!(f.source.edges.indexOf(f)==-1&&f.target.edges.indexOf(f)==-1))throw"Edge already in source and/or target incidency list!";return f.source.edges.push(f),f.target.edges.push(f),f}},o.prototype.remove=function(u){if(u instanceof i){var l=u;if(l.getGraphManager()!=this)throw"Graph not in this graph mgr";if(!(l==this.rootGraph||l.parent!=null&&l.parent.graphManager==this))throw"Invalid parent node!";var f=[];f=f.concat(l.getEdges());for(var h,d=f.length,c=0;c<d;c++)h=f[c],l.remove(h);var v=[];v=v.concat(l.getNodes());var p;d=v.length;for(var c=0;c<d;c++)p=v[c],l.remove(p);l==this.rootGraph&&this.setRootGraph(null);var g=this.graphs.indexOf(l);this.graphs.splice(g,1),l.parent=null}else if(u instanceof s){if(h=u,h==null)throw"Edge is null!";if(!h.isInterGraph)throw"Not an inter-graph edge!";if(!(h.source!=null&&h.target!=null))throw"Source and/or target is null!";if(!(h.source.edges.indexOf(h)!=-1&&h.target.edges.indexOf(h)!=-1))throw"Source and/or target doesn't know this edge!";var g=h.source.edges.indexOf(h);if(h.source.edges.splice(g,1),g=h.target.edges.indexOf(h),h.target.edges.splice(g,1),!(h.source.owner!=null&&h.source.owner.getGraphManager()!=null))throw"Edge owner graph or owner graph manager is null!";if(h.source.owner.getGraphManager().edges.indexOf(h)==-1)throw"Not in owner graph manager's edge list!";var g=h.source.owner.getGraphManager().edges.indexOf(h);h.source.owner.getGraphManager().edges.splice(g,1)}},o.prototype.updateBounds=function(){this.rootGraph.updateBounds(!0)},o.prototype.getGraphs=function(){return this.graphs},o.prototype.getAllNodes=function(){if(this.allNodes==null){for(var u=[],l=this.getGraphs(),f=l.length,h=0;h<f;h++)u=u.concat(l[h].getNodes());this.allNodes=u}return this.allNodes},o.prototype.resetAllNodes=function(){this.allNodes=null},o.prototype.resetAllEdges=function(){this.allEdges=null},o.prototype.resetAllNodesToApplyGravitation=function(){this.allNodesToApplyGravitation=null},o.prototype.getAllEdges=function(){if(this.allEdges==null){var u=[],l=this.getGraphs();l.length;for(var f=0;f<l.length;f++)u=u.concat(l[f].getEdges());u=u.concat(this.edges),this.allEdges=u}return this.allEdges},o.prototype.getAllNodesToApplyGravitation=function(){return this.allNodesToApplyGravitation},o.prototype.setAllNodesToApplyGravitation=function(u){if(this.allNodesToApplyGravitation!=null)throw"assert failed";this.allNodesToApplyGravitation=u},o.prototype.getRoot=function(){return this.rootGraph},o.prototype.setRootGraph=function(u){if(u.getGraphManager()!=this)throw"Root not in this graph mgr!";this.rootGraph=u,u.parent==null&&(u.parent=this.layout.newNode("Root node"))},o.prototype.getLayout=function(){return this.layout},o.prototype.isOneAncestorOfOther=function(u,l){if(!(u!=null&&l!=null))throw"assert failed";if(u==l)return!0;var f=u.getOwner(),h;do{if(h=f.getParent(),h==null)break;if(h==l)return!0;if(f=h.getOwner(),f==null)break}while(!0);f=l.getOwner();do{if(h=f.getParent(),h==null)break;if(h==u)return!0;if(f=h.getOwner(),f==null)break}while(!0);return!1},o.prototype.calcLowestCommonAncestors=function(){for(var u,l,f,h,d,c=this.getAllEdges(),v=c.length,p=0;p<v;p++){if(u=c[p],l=u.source,f=u.target,u.lca=null,u.sourceInLca=l,u.targetInLca=f,l==f){u.lca=l.getOwner();continue}for(h=l.getOwner();u.lca==null;){for(u.targetInLca=f,d=f.getOwner();u.lca==null;){if(d==h){u.lca=d;break}if(d==this.rootGraph)break;if(u.lca!=null)throw"assert failed";u.targetInLca=d.getParent(),d=u.targetInLca.getOwner()}if(h==this.rootGraph)break;u.lca==null&&(u.sourceInLca=h.getParent(),h=u.sourceInLca.getOwner())}if(u.lca==null)throw"assert failed"}},o.prototype.calcLowestCommonAncestor=function(u,l){if(u==l)return u.getOwner();var f=u.getOwner();do{if(f==null)break;var h=l.getOwner();do{if(h==null)break;if(h==f)return h;h=h.getParent().getOwner()}while(!0);f=f.getParent().getOwner()}while(!0);return f},o.prototype.calcInclusionTreeDepths=function(u,l){u==null&&l==null&&(u=this.rootGraph,l=1);for(var f,h=u.getNodes(),d=h.length,c=0;c<d;c++)f=h[c],f.inclusionTreeDepth=l,f.child!=null&&this.calcInclusionTreeDepths(f.child,l+1)},o.prototype.includesInvalidEdge=function(){for(var u,l=this.edges.length,f=0;f<l;f++)if(u=this.edges[f],this.isOneAncestorOfOther(u.source,u.target))return!0;return!1},r.exports=o},function(r,a,n){var i=n(0);function s(){}for(var o in i)s[o]=i[o];s.MAX_ITERATIONS=2500,s.DEFAULT_EDGE_LENGTH=50,s.DEFAULT_SPRING_STRENGTH=.45,s.DEFAULT_REPULSION_STRENGTH=4500,s.DEFAULT_GRAVITY_STRENGTH=.4,s.DEFAULT_COMPOUND_GRAVITY_STRENGTH=1,s.DEFAULT_GRAVITY_RANGE_FACTOR=3.8,s.DEFAULT_COMPOUND_GRAVITY_RANGE_FACTOR=1.5,s.DEFAULT_USE_SMART_IDEAL_EDGE_LENGTH_CALCULATION=!0,s.DEFAULT_USE_SMART_REPULSION_RANGE_CALCULATION=!0,s.DEFAULT_COOLING_FACTOR_INCREMENTAL=.3,s.COOLING_ADAPTATION_FACTOR=.33,s.ADAPTATION_LOWER_NODE_LIMIT=1e3,s.ADAPTATION_UPPER_NODE_LIMIT=5e3,s.MAX_NODE_DISPLACEMENT_INCREMENTAL=100,s.MAX_NODE_DISPLACEMENT=s.MAX_NODE_DISPLACEMENT_INCREMENTAL*3,s.MIN_REPULSION_DIST=s.DEFAULT_EDGE_LENGTH/10,s.CONVERGENCE_CHECK_PERIOD=100,s.PER_LEVEL_IDEAL_EDGE_LENGTH_FACTOR=.1,s.MIN_EDGE_LENGTH=1,s.GRID_CALCULATION_CHECK_PERIOD=10,r.exports=s},function(r,a,n){var i=n(12);function s(){}s.calcSeparationAmount=function(o,u,l,f){if(!o.intersects(u))throw"assert failed";var h=new Array(2);this.decideDirectionsForOverlappingNodes(o,u,h),l[0]=Math.min(o.getRight(),u.getRight())-Math.max(o.x,u.x),l[1]=Math.min(o.getBottom(),u.getBottom())-Math.max(o.y,u.y),o.getX()<=u.getX()&&o.getRight()>=u.getRight()?l[0]+=Math.min(u.getX()-o.getX(),o.getRight()-u.getRight()):u.getX()<=o.getX()&&u.getRight()>=o.getRight()&&(l[0]+=Math.min(o.getX()-u.getX(),u.getRight()-o.getRight())),o.getY()<=u.getY()&&o.getBottom()>=u.getBottom()?l[1]+=Math.min(u.getY()-o.getY(),o.getBottom()-u.getBottom()):u.getY()<=o.getY()&&u.getBottom()>=o.getBottom()&&(l[1]+=Math.min(o.getY()-u.getY(),u.getBottom()-o.getBottom()));var d=Math.abs((u.getCenterY()-o.getCenterY())/(u.getCenterX()-o.getCenterX()));u.getCenterY()===o.getCenterY()&&u.getCenterX()===o.getCenterX()&&(d=1);var c=d*l[0],v=l[1]/d;l[0]<v?v=l[0]:c=l[1],l[0]=-1*h[0]*(v/2+f),l[1]=-1*h[1]*(c/2+f)},s.decideDirectionsForOverlappingNodes=function(o,u,l){o.getCenterX()<u.getCenterX()?l[0]=-1:l[0]=1,o.getCenterY()<u.getCenterY()?l[1]=-1:l[1]=1},s.getIntersection2=function(o,u,l){var f=o.getCenterX(),h=o.getCenterY(),d=u.getCenterX(),c=u.getCenterY();if(o.intersects(u))return l[0]=f,l[1]=h,l[2]=d,l[3]=c,!0;var v=o.getX(),p=o.getY(),g=o.getRight(),y=o.getX(),b=o.getBottom(),m=o.getRight(),T=o.getWidthHalf(),C=o.getHeightHalf(),S=u.getX(),E=u.getY(),w=u.getRight(),x=u.getX(),D=u.getBottom(),L=u.getRight(),A=u.getWidthHalf(),N=u.getHeightHalf(),O=!1,M=!1;if(f===d){if(h>c)return l[0]=f,l[1]=p,l[2]=d,l[3]=D,!1;if(h<c)return l[0]=f,l[1]=b,l[2]=d,l[3]=E,!1}else if(h===c){if(f>d)return l[0]=v,l[1]=h,l[2]=w,l[3]=c,!1;if(f<d)return l[0]=g,l[1]=h,l[2]=S,l[3]=c,!1}else{var R=o.height/o.width,k=u.height/u.width,P=(c-h)/(d-f),B=void 0,z=void 0,G=void 0,F=void 0,U=void 0,Y=void 0;if(-R===P?f>d?(l[0]=y,l[1]=b,O=!0):(l[0]=g,l[1]=p,O=!0):R===P&&(f>d?(l[0]=v,l[1]=p,O=!0):(l[0]=m,l[1]=b,O=!0)),-k===P?d>f?(l[2]=x,l[3]=D,M=!0):(l[2]=w,l[3]=E,M=!0):k===P&&(d>f?(l[2]=S,l[3]=E,M=!0):(l[2]=L,l[3]=D,M=!0)),O&&M)return!1;if(f>d?h>c?(B=this.getCardinalDirection(R,P,4),z=this.getCardinalDirection(k,P,2)):(B=this.getCardinalDirection(-R,P,3),z=this.getCardinalDirection(-k,P,1)):h>c?(B=this.getCardinalDirection(-R,P,1),z=this.getCardinalDirection(-k,P,3)):(B=this.getCardinalDirection(R,P,2),z=this.getCardinalDirection(k,P,4)),!O)switch(B){case 1:F=p,G=f+-C/P,l[0]=G,l[1]=F;break;case 2:G=m,F=h+T*P,l[0]=G,l[1]=F;break;case 3:F=b,G=f+C/P,l[0]=G,l[1]=F;break;case 4:G=y,F=h+-T*P,l[0]=G,l[1]=F;break}if(!M)switch(z){case 1:Y=E,U=d+-N/P,l[2]=U,l[3]=Y;break;case 2:U=L,Y=c+A*P,l[2]=U,l[3]=Y;break;case 3:Y=D,U=d+N/P,l[2]=U,l[3]=Y;break;case 4:U=x,Y=c+-A*P,l[2]=U,l[3]=Y;break}}return!1},s.getCardinalDirection=function(o,u,l){return o>u?l:1+l%4},s.getIntersection=function(o,u,l,f){if(f==null)return this.getIntersection2(o,u,l);var h=o.x,d=o.y,c=u.x,v=u.y,p=l.x,g=l.y,y=f.x,b=f.y,m=void 0,T=void 0,C=void 0,S=void 0,E=void 0,w=void 0,x=void 0,D=void 0,L=void 0;return C=v-d,E=h-c,x=c*d-h*v,S=b-g,w=p-y,D=y*g-p*b,L=C*w-S*E,L===0?null:(m=(E*D-w*x)/L,T=(S*x-C*D)/L,new i(m,T))},s.angleOfVector=function(o,u,l,f){var h=void 0;return o!==l?(h=Math.atan((f-u)/(l-o)),l<o?h+=Math.PI:f<u&&(h+=this.TWO_PI)):f<u?h=this.ONE_AND_HALF_PI:h=this.HALF_PI,h},s.doIntersect=function(o,u,l,f){var h=o.x,d=o.y,c=u.x,v=u.y,p=l.x,g=l.y,y=f.x,b=f.y,m=(c-h)*(b-g)-(y-p)*(v-d);if(m===0)return!1;var T=((b-g)*(y-h)+(p-y)*(b-d))/m,C=((d-v)*(y-h)+(c-h)*(b-d))/m;return 0<T&&T<1&&0<C&&C<1},s.HALF_PI=.5*Math.PI,s.ONE_AND_HALF_PI=1.5*Math.PI,s.TWO_PI=2*Math.PI,s.THREE_PI=3*Math.PI,r.exports=s},function(r,a,n){function i(){}i.sign=function(s){return s>0?1:s<0?-1:0},i.floor=function(s){return s<0?Math.ceil(s):Math.floor(s)},i.ceil=function(s){return s<0?Math.floor(s):Math.ceil(s)},r.exports=i},function(r,a,n){function i(){}i.MAX_VALUE=2147483647,i.MIN_VALUE=-2147483648,r.exports=i},function(r,a,n){var i=function(){function h(d,c){for(var v=0;v<c.length;v++){var p=c[v];p.enumerable=p.enumerable||!1,p.configurable=!0,"value"in p&&(p.writable=!0),Object.defineProperty(d,p.key,p)}}return function(d,c,v){return c&&h(d.prototype,c),v&&h(d,v),d}}();function s(h,d){if(!(h instanceof d))throw new TypeError("Cannot call a class as a function")}var o=function(d){return{value:d,next:null,prev:null}},u=function(d,c,v,p){return d!==null?d.next=c:p.head=c,v!==null?v.prev=c:p.tail=c,c.prev=d,c.next=v,p.length++,c},l=function(d,c){var v=d.prev,p=d.next;return v!==null?v.next=p:c.head=p,p!==null?p.prev=v:c.tail=v,d.prev=d.next=null,c.length--,d},f=function(){function h(d){var c=this;s(this,h),this.length=0,this.head=null,this.tail=null,d!=null&&d.forEach(function(v){return c.push(v)})}return i(h,[{key:"size",value:function(){return this.length}},{key:"insertBefore",value:function(c,v){return u(v.prev,o(c),v,this)}},{key:"insertAfter",value:function(c,v){return u(v,o(c),v.next,this)}},{key:"insertNodeBefore",value:function(c,v){return u(v.prev,c,v,this)}},{key:"insertNodeAfter",value:function(c,v){return u(v,c,v.next,this)}},{key:"push",value:function(c){return u(this.tail,o(c),null,this)}},{key:"unshift",value:function(c){return u(null,o(c),this.head,this)}},{key:"remove",value:function(c){return l(c,this)}},{key:"pop",value:function(){return l(this.tail,this).value}},{key:"popNode",value:function(){return l(this.tail,this)}},{key:"shift",value:function(){return l(this.head,this).value}},{key:"shiftNode",value:function(){return l(this.head,this)}},{key:"get_object_at",value:function(c){if(c<=this.length()){for(var v=1,p=this.head;v<c;)p=p.next,v++;return p.value}}},{key:"set_object_at",value:function(c,v){if(c<=this.length()){for(var p=1,g=this.head;p<c;)g=g.next,p++;g.value=v}}}]),h}();r.exports=f},function(r,a,n){function i(s,o,u){this.x=null,this.y=null,s==null&&o==null&&u==null?(this.x=0,this.y=0):typeof s=="number"&&typeof o=="number"&&u==null?(this.x=s,this.y=o):s.constructor.name=="Point"&&o==null&&u==null&&(u=s,this.x=u.x,this.y=u.y)}i.prototype.getX=function(){return this.x},i.prototype.getY=function(){return this.y},i.prototype.getLocation=function(){return new i(this.x,this.y)},i.prototype.setLocation=function(s,o,u){s.constructor.name=="Point"&&o==null&&u==null?(u=s,this.setLocation(u.x,u.y)):typeof s=="number"&&typeof o=="number"&&u==null&&(parseInt(s)==s&&parseInt(o)==o?this.move(s,o):(this.x=Math.floor(s+.5),this.y=Math.floor(o+.5)))},i.prototype.move=function(s,o){this.x=s,this.y=o},i.prototype.translate=function(s,o){this.x+=s,this.y+=o},i.prototype.equals=function(s){if(s.constructor.name=="Point"){var o=s;return this.x==o.x&&this.y==o.y}return this==s},i.prototype.toString=function(){return new i().constructor.name+"[x="+this.x+",y="+this.y+"]"},r.exports=i},function(r,a,n){function i(s,o,u,l){this.x=0,this.y=0,this.width=0,this.height=0,s!=null&&o!=null&&u!=null&&l!=null&&(this.x=s,this.y=o,this.width=u,this.height=l)}i.prototype.getX=function(){return this.x},i.prototype.setX=function(s){this.x=s},i.prototype.getY=function(){return this.y},i.prototype.setY=function(s){this.y=s},i.prototype.getWidth=function(){return this.width},i.prototype.setWidth=function(s){this.width=s},i.prototype.getHeight=function(){return this.height},i.prototype.setHeight=function(s){this.height=s},i.prototype.getRight=function(){return this.x+this.width},i.prototype.getBottom=function(){return this.y+this.height},i.prototype.intersects=function(s){return!(this.getRight()<s.x||this.getBottom()<s.y||s.getRight()<this.x||s.getBottom()<this.y)},i.prototype.getCenterX=function(){return this.x+this.width/2},i.prototype.getMinX=function(){return this.getX()},i.prototype.getMaxX=function(){return this.getX()+this.width},i.prototype.getCenterY=function(){return this.y+this.height/2},i.prototype.getMinY=function(){return this.getY()},i.prototype.getMaxY=function(){return this.getY()+this.height},i.prototype.getWidthHalf=function(){return this.width/2},i.prototype.getHeightHalf=function(){return this.height/2},r.exports=i},function(r,a,n){var i=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(o){return typeof o}:function(o){return o&&typeof Symbol=="function"&&o.constructor===Symbol&&o!==Symbol.prototype?"symbol":typeof o};function s(){}s.lastID=0,s.createID=function(o){return s.isPrimitive(o)?o:(o.uniqueID!=null||(o.uniqueID=s.getString(),s.lastID++),o.uniqueID)},s.getString=function(o){return o==null&&(o=s.lastID),"Object#"+o},s.isPrimitive=function(o){var u=typeof o>"u"?"undefined":i(o);return o==null||u!="object"&&u!="function"},r.exports=s},function(r,a,n){function i(p){if(Array.isArray(p)){for(var g=0,y=Array(p.length);g<p.length;g++)y[g]=p[g];return y}else return Array.from(p)}var s=n(0),o=n(6),u=n(3),l=n(1),f=n(5),h=n(4),d=n(17),c=n(27);function v(p){c.call(this),this.layoutQuality=s.QUALITY,this.createBendsAsNeeded=s.DEFAULT_CREATE_BENDS_AS_NEEDED,this.incremental=s.DEFAULT_INCREMENTAL,this.animationOnLayout=s.DEFAULT_ANIMATION_ON_LAYOUT,this.animationDuringLayout=s.DEFAULT_ANIMATION_DURING_LAYOUT,this.animationPeriod=s.DEFAULT_ANIMATION_PERIOD,this.uniformLeafNodeSizes=s.DEFAULT_UNIFORM_LEAF_NODE_SIZES,this.edgeToDummyNodes=new Map,this.graphManager=new o(this),this.isLayoutFinished=!1,this.isSubLayout=!1,this.isRemoteUse=!1,p!=null&&(this.isRemoteUse=p)}v.RANDOM_SEED=1,v.prototype=Object.create(c.prototype),v.prototype.getGraphManager=function(){return this.graphManager},v.prototype.getAllNodes=function(){return this.graphManager.getAllNodes()},v.prototype.getAllEdges=function(){return this.graphManager.getAllEdges()},v.prototype.getAllNodesToApplyGravitation=function(){return this.graphManager.getAllNodesToApplyGravitation()},v.prototype.newGraphManager=function(){var p=new o(this);return this.graphManager=p,p},v.prototype.newGraph=function(p){return new f(null,this.graphManager,p)},v.prototype.newNode=function(p){return new u(this.graphManager,p)},v.prototype.newEdge=function(p){return new l(null,null,p)},v.prototype.checkLayoutSuccess=function(){return this.graphManager.getRoot()==null||this.graphManager.getRoot().getNodes().length==0||this.graphManager.includesInvalidEdge()},v.prototype.runLayout=function(){this.isLayoutFinished=!1,this.tilingPreLayout&&this.tilingPreLayout(),this.initParameters();var p;return this.checkLayoutSuccess()?p=!1:p=this.layout(),s.ANIMATE==="during"?!1:(p&&(this.isSubLayout||this.doPostLayout()),this.tilingPostLayout&&this.tilingPostLayout(),this.isLayoutFinished=!0,p)},v.prototype.doPostLayout=function(){this.incremental||this.transform(),this.update()},v.prototype.update2=function(){if(this.createBendsAsNeeded&&(this.createBendpointsFromDummyNodes(),this.graphManager.resetAllEdges()),!this.isRemoteUse){for(var p=this.graphManager.getAllEdges(),g=0;g<p.length;g++)p[g];for(var y=this.graphManager.getRoot().getNodes(),g=0;g<y.length;g++)y[g];this.update(this.graphManager.getRoot())}},v.prototype.update=function(p){if(p==null)this.update2();else if(p instanceof u){var g=p;if(g.getChild()!=null)for(var y=g.getChild().getNodes(),b=0;b<y.length;b++)update(y[b]);if(g.vGraphObject!=null){var m=g.vGraphObject;m.update(g)}}else if(p instanceof l){var T=p;if(T.vGraphObject!=null){var C=T.vGraphObject;C.update(T)}}else if(p instanceof f){var S=p;if(S.vGraphObject!=null){var E=S.vGraphObject;E.update(S)}}},v.prototype.initParameters=function(){this.isSubLayout||(this.layoutQuality=s.QUALITY,this.animationDuringLayout=s.DEFAULT_ANIMATION_DURING_LAYOUT,this.animationPeriod=s.DEFAULT_ANIMATION_PERIOD,this.animationOnLayout=s.DEFAULT_ANIMATION_ON_LAYOUT,this.incremental=s.DEFAULT_INCREMENTAL,this.createBendsAsNeeded=s.DEFAULT_CREATE_BENDS_AS_NEEDED,this.uniformLeafNodeSizes=s.DEFAULT_UNIFORM_LEAF_NODE_SIZES),this.animationDuringLayout&&(this.animationOnLayout=!1)},v.prototype.transform=function(p){if(p==null)this.transform(new h(0,0));else{var g=new d,y=this.graphManager.getRoot().updateLeftTop();if(y!=null){g.setWorldOrgX(p.x),g.setWorldOrgY(p.y),g.setDeviceOrgX(y.x),g.setDeviceOrgY(y.y);for(var b=this.getAllNodes(),m,T=0;T<b.length;T++)m=b[T],m.transform(g)}}},v.prototype.positionNodesRandomly=function(p){if(p==null)this.positionNodesRandomly(this.getGraphManager().getRoot()),this.getGraphManager().getRoot().updateBounds(!0);else for(var g,y,b=p.getNodes(),m=0;m<b.length;m++)g=b[m],y=g.getChild(),y==null||y.getNodes().length==0?g.scatter():(this.positionNodesRandomly(y),g.updateBounds())},v.prototype.getFlatForest=function(){for(var p=[],g=!0,y=this.graphManager.getRoot().getNodes(),b=!0,m=0;m<y.length;m++)y[m].getChild()!=null&&(b=!1);if(!b)return p;var T=new Set,C=[],S=new Map,E=[];for(E=E.concat(y);E.length>0&&g;){for(C.push(E[0]);C.length>0&&g;){var w=C[0];C.splice(0,1),T.add(w);for(var x=w.getEdges(),m=0;m<x.length;m++){var D=x[m].getOtherEnd(w);if(S.get(w)!=D)if(!T.has(D))C.push(D),S.set(D,w);else{g=!1;break}}}if(!g)p=[];else{var L=[].concat(i(T));p.push(L);for(var m=0;m<L.length;m++){var A=L[m],N=E.indexOf(A);N>-1&&E.splice(N,1)}T=new Set,S=new Map}}return p},v.prototype.createDummyNodesForBendpoints=function(p){for(var g=[],y=p.source,b=this.graphManager.calcLowestCommonAncestor(p.source,p.target),m=0;m<p.bendpoints.length;m++){var T=this.newNode(null);T.setRect(new Point(0,0),new Dimension(1,1)),b.add(T);var C=this.newEdge(null);this.graphManager.add(C,y,T),g.add(T),y=T}var C=this.newEdge(null);return this.graphManager.add(C,y,p.target),this.edgeToDummyNodes.set(p,g),p.isInterGraph()?this.graphManager.remove(p):b.remove(p),g},v.prototype.createBendpointsFromDummyNodes=function(){var p=[];p=p.concat(this.graphManager.getAllEdges()),p=[].concat(i(this.edgeToDummyNodes.keys())).concat(p);for(var g=0;g<p.length;g++){var y=p[g];if(y.bendpoints.length>0){for(var b=this.edgeToDummyNodes.get(y),m=0;m<b.length;m++){var T=b[m],C=new h(T.getCenterX(),T.getCenterY()),S=y.bendpoints.get(m);S.x=C.x,S.y=C.y,T.getOwner().remove(T)}this.graphManager.add(y,y.source,y.target)}}},v.transform=function(p,g,y,b){if(y!=null&&b!=null){var m=g;if(p<=50){var T=g/y;m-=(g-T)/50*(50-p)}else{var C=g*b;m+=(C-g)/50*(p-50)}return m}else{var S,E;return p<=50?(S=9*g/500,E=g/10):(S=9*g/50,E=-8*g),S*p+E}},v.findCenterOfTree=function(p){var g=[];g=g.concat(p);var y=[],b=new Map,m=!1,T=null;(g.length==1||g.length==2)&&(m=!0,T=g[0]);for(var C=0;C<g.length;C++){var S=g[C],E=S.getNeighborsList().size;b.set(S,S.getNeighborsList().size),E==1&&y.push(S)}var w=[];for(w=w.concat(y);!m;){var x=[];x=x.concat(w),w=[];for(var C=0;C<g.length;C++){var S=g[C],D=g.indexOf(S);D>=0&&g.splice(D,1);var L=S.getNeighborsList();L.forEach(function(O){if(y.indexOf(O)<0){var M=b.get(O),R=M-1;R==1&&w.push(O),b.set(O,R)}})}y=y.concat(w),(g.length==1||g.length==2)&&(m=!0,T=g[0])}return T},v.prototype.setGraphManager=function(p){this.graphManager=p},r.exports=v},function(r,a,n){function i(){}i.seed=1,i.x=0,i.nextDouble=function(){return i.x=Math.sin(i.seed++)*1e4,i.x-Math.floor(i.x)},r.exports=i},function(r,a,n){var i=n(4);function s(o,u){this.lworldOrgX=0,this.lworldOrgY=0,this.ldeviceOrgX=0,this.ldeviceOrgY=0,this.lworldExtX=1,this.lworldExtY=1,this.ldeviceExtX=1,this.ldeviceExtY=1}s.prototype.getWorldOrgX=function(){return this.lworldOrgX},s.prototype.setWorldOrgX=function(o){this.lworldOrgX=o},s.prototype.getWorldOrgY=function(){return this.lworldOrgY},s.prototype.setWorldOrgY=function(o){this.lworldOrgY=o},s.prototype.getWorldExtX=function(){return this.lworldExtX},s.prototype.setWorldExtX=function(o){this.lworldExtX=o},s.prototype.getWorldExtY=function(){return this.lworldExtY},s.prototype.setWorldExtY=function(o){this.lworldExtY=o},s.prototype.getDeviceOrgX=function(){return this.ldeviceOrgX},s.prototype.setDeviceOrgX=function(o){this.ldeviceOrgX=o},s.prototype.getDeviceOrgY=function(){return this.ldeviceOrgY},s.prototype.setDeviceOrgY=function(o){this.ldeviceOrgY=o},s.prototype.getDeviceExtX=function(){return this.ldeviceExtX},s.prototype.setDeviceExtX=function(o){this.ldeviceExtX=o},s.prototype.getDeviceExtY=function(){return this.ldeviceExtY},s.prototype.setDeviceExtY=function(o){this.ldeviceExtY=o},s.prototype.transformX=function(o){var u=0,l=this.lworldExtX;return l!=0&&(u=this.ldeviceOrgX+(o-this.lworldOrgX)*this.ldeviceExtX/l),u},s.prototype.transformY=function(o){var u=0,l=this.lworldExtY;return l!=0&&(u=this.ldeviceOrgY+(o-this.lworldOrgY)*this.ldeviceExtY/l),u},s.prototype.inverseTransformX=function(o){var u=0,l=this.ldeviceExtX;return l!=0&&(u=this.lworldOrgX+(o-this.ldeviceOrgX)*this.lworldExtX/l),u},s.prototype.inverseTransformY=function(o){var u=0,l=this.ldeviceExtY;return l!=0&&(u=this.lworldOrgY+(o-this.ldeviceOrgY)*this.lworldExtY/l),u},s.prototype.inverseTransformPoint=function(o){var u=new i(this.inverseTransformX(o.x),this.inverseTransformY(o.y));return u},r.exports=s},function(r,a,n){function i(c){if(Array.isArray(c)){for(var v=0,p=Array(c.length);v<c.length;v++)p[v]=c[v];return p}else return Array.from(c)}var s=n(15),o=n(7),u=n(0),l=n(8),f=n(9);function h(){s.call(this),this.useSmartIdealEdgeLengthCalculation=o.DEFAULT_USE_SMART_IDEAL_EDGE_LENGTH_CALCULATION,this.idealEdgeLength=o.DEFAULT_EDGE_LENGTH,this.springConstant=o.DEFAULT_SPRING_STRENGTH,this.repulsionConstant=o.DEFAULT_REPULSION_STRENGTH,this.gravityConstant=o.DEFAULT_GRAVITY_STRENGTH,this.compoundGravityConstant=o.DEFAULT_COMPOUND_GRAVITY_STRENGTH,this.gravityRangeFactor=o.DEFAULT_GRAVITY_RANGE_FACTOR,this.compoundGravityRangeFactor=o.DEFAULT_COMPOUND_GRAVITY_RANGE_FACTOR,this.displacementThresholdPerNode=3*o.DEFAULT_EDGE_LENGTH/100,this.coolingFactor=o.DEFAULT_COOLING_FACTOR_INCREMENTAL,this.initialCoolingFactor=o.DEFAULT_COOLING_FACTOR_INCREMENTAL,this.totalDisplacement=0,this.oldTotalDisplacement=0,this.maxIterations=o.MAX_ITERATIONS}h.prototype=Object.create(s.prototype);for(var d in s)h[d]=s[d];h.prototype.initParameters=function(){s.prototype.initParameters.call(this,arguments),this.totalIterations=0,this.notAnimatedIterations=0,this.useFRGridVariant=o.DEFAULT_USE_SMART_REPULSION_RANGE_CALCULATION,this.grid=[]},h.prototype.calcIdealEdgeLengths=function(){for(var c,v,p,g,y,b,m=this.getGraphManager().getAllEdges(),T=0;T<m.length;T++)c=m[T],c.idealLength=this.idealEdgeLength,c.isInterGraph&&(p=c.getSource(),g=c.getTarget(),y=c.getSourceInLca().getEstimatedSize(),b=c.getTargetInLca().getEstimatedSize(),this.useSmartIdealEdgeLengthCalculation&&(c.idealLength+=y+b-2*u.SIMPLE_NODE_SIZE),v=c.getLca().getInclusionTreeDepth(),c.idealLength+=o.DEFAULT_EDGE_LENGTH*o.PER_LEVEL_IDEAL_EDGE_LENGTH_FACTOR*(p.getInclusionTreeDepth()+g.getInclusionTreeDepth()-2*v))},h.prototype.initSpringEmbedder=function(){var c=this.getAllNodes().length;this.incremental?(c>o.ADAPTATION_LOWER_NODE_LIMIT&&(this.coolingFactor=Math.max(this.coolingFactor*o.COOLING_ADAPTATION_FACTOR,this.coolingFactor-(c-o.ADAPTATION_LOWER_NODE_LIMIT)/(o.ADAPTATION_UPPER_NODE_LIMIT-o.ADAPTATION_LOWER_NODE_LIMIT)*this.coolingFactor*(1-o.COOLING_ADAPTATION_FACTOR))),this.maxNodeDisplacement=o.MAX_NODE_DISPLACEMENT_INCREMENTAL):(c>o.ADAPTATION_LOWER_NODE_LIMIT?this.coolingFactor=Math.max(o.COOLING_ADAPTATION_FACTOR,1-(c-o.ADAPTATION_LOWER_NODE_LIMIT)/(o.ADAPTATION_UPPER_NODE_LIMIT-o.ADAPTATION_LOWER_NODE_LIMIT)*(1-o.COOLING_ADAPTATION_FACTOR)):this.coolingFactor=1,this.initialCoolingFactor=this.coolingFactor,this.maxNodeDisplacement=o.MAX_NODE_DISPLACEMENT),this.maxIterations=Math.max(this.getAllNodes().length*5,this.maxIterations),this.totalDisplacementThreshold=this.displacementThresholdPerNode*this.getAllNodes().length,this.repulsionRange=this.calcRepulsionRange()},h.prototype.calcSpringForces=function(){for(var c=this.getAllEdges(),v,p=0;p<c.length;p++)v=c[p],this.calcSpringForce(v,v.idealLength)},h.prototype.calcRepulsionForces=function(){var c=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!0,v=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1,p,g,y,b,m=this.getAllNodes(),T;if(this.useFRGridVariant)for(this.totalIterations%o.GRID_CALCULATION_CHECK_PERIOD==1&&c&&this.updateGrid(),T=new Set,p=0;p<m.length;p++)y=m[p],this.calculateRepulsionForceOfANode(y,T,c,v),T.add(y);else for(p=0;p<m.length;p++)for(y=m[p],g=p+1;g<m.length;g++)b=m[g],y.getOwner()==b.getOwner()&&this.calcRepulsionForce(y,b)},h.prototype.calcGravitationalForces=function(){for(var c,v=this.getAllNodesToApplyGravitation(),p=0;p<v.length;p++)c=v[p],this.calcGravitationalForce(c)},h.prototype.moveNodes=function(){for(var c=this.getAllNodes(),v,p=0;p<c.length;p++)v=c[p],v.move()},h.prototype.calcSpringForce=function(c,v){var p=c.getSource(),g=c.getTarget(),y,b,m,T;if(this.uniformLeafNodeSizes&&p.getChild()==null&&g.getChild()==null)c.updateLengthSimple();else if(c.updateLength(),c.isOverlapingSourceAndTarget)return;y=c.getLength(),y!=0&&(b=this.springConstant*(y-v),m=b*(c.lengthX/y),T=b*(c.lengthY/y),p.springForceX+=m,p.springForceY+=T,g.springForceX-=m,g.springForceY-=T)},h.prototype.calcRepulsionForce=function(c,v){var p=c.getRect(),g=v.getRect(),y=new Array(2),b=new Array(4),m,T,C,S,E,w,x;if(p.intersects(g)){l.calcSeparationAmount(p,g,y,o.DEFAULT_EDGE_LENGTH/2),w=2*y[0],x=2*y[1];var D=c.noOfChildren*v.noOfChildren/(c.noOfChildren+v.noOfChildren);c.repulsionForceX-=D*w,c.repulsionForceY-=D*x,v.repulsionForceX+=D*w,v.repulsionForceY+=D*x}else this.uniformLeafNodeSizes&&c.getChild()==null&&v.getChild()==null?(m=g.getCenterX()-p.getCenterX(),T=g.getCenterY()-p.getCenterY()):(l.getIntersection(p,g,b),m=b[2]-b[0],T=b[3]-b[1]),Math.abs(m)<o.MIN_REPULSION_DIST&&(m=f.sign(m)*o.MIN_REPULSION_DIST),Math.abs(T)<o.MIN_REPULSION_DIST&&(T=f.sign(T)*o.MIN_REPULSION_DIST),C=m*m+T*T,S=Math.sqrt(C),E=this.repulsionConstant*c.noOfChildren*v.noOfChildren/C,w=E*m/S,x=E*T/S,c.repulsionForceX-=w,c.repulsionForceY-=x,v.repulsionForceX+=w,v.repulsionForceY+=x},h.prototype.calcGravitationalForce=function(c){var v,p,g,y,b,m,T,C;v=c.getOwner(),p=(v.getRight()+v.getLeft())/2,g=(v.getTop()+v.getBottom())/2,y=c.getCenterX()-p,b=c.getCenterY()-g,m=Math.abs(y)+c.getWidth()/2,T=Math.abs(b)+c.getHeight()/2,c.getOwner()==this.graphManager.getRoot()?(C=v.getEstimatedSize()*this.gravityRangeFactor,(m>C||T>C)&&(c.gravitationForceX=-this.gravityConstant*y,c.gravitationForceY=-this.gravityConstant*b)):(C=v.getEstimatedSize()*this.compoundGravityRangeFactor,(m>C||T>C)&&(c.gravitationForceX=-this.gravityConstant*y*this.compoundGravityConstant,c.gravitationForceY=-this.gravityConstant*b*this.compoundGravityConstant))},h.prototype.isConverged=function(){var c,v=!1;return this.totalIterations>this.maxIterations/3&&(v=Math.abs(this.totalDisplacement-this.oldTotalDisplacement)<2),c=this.totalDisplacement<this.totalDisplacementThreshold,this.oldTotalDisplacement=this.totalDisplacement,c||v},h.prototype.animate=function(){this.animationDuringLayout&&!this.isSubLayout&&(this.notAnimatedIterations==this.animationPeriod?(this.update(),this.notAnimatedIterations=0):this.notAnimatedIterations++)},h.prototype.calcNoOfChildrenForAllNodes=function(){for(var c,v=this.graphManager.getAllNodes(),p=0;p<v.length;p++)c=v[p],c.noOfChildren=c.getNoOfChildren()},h.prototype.calcGrid=function(c){var v=0,p=0;v=parseInt(Math.ceil((c.getRight()-c.getLeft())/this.repulsionRange)),p=parseInt(Math.ceil((c.getBottom()-c.getTop())/this.repulsionRange));for(var g=new Array(v),y=0;y<v;y++)g[y]=new Array(p);for(var y=0;y<v;y++)for(var b=0;b<p;b++)g[y][b]=new Array;return g},h.prototype.addNodeToGrid=function(c,v,p){var g=0,y=0,b=0,m=0;g=parseInt(Math.floor((c.getRect().x-v)/this.repulsionRange)),y=parseInt(Math.floor((c.getRect().width+c.getRect().x-v)/this.repulsionRange)),b=parseInt(Math.floor((c.getRect().y-p)/this.repulsionRange)),m=parseInt(Math.floor((c.getRect().height+c.getRect().y-p)/this.repulsionRange));for(var T=g;T<=y;T++)for(var C=b;C<=m;C++)this.grid[T][C].push(c),c.setGridCoordinates(g,y,b,m)},h.prototype.updateGrid=function(){var c,v,p=this.getAllNodes();for(this.grid=this.calcGrid(this.graphManager.getRoot()),c=0;c<p.length;c++)v=p[c],this.addNodeToGrid(v,this.graphManager.getRoot().getLeft(),this.graphManager.getRoot().getTop())},h.prototype.calculateRepulsionForceOfANode=function(c,v,p,g){if(this.totalIterations%o.GRID_CALCULATION_CHECK_PERIOD==1&&p||g){var y=new Set;c.surrounding=new Array;for(var b,m=this.grid,T=c.startX-1;T<c.finishX+2;T++)for(var C=c.startY-1;C<c.finishY+2;C++)if(!(T<0||C<0||T>=m.length||C>=m[0].length)){for(var S=0;S<m[T][C].length;S++)if(b=m[T][C][S],!(c.getOwner()!=b.getOwner()||c==b)&&!v.has(b)&&!y.has(b)){var E=Math.abs(c.getCenterX()-b.getCenterX())-(c.getWidth()/2+b.getWidth()/2),w=Math.abs(c.getCenterY()-b.getCenterY())-(c.getHeight()/2+b.getHeight()/2);E<=this.repulsionRange&&w<=this.repulsionRange&&y.add(b)}}c.surrounding=[].concat(i(y))}for(T=0;T<c.surrounding.length;T++)this.calcRepulsionForce(c,c.surrounding[T])},h.prototype.calcRepulsionRange=function(){return 0},r.exports=h},function(r,a,n){var i=n(1),s=n(7);function o(l,f,h){i.call(this,l,f,h),this.idealLength=s.DEFAULT_EDGE_LENGTH}o.prototype=Object.create(i.prototype);for(var u in i)o[u]=i[u];r.exports=o},function(r,a,n){var i=n(3);function s(u,l,f,h){i.call(this,u,l,f,h),this.springForceX=0,this.springForceY=0,this.repulsionForceX=0,this.repulsionForceY=0,this.gravitationForceX=0,this.gravitationForceY=0,this.displacementX=0,this.displacementY=0,this.startX=0,this.finishX=0,this.startY=0,this.finishY=0,this.surrounding=[]}s.prototype=Object.create(i.prototype);for(var o in i)s[o]=i[o];s.prototype.setGridCoordinates=function(u,l,f,h){this.startX=u,this.finishX=l,this.startY=f,this.finishY=h},r.exports=s},function(r,a,n){function i(s,o){this.width=0,this.height=0,s!==null&&o!==null&&(this.height=o,this.width=s)}i.prototype.getWidth=function(){return this.width},i.prototype.setWidth=function(s){this.width=s},i.prototype.getHeight=function(){return this.height},i.prototype.setHeight=function(s){this.height=s},r.exports=i},function(r,a,n){var i=n(14);function s(){this.map={},this.keys=[]}s.prototype.put=function(o,u){var l=i.createID(o);this.contains(l)||(this.map[l]=u,this.keys.push(o))},s.prototype.contains=function(o){return i.createID(o),this.map[o]!=null},s.prototype.get=function(o){var u=i.createID(o);return this.map[u]},s.prototype.keySet=function(){return this.keys},r.exports=s},function(r,a,n){var i=n(14);function s(){this.set={}}s.prototype.add=function(o){var u=i.createID(o);this.contains(u)||(this.set[u]=o)},s.prototype.remove=function(o){delete this.set[i.createID(o)]},s.prototype.clear=function(){this.set={}},s.prototype.contains=function(o){return this.set[i.createID(o)]==o},s.prototype.isEmpty=function(){return this.size()===0},s.prototype.size=function(){return Object.keys(this.set).length},s.prototype.addAllTo=function(o){for(var u=Object.keys(this.set),l=u.length,f=0;f<l;f++)o.push(this.set[u[f]])},s.prototype.size=function(){return Object.keys(this.set).length},s.prototype.addAll=function(o){for(var u=o.length,l=0;l<u;l++){var f=o[l];this.add(f)}},r.exports=s},function(r,a,n){var i=function(){function l(f,h){for(var d=0;d<h.length;d++){var c=h[d];c.enumerable=c.enumerable||!1,c.configurable=!0,"value"in c&&(c.writable=!0),Object.defineProperty(f,c.key,c)}}return function(f,h,d){return h&&l(f.prototype,h),d&&l(f,d),f}}();function s(l,f){if(!(l instanceof f))throw new TypeError("Cannot call a class as a function")}var o=n(11),u=function(){function l(f,h){s(this,l),(h!==null||h!==void 0)&&(this.compareFunction=this._defaultCompareFunction);var d=void 0;f instanceof o?d=f.size():d=f.length,this._quicksort(f,0,d-1)}return i(l,[{key:"_quicksort",value:function(h,d,c){if(d<c){var v=this._partition(h,d,c);this._quicksort(h,d,v),this._quicksort(h,v+1,c)}}},{key:"_partition",value:function(h,d,c){for(var v=this._get(h,d),p=d,g=c;;){for(;this.compareFunction(v,this._get(h,g));)g--;for(;this.compareFunction(this._get(h,p),v);)p++;if(p<g)this._swap(h,p,g),p++,g--;else return g}}},{key:"_get",value:function(h,d){return h instanceof o?h.get_object_at(d):h[d]}},{key:"_set",value:function(h,d,c){h instanceof o?h.set_object_at(d,c):h[d]=c}},{key:"_swap",value:function(h,d,c){var v=this._get(h,d);this._set(h,d,this._get(h,c)),this._set(h,c,v)}},{key:"_defaultCompareFunction",value:function(h,d){return d>h}}]),l}();r.exports=u},function(r,a,n){var i=function(){function u(l,f){for(var h=0;h<f.length;h++){var d=f[h];d.enumerable=d.enumerable||!1,d.configurable=!0,"value"in d&&(d.writable=!0),Object.defineProperty(l,d.key,d)}}return function(l,f,h){return f&&u(l.prototype,f),h&&u(l,h),l}}();function s(u,l){if(!(u instanceof l))throw new TypeError("Cannot call a class as a function")}var o=function(){function u(l,f){var h=arguments.length>2&&arguments[2]!==void 0?arguments[2]:1,d=arguments.length>3&&arguments[3]!==void 0?arguments[3]:-1,c=arguments.length>4&&arguments[4]!==void 0?arguments[4]:-1;s(this,u),this.sequence1=l,this.sequence2=f,this.match_score=h,this.mismatch_penalty=d,this.gap_penalty=c,this.iMax=l.length+1,this.jMax=f.length+1,this.grid=new Array(this.iMax);for(var v=0;v<this.iMax;v++){this.grid[v]=new Array(this.jMax);for(var p=0;p<this.jMax;p++)this.grid[v][p]=0}this.tracebackGrid=new Array(this.iMax);for(var g=0;g<this.iMax;g++){this.tracebackGrid[g]=new Array(this.jMax);for(var y=0;y<this.jMax;y++)this.tracebackGrid[g][y]=[null,null,null]}this.alignments=[],this.score=-1,this.computeGrids()}return i(u,[{key:"getScore",value:function(){return this.score}},{key:"getAlignments",value:function(){return this.alignments}},{key:"computeGrids",value:function(){for(var f=1;f<this.jMax;f++)this.grid[0][f]=this.grid[0][f-1]+this.gap_penalty,this.tracebackGrid[0][f]=[!1,!1,!0];for(var h=1;h<this.iMax;h++)this.grid[h][0]=this.grid[h-1][0]+this.gap_penalty,this.tracebackGrid[h][0]=[!1,!0,!1];for(var d=1;d<this.iMax;d++)for(var c=1;c<this.jMax;c++){var v=void 0;this.sequence1[d-1]===this.sequence2[c-1]?v=this.grid[d-1][c-1]+this.match_score:v=this.grid[d-1][c-1]+this.mismatch_penalty;var p=this.grid[d-1][c]+this.gap_penalty,g=this.grid[d][c-1]+this.gap_penalty,y=[v,p,g],b=this.arrayAllMaxIndexes(y);this.grid[d][c]=y[b[0]],this.tracebackGrid[d][c]=[b.includes(0),b.includes(1),b.includes(2)]}this.score=this.grid[this.iMax-1][this.jMax-1]}},{key:"alignmentTraceback",value:function(){var f=[];for(f.push({pos:[this.sequence1.length,this.sequence2.length],seq1:"",seq2:""});f[0];){var h=f[0],d=this.tracebackGrid[h.pos[0]][h.pos[1]];d[0]&&f.push({pos:[h.pos[0]-1,h.pos[1]-1],seq1:this.sequence1[h.pos[0]-1]+h.seq1,seq2:this.sequence2[h.pos[1]-1]+h.seq2}),d[1]&&f.push({pos:[h.pos[0]-1,h.pos[1]],seq1:this.sequence1[h.pos[0]-1]+h.seq1,seq2:"-"+h.seq2}),d[2]&&f.push({pos:[h.pos[0],h.pos[1]-1],seq1:"-"+h.seq1,seq2:this.sequence2[h.pos[1]-1]+h.seq2}),h.pos[0]===0&&h.pos[1]===0&&this.alignments.push({sequence1:h.seq1,sequence2:h.seq2}),f.shift()}return this.alignments}},{key:"getAllIndexes",value:function(f,h){for(var d=[],c=-1;(c=f.indexOf(h,c+1))!==-1;)d.push(c);return d}},{key:"arrayAllMaxIndexes",value:function(f){return this.getAllIndexes(f,Math.max.apply(null,f))}}]),u}();r.exports=o},function(r,a,n){var i=function(){};i.FDLayout=n(18),i.FDLayoutConstants=n(7),i.FDLayoutEdge=n(19),i.FDLayoutNode=n(20),i.DimensionD=n(21),i.HashMap=n(22),i.HashSet=n(23),i.IGeometry=n(8),i.IMath=n(9),i.Integer=n(10),i.Point=n(12),i.PointD=n(4),i.RandomSeed=n(16),i.RectangleD=n(13),i.Transform=n(17),i.UniqueIDGeneretor=n(14),i.Quicksort=n(24),i.LinkedList=n(11),i.LGraphObject=n(2),i.LGraph=n(5),i.LEdge=n(1),i.LGraphManager=n(6),i.LNode=n(3),i.Layout=n(15),i.LayoutConstants=n(0),i.NeedlemanWunsch=n(25),r.exports=i},function(r,a,n){function i(){this.listeners=[]}var s=i.prototype;s.addListener=function(o,u){this.listeners.push({event:o,callback:u})},s.removeListener=function(o,u){for(var l=this.listeners.length;l>=0;l--){var f=this.listeners[l];f.event===o&&f.callback===u&&this.listeners.splice(l,1)}},s.emit=function(o,u){for(var l=0;l<this.listeners.length;l++){var f=this.listeners[l];o===f.event&&f.callback(u)}},r.exports=i}])})}(qn)),qn.exports}var ro;function Xp(){return ro||(ro=1,function(t,e){(function(a,n){t.exports=n(Hp())})(hi,function(r){return function(a){var n={};function i(s){if(n[s])return n[s].exports;var o=n[s]={i:s,l:!1,exports:{}};return a[s].call(o.exports,o,o.exports,i),o.l=!0,o.exports}return i.m=a,i.c=n,i.i=function(s){return s},i.d=function(s,o,u){i.o(s,o)||Object.defineProperty(s,o,{configurable:!1,enumerable:!0,get:u})},i.n=function(s){var o=s&&s.__esModule?function(){return s.default}:function(){return s};return i.d(o,"a",o),o},i.o=function(s,o){return Object.prototype.hasOwnProperty.call(s,o)},i.p="",i(i.s=7)}([function(a,n){a.exports=r},function(a,n,i){var s=i(0).FDLayoutConstants;function o(){}for(var u in s)o[u]=s[u];o.DEFAULT_USE_MULTI_LEVEL_SCALING=!1,o.DEFAULT_RADIAL_SEPARATION=s.DEFAULT_EDGE_LENGTH,o.DEFAULT_COMPONENT_SEPERATION=60,o.TILE=!0,o.TILING_PADDING_VERTICAL=10,o.TILING_PADDING_HORIZONTAL=10,o.TREE_REDUCTION_ON_INCREMENTAL=!1,a.exports=o},function(a,n,i){var s=i(0).FDLayoutEdge;function o(l,f,h){s.call(this,l,f,h)}o.prototype=Object.create(s.prototype);for(var u in s)o[u]=s[u];a.exports=o},function(a,n,i){var s=i(0).LGraph;function o(l,f,h){s.call(this,l,f,h)}o.prototype=Object.create(s.prototype);for(var u in s)o[u]=s[u];a.exports=o},function(a,n,i){var s=i(0).LGraphManager;function o(l){s.call(this,l)}o.prototype=Object.create(s.prototype);for(var u in s)o[u]=s[u];a.exports=o},function(a,n,i){var s=i(0).FDLayoutNode,o=i(0).IMath;function u(f,h,d,c){s.call(this,f,h,d,c)}u.prototype=Object.create(s.prototype);for(var l in s)u[l]=s[l];u.prototype.move=function(){var f=this.graphManager.getLayout();this.displacementX=f.coolingFactor*(this.springForceX+this.repulsionForceX+this.gravitationForceX)/this.noOfChildren,this.displacementY=f.coolingFactor*(this.springForceY+this.repulsionForceY+this.gravitationForceY)/this.noOfChildren,Math.abs(this.displacementX)>f.coolingFactor*f.maxNodeDisplacement&&(this.displacementX=f.coolingFactor*f.maxNodeDisplacement*o.sign(this.displacementX)),Math.abs(this.displacementY)>f.coolingFactor*f.maxNodeDisplacement&&(this.displacementY=f.coolingFactor*f.maxNodeDisplacement*o.sign(this.displacementY)),this.child==null?this.moveBy(this.displacementX,this.displacementY):this.child.getNodes().length==0?this.moveBy(this.displacementX,this.displacementY):this.propogateDisplacementToChildren(this.displacementX,this.displacementY),f.totalDisplacement+=Math.abs(this.displacementX)+Math.abs(this.displacementY),this.springForceX=0,this.springForceY=0,this.repulsionForceX=0,this.repulsionForceY=0,this.gravitationForceX=0,this.gravitationForceY=0,this.displacementX=0,this.displacementY=0},u.prototype.propogateDisplacementToChildren=function(f,h){for(var d=this.getChild().getNodes(),c,v=0;v<d.length;v++)c=d[v],c.getChild()==null?(c.moveBy(f,h),c.displacementX+=f,c.displacementY+=h):c.propogateDisplacementToChildren(f,h)},u.prototype.setPred1=function(f){this.pred1=f},u.prototype.getPred1=function(){return pred1},u.prototype.getPred2=function(){return pred2},u.prototype.setNext=function(f){this.next=f},u.prototype.getNext=function(){return next},u.prototype.setProcessed=function(f){this.processed=f},u.prototype.isProcessed=function(){return processed},a.exports=u},function(a,n,i){var s=i(0).FDLayout,o=i(4),u=i(3),l=i(5),f=i(2),h=i(1),d=i(0).FDLayoutConstants,c=i(0).LayoutConstants,v=i(0).Point,p=i(0).PointD,g=i(0).Layout,y=i(0).Integer,b=i(0).IGeometry,m=i(0).LGraph,T=i(0).Transform;function C(){s.call(this),this.toBeTiled={}}C.prototype=Object.create(s.prototype);for(var S in s)C[S]=s[S];C.prototype.newGraphManager=function(){var E=new o(this);return this.graphManager=E,E},C.prototype.newGraph=function(E){return new u(null,this.graphManager,E)},C.prototype.newNode=function(E){return new l(this.graphManager,E)},C.prototype.newEdge=function(E){return new f(null,null,E)},C.prototype.initParameters=function(){s.prototype.initParameters.call(this,arguments),this.isSubLayout||(h.DEFAULT_EDGE_LENGTH<10?this.idealEdgeLength=10:this.idealEdgeLength=h.DEFAULT_EDGE_LENGTH,this.useSmartIdealEdgeLengthCalculation=h.DEFAULT_USE_SMART_IDEAL_EDGE_LENGTH_CALCULATION,this.springConstant=d.DEFAULT_SPRING_STRENGTH,this.repulsionConstant=d.DEFAULT_REPULSION_STRENGTH,this.gravityConstant=d.DEFAULT_GRAVITY_STRENGTH,this.compoundGravityConstant=d.DEFAULT_COMPOUND_GRAVITY_STRENGTH,this.gravityRangeFactor=d.DEFAULT_GRAVITY_RANGE_FACTOR,this.compoundGravityRangeFactor=d.DEFAULT_COMPOUND_GRAVITY_RANGE_FACTOR,this.prunedNodesAll=[],this.growTreeIterations=0,this.afterGrowthIterations=0,this.isTreeGrowing=!1,this.isGrowthFinished=!1,this.coolingCycle=0,this.maxCoolingCycle=this.maxIterations/d.CONVERGENCE_CHECK_PERIOD,this.finalTemperature=d.CONVERGENCE_CHECK_PERIOD/this.maxIterations,this.coolingAdjuster=1)},C.prototype.layout=function(){var E=c.DEFAULT_CREATE_BENDS_AS_NEEDED;return E&&(this.createBendpoints(),this.graphManager.resetAllEdges()),this.level=0,this.classicLayout()},C.prototype.classicLayout=function(){if(this.nodesWithGravity=this.calculateNodesToApplyGravitationTo(),this.graphManager.setAllNodesToApplyGravitation(this.nodesWithGravity),this.calcNoOfChildrenForAllNodes(),this.graphManager.calcLowestCommonAncestors(),this.graphManager.calcInclusionTreeDepths(),this.graphManager.getRoot().calcEstimatedSize(),this.calcIdealEdgeLengths(),this.incremental){if(h.TREE_REDUCTION_ON_INCREMENTAL){this.reduceTrees(),this.graphManager.resetAllNodesToApplyGravitation();var w=new Set(this.getAllNodes()),x=this.nodesWithGravity.filter(function(A){return w.has(A)});this.graphManager.setAllNodesToApplyGravitation(x)}}else{var E=this.getFlatForest();if(E.length>0)this.positionNodesRadially(E);else{this.reduceTrees(),this.graphManager.resetAllNodesToApplyGravitation();var w=new Set(this.getAllNodes()),x=this.nodesWithGravity.filter(function(D){return w.has(D)});this.graphManager.setAllNodesToApplyGravitation(x),this.positionNodesRandomly()}}return this.initSpringEmbedder(),this.runSpringEmbedder(),!0},C.prototype.tick=function(){if(this.totalIterations++,this.totalIterations===this.maxIterations&&!this.isTreeGrowing&&!this.isGrowthFinished)if(this.prunedNodesAll.length>0)this.isTreeGrowing=!0;else return!0;if(this.totalIterations%d.CONVERGENCE_CHECK_PERIOD==0&&!this.isTreeGrowing&&!this.isGrowthFinished){if(this.isConverged())if(this.prunedNodesAll.length>0)this.isTreeGrowing=!0;else return!0;this.coolingCycle++,this.layoutQuality==0?this.coolingAdjuster=this.coolingCycle:this.layoutQuality==1&&(this.coolingAdjuster=this.coolingCycle/3),this.coolingFactor=Math.max(this.initialCoolingFactor-Math.pow(this.coolingCycle,Math.log(100*(this.initialCoolingFactor-this.finalTemperature))/Math.log(this.maxCoolingCycle))/100*this.coolingAdjuster,this.finalTemperature),this.animationPeriod=Math.ceil(this.initialAnimationPeriod*Math.sqrt(this.coolingFactor))}if(this.isTreeGrowing){if(this.growTreeIterations%10==0)if(this.prunedNodesAll.length>0){this.graphManager.updateBounds(),this.updateGrid(),this.growTree(this.prunedNodesAll),this.graphManager.resetAllNodesToApplyGravitation();var E=new Set(this.getAllNodes()),w=this.nodesWithGravity.filter(function(L){return E.has(L)});this.graphManager.setAllNodesToApplyGravitation(w),this.graphManager.updateBounds(),this.updateGrid(),this.coolingFactor=d.DEFAULT_COOLING_FACTOR_INCREMENTAL}else this.isTreeGrowing=!1,this.isGrowthFinished=!0;this.growTreeIterations++}if(this.isGrowthFinished){if(this.isConverged())return!0;this.afterGrowthIterations%10==0&&(this.graphManager.updateBounds(),this.updateGrid()),this.coolingFactor=d.DEFAULT_COOLING_FACTOR_INCREMENTAL*((100-this.afterGrowthIterations)/100),this.afterGrowthIterations++}var x=!this.isTreeGrowing&&!this.isGrowthFinished,D=this.growTreeIterations%10==1&&this.isTreeGrowing||this.afterGrowthIterations%10==1&&this.isGrowthFinished;return this.totalDisplacement=0,this.graphManager.updateBounds(),this.calcSpringForces(),this.calcRepulsionForces(x,D),this.calcGravitationalForces(),this.moveNodes(),this.animate(),!1},C.prototype.getPositionsData=function(){for(var E=this.graphManager.getAllNodes(),w={},x=0;x<E.length;x++){var D=E[x].rect,L=E[x].id;w[L]={id:L,x:D.getCenterX(),y:D.getCenterY(),w:D.width,h:D.height}}return w},C.prototype.runSpringEmbedder=function(){this.initialAnimationPeriod=25,this.animationPeriod=this.initialAnimationPeriod;var E=!1;if(d.ANIMATE==="during")this.emit("layoutstarted");else{for(;!E;)E=this.tick();this.graphManager.updateBounds()}},C.prototype.calculateNodesToApplyGravitationTo=function(){var E=[],w,x=this.graphManager.getGraphs(),D=x.length,L;for(L=0;L<D;L++)w=x[L],w.updateConnected(),w.isConnected||(E=E.concat(w.getNodes()));return E},C.prototype.createBendpoints=function(){var E=[];E=E.concat(this.graphManager.getAllEdges());var w=new Set,x;for(x=0;x<E.length;x++){var D=E[x];if(!w.has(D)){var L=D.getSource(),A=D.getTarget();if(L==A)D.getBendpoints().push(new p),D.getBendpoints().push(new p),this.createDummyNodesForBendpoints(D),w.add(D);else{var N=[];if(N=N.concat(L.getEdgeListToNode(A)),N=N.concat(A.getEdgeListToNode(L)),!w.has(N[0])){if(N.length>1){var O;for(O=0;O<N.length;O++){var M=N[O];M.getBendpoints().push(new p),this.createDummyNodesForBendpoints(M)}}N.forEach(function(R){w.add(R)})}}}if(w.size==E.length)break}},C.prototype.positionNodesRadially=function(E){for(var w=new v(0,0),x=Math.ceil(Math.sqrt(E.length)),D=0,L=0,A=0,N=new p(0,0),O=0;O<E.length;O++){O%x==0&&(A=0,L=D,O!=0&&(L+=h.DEFAULT_COMPONENT_SEPERATION),D=0);var M=E[O],R=g.findCenterOfTree(M);w.x=A,w.y=L,N=C.radialLayout(M,R,w),N.y>D&&(D=Math.floor(N.y)),A=Math.floor(N.x+h.DEFAULT_COMPONENT_SEPERATION)}this.transform(new p(c.WORLD_CENTER_X-N.x/2,c.WORLD_CENTER_Y-N.y/2))},C.radialLayout=function(E,w,x){var D=Math.max(this.maxDiagonalInTree(E),h.DEFAULT_RADIAL_SEPARATION);C.branchRadialLayout(w,null,0,359,0,D);var L=m.calculateBounds(E),A=new T;A.setDeviceOrgX(L.getMinX()),A.setDeviceOrgY(L.getMinY()),A.setWorldOrgX(x.x),A.setWorldOrgY(x.y);for(var N=0;N<E.length;N++){var O=E[N];O.transform(A)}var M=new p(L.getMaxX(),L.getMaxY());return A.inverseTransformPoint(M)},C.branchRadialLayout=function(E,w,x,D,L,A){var N=(D-x+1)/2;N<0&&(N+=180);var O=(N+x)%360,M=O*b.TWO_PI/360,R=L*Math.cos(M),k=L*Math.sin(M);E.setCenter(R,k);var P=[];P=P.concat(E.getEdges());var B=P.length;w!=null&&B--;for(var z=0,G=P.length,F,U=E.getEdgesBetween(w);U.length>1;){var Y=U[0];U.splice(0,1);var W=P.indexOf(Y);W>=0&&P.splice(W,1),G--,B--}w!=null?F=(P.indexOf(U[0])+1)%G:F=0;for(var K=Math.abs(D-x)/B,j=F;z!=B;j=++j%G){var _=P[j].getOtherEnd(E);if(_!=w){var V=(x+z*K)%360,H=(V+K)%360;C.branchRadialLayout(_,E,V,H,L+A,A),z++}}},C.maxDiagonalInTree=function(E){for(var w=y.MIN_VALUE,x=0;x<E.length;x++){var D=E[x],L=D.getDiagonal();L>w&&(w=L)}return w},C.prototype.calcRepulsionRange=function(){return 2*(this.level+1)*this.idealEdgeLength},C.prototype.groupZeroDegreeMembers=function(){var E=this,w={};this.memberGroups={},this.idToDummyNode={};for(var x=[],D=this.graphManager.getAllNodes(),L=0;L<D.length;L++){var A=D[L],N=A.getParent();this.getNodeDegreeWithChildren(A)===0&&(N.id==null||!this.getToBeTiled(N))&&x.push(A)}for(var L=0;L<x.length;L++){var A=x[L],O=A.getParent().id;typeof w[O]>"u"&&(w[O]=[]),w[O]=w[O].concat(A)}Object.keys(w).forEach(function(M){if(w[M].length>1){var R="DummyCompound_"+M;E.memberGroups[R]=w[M];var k=w[M][0].getParent(),P=new l(E.graphManager);P.id=R,P.paddingLeft=k.paddingLeft||0,P.paddingRight=k.paddingRight||0,P.paddingBottom=k.paddingBottom||0,P.paddingTop=k.paddingTop||0,E.idToDummyNode[R]=P;var B=E.getGraphManager().add(E.newGraph(),P),z=k.getChild();z.add(P);for(var G=0;G<w[M].length;G++){var F=w[M][G];z.remove(F),B.add(F)}}})},C.prototype.clearCompounds=function(){var E={},w={};this.performDFSOnCompounds();for(var x=0;x<this.compoundOrder.length;x++)w[this.compoundOrder[x].id]=this.compoundOrder[x],E[this.compoundOrder[x].id]=[].concat(this.compoundOrder[x].getChild().getNodes()),this.graphManager.remove(this.compoundOrder[x].getChild()),this.compoundOrder[x].child=null;this.graphManager.resetAllNodes(),this.tileCompoundMembers(E,w)},C.prototype.clearZeroDegreeMembers=function(){var E=this,w=this.tiledZeroDegreePack=[];Object.keys(this.memberGroups).forEach(function(x){var D=E.idToDummyNode[x];w[x]=E.tileNodes(E.memberGroups[x],D.paddingLeft+D.paddingRight),D.rect.width=w[x].width,D.rect.height=w[x].height})},C.prototype.repopulateCompounds=function(){for(var E=this.compoundOrder.length-1;E>=0;E--){var w=this.compoundOrder[E],x=w.id,D=w.paddingLeft,L=w.paddingTop;this.adjustLocations(this.tiledMemberPack[x],w.rect.x,w.rect.y,D,L)}},C.prototype.repopulateZeroDegreeMembers=function(){var E=this,w=this.tiledZeroDegreePack;Object.keys(w).forEach(function(x){var D=E.idToDummyNode[x],L=D.paddingLeft,A=D.paddingTop;E.adjustLocations(w[x],D.rect.x,D.rect.y,L,A)})},C.prototype.getToBeTiled=function(E){var w=E.id;if(this.toBeTiled[w]!=null)return this.toBeTiled[w];var x=E.getChild();if(x==null)return this.toBeTiled[w]=!1,!1;for(var D=x.getNodes(),L=0;L<D.length;L++){var A=D[L];if(this.getNodeDegree(A)>0)return this.toBeTiled[w]=!1,!1;if(A.getChild()==null){this.toBeTiled[A.id]=!1;continue}if(!this.getToBeTiled(A))return this.toBeTiled[w]=!1,!1}return this.toBeTiled[w]=!0,!0},C.prototype.getNodeDegree=function(E){E.id;for(var w=E.getEdges(),x=0,D=0;D<w.length;D++){var L=w[D];L.getSource().id!==L.getTarget().id&&(x=x+1)}return x},C.prototype.getNodeDegreeWithChildren=function(E){var w=this.getNodeDegree(E);if(E.getChild()==null)return w;for(var x=E.getChild().getNodes(),D=0;D<x.length;D++){var L=x[D];w+=this.getNodeDegreeWithChildren(L)}return w},C.prototype.performDFSOnCompounds=function(){this.compoundOrder=[],this.fillCompexOrderByDFS(this.graphManager.getRoot().getNodes())},C.prototype.fillCompexOrderByDFS=function(E){for(var w=0;w<E.length;w++){var x=E[w];x.getChild()!=null&&this.fillCompexOrderByDFS(x.getChild().getNodes()),this.getToBeTiled(x)&&this.compoundOrder.push(x)}},C.prototype.adjustLocations=function(E,w,x,D,L){w+=D,x+=L;for(var A=w,N=0;N<E.rows.length;N++){var O=E.rows[N];w=A;for(var M=0,R=0;R<O.length;R++){var k=O[R];k.rect.x=w,k.rect.y=x,w+=k.rect.width+E.horizontalPadding,k.rect.height>M&&(M=k.rect.height)}x+=M+E.verticalPadding}},C.prototype.tileCompoundMembers=function(E,w){var x=this;this.tiledMemberPack=[],Object.keys(E).forEach(function(D){var L=w[D];x.tiledMemberPack[D]=x.tileNodes(E[D],L.paddingLeft+L.paddingRight),L.rect.width=x.tiledMemberPack[D].width,L.rect.height=x.tiledMemberPack[D].height})},C.prototype.tileNodes=function(E,w){var x=h.TILING_PADDING_VERTICAL,D=h.TILING_PADDING_HORIZONTAL,L={rows:[],rowWidth:[],rowHeight:[],width:0,height:w,verticalPadding:x,horizontalPadding:D};E.sort(function(O,M){return O.rect.width*O.rect.height>M.rect.width*M.rect.height?-1:O.rect.width*O.rect.height<M.rect.width*M.rect.height?1:0});for(var A=0;A<E.length;A++){var N=E[A];L.rows.length==0?this.insertNodeToRow(L,N,0,w):this.canAddHorizontal(L,N.rect.width,N.rect.height)?this.insertNodeToRow(L,N,this.getShortestRowIndex(L),w):this.insertNodeToRow(L,N,L.rows.length,w),this.shiftToLastRow(L)}return L},C.prototype.insertNodeToRow=function(E,w,x,D){var L=D;if(x==E.rows.length){var A=[];E.rows.push(A),E.rowWidth.push(L),E.rowHeight.push(0)}var N=E.rowWidth[x]+w.rect.width;E.rows[x].length>0&&(N+=E.horizontalPadding),E.rowWidth[x]=N,E.width<N&&(E.width=N);var O=w.rect.height;x>0&&(O+=E.verticalPadding);var M=0;O>E.rowHeight[x]&&(M=E.rowHeight[x],E.rowHeight[x]=O,M=E.rowHeight[x]-M),E.height+=M,E.rows[x].push(w)},C.prototype.getShortestRowIndex=function(E){for(var w=-1,x=Number.MAX_VALUE,D=0;D<E.rows.length;D++)E.rowWidth[D]<x&&(w=D,x=E.rowWidth[D]);return w},C.prototype.getLongestRowIndex=function(E){for(var w=-1,x=Number.MIN_VALUE,D=0;D<E.rows.length;D++)E.rowWidth[D]>x&&(w=D,x=E.rowWidth[D]);return w},C.prototype.canAddHorizontal=function(E,w,x){var D=this.getShortestRowIndex(E);if(D<0)return!0;var L=E.rowWidth[D];if(L+E.horizontalPadding+w<=E.width)return!0;var A=0;E.rowHeight[D]<x&&D>0&&(A=x+E.verticalPadding-E.rowHeight[D]);var N;E.width-L>=w+E.horizontalPadding?N=(E.height+A)/(L+w+E.horizontalPadding):N=(E.height+A)/E.width,A=x+E.verticalPadding;var O;return E.width<w?O=(E.height+A)/w:O=(E.height+A)/E.width,O<1&&(O=1/O),N<1&&(N=1/N),N<O},C.prototype.shiftToLastRow=function(E){var w=this.getLongestRowIndex(E),x=E.rowWidth.length-1,D=E.rows[w],L=D[D.length-1],A=L.width+E.horizontalPadding;if(E.width-E.rowWidth[x]>A&&w!=x){D.splice(-1,1),E.rows[x].push(L),E.rowWidth[w]=E.rowWidth[w]-A,E.rowWidth[x]=E.rowWidth[x]+A,E.width=E.rowWidth[instance.getLongestRowIndex(E)];for(var N=Number.MIN_VALUE,O=0;O<D.length;O++)D[O].height>N&&(N=D[O].height);w>0&&(N+=E.verticalPadding);var M=E.rowHeight[w]+E.rowHeight[x];E.rowHeight[w]=N,E.rowHeight[x]<L.height+E.verticalPadding&&(E.rowHeight[x]=L.height+E.verticalPadding);var R=E.rowHeight[w]+E.rowHeight[x];E.height+=R-M,this.shiftToLastRow(E)}},C.prototype.tilingPreLayout=function(){h.TILE&&(this.groupZeroDegreeMembers(),this.clearCompounds(),this.clearZeroDegreeMembers())},C.prototype.tilingPostLayout=function(){h.TILE&&(this.repopulateZeroDegreeMembers(),this.repopulateCompounds())},C.prototype.reduceTrees=function(){for(var E=[],w=!0,x;w;){var D=this.graphManager.getAllNodes(),L=[];w=!1;for(var A=0;A<D.length;A++)x=D[A],x.getEdges().length==1&&!x.getEdges()[0].isInterGraph&&x.getChild()==null&&(L.push([x,x.getEdges()[0],x.getOwner()]),w=!0);if(w==!0){for(var N=[],O=0;O<L.length;O++)L[O][0].getEdges().length==1&&(N.push(L[O]),L[O][0].getOwner().remove(L[O][0]));E.push(N),this.graphManager.resetAllNodes(),this.graphManager.resetAllEdges()}}this.prunedNodesAll=E},C.prototype.growTree=function(E){for(var w=E.length,x=E[w-1],D,L=0;L<x.length;L++)D=x[L],this.findPlaceforPrunedNode(D),D[2].add(D[0]),D[2].add(D[1],D[1].source,D[1].target);E.splice(E.length-1,1),this.graphManager.resetAllNodes(),this.graphManager.resetAllEdges()},C.prototype.findPlaceforPrunedNode=function(E){var w,x,D=E[0];D==E[1].source?x=E[1].target:x=E[1].source;var L=x.startX,A=x.finishX,N=x.startY,O=x.finishY,M=0,R=0,k=0,P=0,B=[M,k,R,P];if(N>0)for(var z=L;z<=A;z++)B[0]+=this.grid[z][N-1].length+this.grid[z][N].length-1;if(A<this.grid.length-1)for(var z=N;z<=O;z++)B[1]+=this.grid[A+1][z].length+this.grid[A][z].length-1;if(O<this.grid[0].length-1)for(var z=L;z<=A;z++)B[2]+=this.grid[z][O+1].length+this.grid[z][O].length-1;if(L>0)for(var z=N;z<=O;z++)B[3]+=this.grid[L-1][z].length+this.grid[L][z].length-1;for(var G=y.MAX_VALUE,F,U,Y=0;Y<B.length;Y++)B[Y]<G?(G=B[Y],F=1,U=Y):B[Y]==G&&F++;if(F==3&&G==0)B[0]==0&&B[1]==0&&B[2]==0?w=1:B[0]==0&&B[1]==0&&B[3]==0?w=0:B[0]==0&&B[2]==0&&B[3]==0?w=3:B[1]==0&&B[2]==0&&B[3]==0&&(w=2);else if(F==2&&G==0){var W=Math.floor(Math.random()*2);B[0]==0&&B[1]==0?W==0?w=0:w=1:B[0]==0&&B[2]==0?W==0?w=0:w=2:B[0]==0&&B[3]==0?W==0?w=0:w=3:B[1]==0&&B[2]==0?W==0?w=1:w=2:B[1]==0&&B[3]==0?W==0?w=1:w=3:W==0?w=2:w=3}else if(F==4&&G==0){var W=Math.floor(Math.random()*4);w=W}else w=U;w==0?D.setCenter(x.getCenterX(),x.getCenterY()-x.getHeight()/2-d.DEFAULT_EDGE_LENGTH-D.getHeight()/2):w==1?D.setCenter(x.getCenterX()+x.getWidth()/2+d.DEFAULT_EDGE_LENGTH+D.getWidth()/2,x.getCenterY()):w==2?D.setCenter(x.getCenterX(),x.getCenterY()+x.getHeight()/2+d.DEFAULT_EDGE_LENGTH+D.getHeight()/2):D.setCenter(x.getCenterX()-x.getWidth()/2-d.DEFAULT_EDGE_LENGTH-D.getWidth()/2,x.getCenterY())},a.exports=C},function(a,n,i){var s={};s.layoutBase=i(0),s.CoSEConstants=i(1),s.CoSEEdge=i(2),s.CoSEGraph=i(3),s.CoSEGraphManager=i(4),s.CoSELayout=i(6),s.CoSENode=i(5),a.exports=s}])})}(Xn)),Xn.exports}(function(t,e){(function(a,n){t.exports=n(Xp())})(hi,function(r){return function(a){var n={};function i(s){if(n[s])return n[s].exports;var o=n[s]={i:s,l:!1,exports:{}};return a[s].call(o.exports,o,o.exports,i),o.l=!0,o.exports}return i.m=a,i.c=n,i.i=function(s){return s},i.d=function(s,o,u){i.o(s,o)||Object.defineProperty(s,o,{configurable:!1,enumerable:!0,get:u})},i.n=function(s){var o=s&&s.__esModule?function(){return s.default}:function(){return s};return i.d(o,"a",o),o},i.o=function(s,o){return Object.prototype.hasOwnProperty.call(s,o)},i.p="",i(i.s=1)}([function(a,n){a.exports=r},function(a,n,i){var s=i(0).layoutBase.LayoutConstants,o=i(0).layoutBase.FDLayoutConstants,u=i(0).CoSEConstants,l=i(0).CoSELayout,f=i(0).CoSENode,h=i(0).layoutBase.PointD,d=i(0).layoutBase.DimensionD,c={ready:function(){},stop:function(){},quality:"default",nodeDimensionsIncludeLabels:!1,refresh:30,fit:!0,padding:10,randomize:!0,nodeRepulsion:4500,idealEdgeLength:50,edgeElasticity:.45,nestingFactor:.1,gravity:.25,numIter:2500,tile:!0,animate:"end",animationDuration:500,tilingPaddingVertical:10,tilingPaddingHorizontal:10,gravityRangeCompound:1.5,gravityCompound:1,gravityRange:3.8,initialEnergyOnIncremental:.5};function v(b,m){var T={};for(var C in b)T[C]=b[C];for(var C in m)T[C]=m[C];return T}function p(b){this.options=v(c,b),g(this.options)}var g=function(m){m.nodeRepulsion!=null&&(u.DEFAULT_REPULSION_STRENGTH=o.DEFAULT_REPULSION_STRENGTH=m.nodeRepulsion),m.idealEdgeLength!=null&&(u.DEFAULT_EDGE_LENGTH=o.DEFAULT_EDGE_LENGTH=m.idealEdgeLength),m.edgeElasticity!=null&&(u.DEFAULT_SPRING_STRENGTH=o.DEFAULT_SPRING_STRENGTH=m.edgeElasticity),m.nestingFactor!=null&&(u.PER_LEVEL_IDEAL_EDGE_LENGTH_FACTOR=o.PER_LEVEL_IDEAL_EDGE_LENGTH_FACTOR=m.nestingFactor),m.gravity!=null&&(u.DEFAULT_GRAVITY_STRENGTH=o.DEFAULT_GRAVITY_STRENGTH=m.gravity),m.numIter!=null&&(u.MAX_ITERATIONS=o.MAX_ITERATIONS=m.numIter),m.gravityRange!=null&&(u.DEFAULT_GRAVITY_RANGE_FACTOR=o.DEFAULT_GRAVITY_RANGE_FACTOR=m.gravityRange),m.gravityCompound!=null&&(u.DEFAULT_COMPOUND_GRAVITY_STRENGTH=o.DEFAULT_COMPOUND_GRAVITY_STRENGTH=m.gravityCompound),m.gravityRangeCompound!=null&&(u.DEFAULT_COMPOUND_GRAVITY_RANGE_FACTOR=o.DEFAULT_COMPOUND_GRAVITY_RANGE_FACTOR=m.gravityRangeCompound),m.initialEnergyOnIncremental!=null&&(u.DEFAULT_COOLING_FACTOR_INCREMENTAL=o.DEFAULT_COOLING_FACTOR_INCREMENTAL=m.initialEnergyOnIncremental),m.quality=="draft"?s.QUALITY=0:m.quality=="proof"?s.QUALITY=2:s.QUALITY=1,u.NODE_DIMENSIONS_INCLUDE_LABELS=o.NODE_DIMENSIONS_INCLUDE_LABELS=s.NODE_DIMENSIONS_INCLUDE_LABELS=m.nodeDimensionsIncludeLabels,u.DEFAULT_INCREMENTAL=o.DEFAULT_INCREMENTAL=s.DEFAULT_INCREMENTAL=!m.randomize,u.ANIMATE=o.ANIMATE=s.ANIMATE=m.animate,u.TILE=m.tile,u.TILING_PADDING_VERTICAL=typeof m.tilingPaddingVertical=="function"?m.tilingPaddingVertical.call():m.tilingPaddingVertical,u.TILING_PADDING_HORIZONTAL=typeof m.tilingPaddingHorizontal=="function"?m.tilingPaddingHorizontal.call():m.tilingPaddingHorizontal};p.prototype.run=function(){var b,m,T=this.options;this.idToLNode={};var C=this.layout=new l,S=this;S.stopped=!1,this.cy=this.options.cy,this.cy.trigger({type:"layoutstart",layout:this});var E=C.newGraphManager();this.gm=E;var w=this.options.eles.nodes(),x=this.options.eles.edges();this.root=E.addRoot(),this.processChildrenList(this.root,this.getTopMostNodes(w),C);for(var D=0;D<x.length;D++){var L=x[D],A=this.idToLNode[L.data("source")],N=this.idToLNode[L.data("target")];if(A!==N&&A.getEdgesBetween(N).length==0){var O=E.add(C.newEdge(),A,N);O.id=L.id()}}var M=function(P,B){typeof P=="number"&&(P=B);var z=P.data("id"),G=S.idToLNode[z];return{x:G.getRect().getCenterX(),y:G.getRect().getCenterY()}},R=function k(){for(var P=function(){T.fit&&T.cy.fit(T.eles,T.padding),b||(b=!0,S.cy.one("layoutready",T.ready),S.cy.trigger({type:"layoutready",layout:S}))},B=S.options.refresh,z,G=0;G<B&&!z;G++)z=S.stopped||S.layout.tick();if(z){C.checkLayoutSuccess()&&!C.isSubLayout&&C.doPostLayout(),C.tilingPostLayout&&C.tilingPostLayout(),C.isLayoutFinished=!0,S.options.eles.nodes().positions(M),P(),S.cy.one("layoutstop",S.options.stop),S.cy.trigger({type:"layoutstop",layout:S}),m&&cancelAnimationFrame(m),b=!1;return}var F=S.layout.getPositionsData();T.eles.nodes().positions(function(U,Y){if(typeof U=="number"&&(U=Y),!U.isParent()){for(var W=U.id(),K=F[W],j=U;K==null&&(K=F[j.data("parent")]||F["DummyCompound_"+j.data("parent")],F[W]=K,j=j.parent()[0],j!=null););return K!=null?{x:K.x,y:K.y}:{x:U.position("x"),y:U.position("y")}}}),P(),m=requestAnimationFrame(k)};return C.addListener("layoutstarted",function(){S.options.animate==="during"&&(m=requestAnimationFrame(R))}),C.runLayout(),this.options.animate!=="during"&&(S.options.eles.nodes().not(":parent").layoutPositions(S,S.options,M),b=!1),this},p.prototype.getTopMostNodes=function(b){for(var m={},T=0;T<b.length;T++)m[b[T].id()]=!0;var C=b.filter(function(S,E){typeof S=="number"&&(S=E);for(var w=S.parent()[0];w!=null;){if(m[w.id()])return!1;w=w.parent()[0]}return!0});return C},p.prototype.processChildrenList=function(b,m,T){for(var C=m.length,S=0;S<C;S++){var E=m[S],w=E.children(),x,D=E.layoutDimensions({nodeDimensionsIncludeLabels:this.options.nodeDimensionsIncludeLabels});if(E.outerWidth()!=null&&E.outerHeight()!=null?x=b.add(new f(T.graphManager,new h(E.position("x")-D.w/2,E.position("y")-D.h/2),new d(parseFloat(D.w),parseFloat(D.h)))):x=b.add(new f(this.graphManager)),x.id=E.data("id"),x.paddingLeft=parseInt(E.css("padding")),x.paddingTop=parseInt(E.css("padding")),x.paddingRight=parseInt(E.css("padding")),x.paddingBottom=parseInt(E.css("padding")),this.options.nodeDimensionsIncludeLabels&&E.isParent()){var L=E.boundingBox({includeLabels:!0,includeNodes:!1}).w,A=E.boundingBox({includeLabels:!0,includeNodes:!1}).h,N=E.css("text-halign");x.labelWidth=L,x.labelHeight=A,x.labelPos=N}if(this.idToLNode[E.data("id")]=x,isNaN(x.rect.x)&&(x.rect.x=0),isNaN(x.rect.y)&&(x.rect.y=0),w!=null&&w.length>0){var O;O=T.getGraphManager().add(T.newGraph(),x),this.processChildrenList(O,w,T)}}},p.prototype.stop=function(){return this.stopped=!0,this};var y=function(m){m("layout","cose-bilkent",p)};typeof cytoscape<"u"&&y(cytoscape),a.exports=y}])})})(Yu);var qp=Yu.exports;const Wp=rl(qp);var fi=function(){var t=function(T,C,S,E){for(S=S||{},E=T.length;E--;S[T[E]]=C);return S},e=[1,4],r=[1,13],a=[1,12],n=[1,15],i=[1,16],s=[1,20],o=[1,19],u=[6,7,8],l=[1,26],f=[1,24],h=[1,25],d=[6,7,11],c=[1,6,13,15,16,19,22],v=[1,33],p=[1,34],g=[1,6,7,11,13,15,16,19,22],y={trace:function(){},yy:{},symbols_:{error:2,start:3,mindMap:4,spaceLines:5,SPACELINE:6,NL:7,MINDMAP:8,document:9,stop:10,EOF:11,statement:12,SPACELIST:13,node:14,ICON:15,CLASS:16,nodeWithId:17,nodeWithoutId:18,NODE_DSTART:19,NODE_DESCR:20,NODE_DEND:21,NODE_ID:22,$accept:0,$end:1},terminals_:{2:"error",6:"SPACELINE",7:"NL",8:"MINDMAP",11:"EOF",13:"SPACELIST",15:"ICON",16:"CLASS",19:"NODE_DSTART",20:"NODE_DESCR",21:"NODE_DEND",22:"NODE_ID"},productions_:[0,[3,1],[3,2],[5,1],[5,2],[5,2],[4,2],[4,3],[10,1],[10,1],[10,1],[10,2],[10,2],[9,3],[9,2],[12,2],[12,2],[12,2],[12,1],[12,1],[12,1],[12,1],[12,1],[14,1],[14,1],[18,3],[17,1],[17,4]],performAction:function(C,S,E,w,x,D,L){var A=D.length-1;switch(x){case 6:case 7:return w;case 8:w.getLogger().trace("Stop NL ");break;case 9:w.getLogger().trace("Stop EOF ");break;case 11:w.getLogger().trace("Stop NL2 ");break;case 12:w.getLogger().trace("Stop EOF2 ");break;case 15:w.getLogger().info("Node: ",D[A].id),w.addNode(D[A-1].length,D[A].id,D[A].descr,D[A].type);break;case 16:w.getLogger().trace("Icon: ",D[A]),w.decorateNode({icon:D[A]});break;case 17:case 21:w.decorateNode({class:D[A]});break;case 18:w.getLogger().trace("SPACELIST");break;case 19:w.getLogger().trace("Node: ",D[A].id),w.addNode(0,D[A].id,D[A].descr,D[A].type);break;case 20:w.decorateNode({icon:D[A]});break;case 25:w.getLogger().trace("node found ..",D[A-2]),this.$={id:D[A-1],descr:D[A-1],type:w.getType(D[A-2],D[A])};break;case 26:this.$={id:D[A],descr:D[A],type:w.nodeType.DEFAULT};break;case 27:w.getLogger().trace("node found ..",D[A-3]),this.$={id:D[A-3],descr:D[A-1],type:w.getType(D[A-2],D[A])};break}},table:[{3:1,4:2,5:3,6:[1,5],8:e},{1:[3]},{1:[2,1]},{4:6,6:[1,7],7:[1,8],8:e},{6:r,7:[1,10],9:9,12:11,13:a,14:14,15:n,16:i,17:17,18:18,19:s,22:o},t(u,[2,3]),{1:[2,2]},t(u,[2,4]),t(u,[2,5]),{1:[2,6],6:r,12:21,13:a,14:14,15:n,16:i,17:17,18:18,19:s,22:o},{6:r,9:22,12:11,13:a,14:14,15:n,16:i,17:17,18:18,19:s,22:o},{6:l,7:f,10:23,11:h},t(d,[2,22],{17:17,18:18,14:27,15:[1,28],16:[1,29],19:s,22:o}),t(d,[2,18]),t(d,[2,19]),t(d,[2,20]),t(d,[2,21]),t(d,[2,23]),t(d,[2,24]),t(d,[2,26],{19:[1,30]}),{20:[1,31]},{6:l,7:f,10:32,11:h},{1:[2,7],6:r,12:21,13:a,14:14,15:n,16:i,17:17,18:18,19:s,22:o},t(c,[2,14],{7:v,11:p}),t(g,[2,8]),t(g,[2,9]),t(g,[2,10]),t(d,[2,15]),t(d,[2,16]),t(d,[2,17]),{20:[1,35]},{21:[1,36]},t(c,[2,13],{7:v,11:p}),t(g,[2,11]),t(g,[2,12]),{21:[1,37]},t(d,[2,25]),t(d,[2,27])],defaultActions:{2:[2,1],6:[2,2]},parseError:function(C,S){if(S.recoverable)this.trace(C);else{var E=new Error(C);throw E.hash=S,E}},parse:function(C){var S=this,E=[0],w=[],x=[null],D=[],L=this.table,A="",N=0,O=0,M=2,R=1,k=D.slice.call(arguments,1),P=Object.create(this.lexer),B={yy:{}};for(var z in this.yy)Object.prototype.hasOwnProperty.call(this.yy,z)&&(B.yy[z]=this.yy[z]);P.setInput(C,B.yy),B.yy.lexer=P,B.yy.parser=this,typeof P.yylloc>"u"&&(P.yylloc={});var G=P.yylloc;D.push(G);var F=P.options&&P.options.ranges;typeof B.yy.parseError=="function"?this.parseError=B.yy.parseError:this.parseError=Object.getPrototypeOf(this).parseError;function U(){var te;return te=w.pop()||P.lex()||R,typeof te!="number"&&(te instanceof Array&&(w=te,te=w.pop()),te=S.symbols_[te]||te),te}for(var Y,W,K,j,_={},V,H,Q,ne;;){if(W=E[E.length-1],this.defaultActions[W]?K=this.defaultActions[W]:((Y===null||typeof Y>"u")&&(Y=U()),K=L[W]&&L[W][Y]),typeof K>"u"||!K.length||!K[0]){var ce="";ne=[];for(V in L[W])this.terminals_[V]&&V>M&&ne.push("'"+this.terminals_[V]+"'");P.showPosition?ce="Parse error on line "+(N+1)+`:
`+P.showPosition()+`
Expecting `+ne.join(", ")+", got '"+(this.terminals_[Y]||Y)+"'":ce="Parse error on line "+(N+1)+": Unexpected "+(Y==R?"end of input":"'"+(this.terminals_[Y]||Y)+"'"),this.parseError(ce,{text:P.match,token:this.terminals_[Y]||Y,line:P.yylineno,loc:G,expected:ne})}if(K[0]instanceof Array&&K.length>1)throw new Error("Parse Error: multiple actions possible at state: "+W+", token: "+Y);switch(K[0]){case 1:E.push(Y),x.push(P.yytext),D.push(P.yylloc),E.push(K[1]),Y=null,O=P.yyleng,A=P.yytext,N=P.yylineno,G=P.yylloc;break;case 2:if(H=this.productions_[K[1]][1],_.$=x[x.length-H],_._$={first_line:D[D.length-(H||1)].first_line,last_line:D[D.length-1].last_line,first_column:D[D.length-(H||1)].first_column,last_column:D[D.length-1].last_column},F&&(_._$.range=[D[D.length-(H||1)].range[0],D[D.length-1].range[1]]),j=this.performAction.apply(_,[A,O,N,B.yy,K[1],x,D].concat(k)),typeof j<"u")return j;H&&(E=E.slice(0,-1*H*2),x=x.slice(0,-1*H),D=D.slice(0,-1*H)),E.push(this.productions_[K[1]][0]),x.push(_.$),D.push(_._$),Q=L[E[E.length-2]][E[E.length-1]],E.push(Q);break;case 3:return!0}}return!0}},b=function(){var T={EOF:1,parseError:function(S,E){if(this.yy.parser)this.yy.parser.parseError(S,E);else throw new Error(S)},setInput:function(C,S){return this.yy=S||this.yy||{},this._input=C,this._more=this._backtrack=this.done=!1,this.yylineno=this.yyleng=0,this.yytext=this.matched=this.match="",this.conditionStack=["INITIAL"],this.yylloc={first_line:1,first_column:0,last_line:1,last_column:0},this.options.ranges&&(this.yylloc.range=[0,0]),this.offset=0,this},input:function(){var C=this._input[0];this.yytext+=C,this.yyleng++,this.offset++,this.match+=C,this.matched+=C;var S=C.match(/(?:\r\n?|\n).*/g);return S?(this.yylineno++,this.yylloc.last_line++):this.yylloc.last_column++,this.options.ranges&&this.yylloc.range[1]++,this._input=this._input.slice(1),C},unput:function(C){var S=C.length,E=C.split(/(?:\r\n?|\n)/g);this._input=C+this._input,this.yytext=this.yytext.substr(0,this.yytext.length-S),this.offset-=S;var w=this.match.split(/(?:\r\n?|\n)/g);this.match=this.match.substr(0,this.match.length-1),this.matched=this.matched.substr(0,this.matched.length-1),E.length-1&&(this.yylineno-=E.length-1);var x=this.yylloc.range;return this.yylloc={first_line:this.yylloc.first_line,last_line:this.yylineno+1,first_column:this.yylloc.first_column,last_column:E?(E.length===w.length?this.yylloc.first_column:0)+w[w.length-E.length].length-E[0].length:this.yylloc.first_column-S},this.options.ranges&&(this.yylloc.range=[x[0],x[0]+this.yyleng-S]),this.yyleng=this.yytext.length,this},more:function(){return this._more=!0,this},reject:function(){if(this.options.backtrack_lexer)this._backtrack=!0;else return this.parseError("Lexical error on line "+(this.yylineno+1)+`. You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).
`+this.showPosition(),{text:"",token:null,line:this.yylineno});return this},less:function(C){this.unput(this.match.slice(C))},pastInput:function(){var C=this.matched.substr(0,this.matched.length-this.match.length);return(C.length>20?"...":"")+C.substr(-20).replace(/\n/g,"")},upcomingInput:function(){var C=this.match;return C.length<20&&(C+=this._input.substr(0,20-C.length)),(C.substr(0,20)+(C.length>20?"...":"")).replace(/\n/g,"")},showPosition:function(){var C=this.pastInput(),S=new Array(C.length+1).join("-");return C+this.upcomingInput()+`
`+S+"^"},test_match:function(C,S){var E,w,x;if(this.options.backtrack_lexer&&(x={yylineno:this.yylineno,yylloc:{first_line:this.yylloc.first_line,last_line:this.last_line,first_column:this.yylloc.first_column,last_column:this.yylloc.last_column},yytext:this.yytext,match:this.match,matches:this.matches,matched:this.matched,yyleng:this.yyleng,offset:this.offset,_more:this._more,_input:this._input,yy:this.yy,conditionStack:this.conditionStack.slice(0),done:this.done},this.options.ranges&&(x.yylloc.range=this.yylloc.range.slice(0))),w=C[0].match(/(?:\r\n?|\n).*/g),w&&(this.yylineno+=w.length),this.yylloc={first_line:this.yylloc.last_line,last_line:this.yylineno+1,first_column:this.yylloc.last_column,last_column:w?w[w.length-1].length-w[w.length-1].match(/\r?\n?/)[0].length:this.yylloc.last_column+C[0].length},this.yytext+=C[0],this.match+=C[0],this.matches=C,this.yyleng=this.yytext.length,this.options.ranges&&(this.yylloc.range=[this.offset,this.offset+=this.yyleng]),this._more=!1,this._backtrack=!1,this._input=this._input.slice(C[0].length),this.matched+=C[0],E=this.performAction.call(this,this.yy,this,S,this.conditionStack[this.conditionStack.length-1]),this.done&&this._input&&(this.done=!1),E)return E;if(this._backtrack){for(var D in x)this[D]=x[D];return!1}return!1},next:function(){if(this.done)return this.EOF;this._input||(this.done=!0);var C,S,E,w;this._more||(this.yytext="",this.match="");for(var x=this._currentRules(),D=0;D<x.length;D++)if(E=this._input.match(this.rules[x[D]]),E&&(!S||E[0].length>S[0].length)){if(S=E,w=D,this.options.backtrack_lexer){if(C=this.test_match(E,x[D]),C!==!1)return C;if(this._backtrack){S=!1;continue}else return!1}else if(!this.options.flex)break}return S?(C=this.test_match(S,x[w]),C!==!1?C:!1):this._input===""?this.EOF:this.parseError("Lexical error on line "+(this.yylineno+1)+`. Unrecognized text.
`+this.showPosition(),{text:"",token:null,line:this.yylineno})},lex:function(){var S=this.next();return S||this.lex()},begin:function(S){this.conditionStack.push(S)},popState:function(){var S=this.conditionStack.length-1;return S>0?this.conditionStack.pop():this.conditionStack[0]},_currentRules:function(){return this.conditionStack.length&&this.conditionStack[this.conditionStack.length-1]?this.conditions[this.conditionStack[this.conditionStack.length-1]].rules:this.conditions.INITIAL.rules},topState:function(S){return S=this.conditionStack.length-1-Math.abs(S||0),S>=0?this.conditionStack[S]:"INITIAL"},pushState:function(S){this.begin(S)},stateStackSize:function(){return this.conditionStack.length},options:{"case-insensitive":!0},performAction:function(S,E,w,x){switch(w){case 0:return S.getLogger().trace("Found comment",E.yytext),6;case 1:return 8;case 2:this.begin("CLASS");break;case 3:return this.popState(),16;case 4:this.popState();break;case 5:S.getLogger().trace("Begin icon"),this.begin("ICON");break;case 6:return S.getLogger().trace("SPACELINE"),6;case 7:return 7;case 8:return 15;case 9:S.getLogger().trace("end icon"),this.popState();break;case 10:return S.getLogger().trace("Exploding node"),this.begin("NODE"),19;case 11:return S.getLogger().trace("Cloud"),this.begin("NODE"),19;case 12:return S.getLogger().trace("Explosion Bang"),this.begin("NODE"),19;case 13:return S.getLogger().trace("Cloud Bang"),this.begin("NODE"),19;case 14:return this.begin("NODE"),19;case 15:return this.begin("NODE"),19;case 16:return this.begin("NODE"),19;case 17:return this.begin("NODE"),19;case 18:return 13;case 19:return 22;case 20:return 11;case 21:this.begin("NSTR2");break;case 22:return"NODE_DESCR";case 23:this.popState();break;case 24:S.getLogger().trace("Starting NSTR"),this.begin("NSTR");break;case 25:return S.getLogger().trace("description:",E.yytext),"NODE_DESCR";case 26:this.popState();break;case 27:return this.popState(),S.getLogger().trace("node end ))"),"NODE_DEND";case 28:return this.popState(),S.getLogger().trace("node end )"),"NODE_DEND";case 29:return this.popState(),S.getLogger().trace("node end ...",E.yytext),"NODE_DEND";case 30:return this.popState(),S.getLogger().trace("node end (("),"NODE_DEND";case 31:return this.popState(),S.getLogger().trace("node end (-"),"NODE_DEND";case 32:return this.popState(),S.getLogger().trace("node end (-"),"NODE_DEND";case 33:return this.popState(),S.getLogger().trace("node end (("),"NODE_DEND";case 34:return this.popState(),S.getLogger().trace("node end (("),"NODE_DEND";case 35:return S.getLogger().trace("Long description:",E.yytext),20;case 36:return S.getLogger().trace("Long description:",E.yytext),20}},rules:[/^(?:\s*%%.*)/i,/^(?:mindmap\b)/i,/^(?::::)/i,/^(?:.+)/i,/^(?:\n)/i,/^(?:::icon\()/i,/^(?:[\s]+[\n])/i,/^(?:[\n]+)/i,/^(?:[^\)]+)/i,/^(?:\))/i,/^(?:-\))/i,/^(?:\(-)/i,/^(?:\)\))/i,/^(?:\))/i,/^(?:\(\()/i,/^(?:\{\{)/i,/^(?:\()/i,/^(?:\[)/i,/^(?:[\s]+)/i,/^(?:[^\(\[\n\)\{\}]+)/i,/^(?:$)/i,/^(?:["][`])/i,/^(?:[^`"]+)/i,/^(?:[`]["])/i,/^(?:["])/i,/^(?:[^"]+)/i,/^(?:["])/i,/^(?:[\)]\))/i,/^(?:[\)])/i,/^(?:[\]])/i,/^(?:\}\})/i,/^(?:\(-)/i,/^(?:-\))/i,/^(?:\(\()/i,/^(?:\()/i,/^(?:[^\)\]\(\}]+)/i,/^(?:.+(?!\(\())/i],conditions:{CLASS:{rules:[3,4],inclusive:!1},ICON:{rules:[8,9],inclusive:!1},NSTR2:{rules:[22,23],inclusive:!1},NSTR:{rules:[25,26],inclusive:!1},NODE:{rules:[21,24,27,28,29,30,31,32,33,34,35,36],inclusive:!1},INITIAL:{rules:[0,1,2,5,6,7,10,11,12,13,14,15,16,17,18,19,20],inclusive:!0}}};return T}();y.lexer=b;function m(){this.yy={}}return m.prototype=y,y.Parser=m,new m}();fi.parser=fi;const Kp=fi;let Nt=[],_u=0,Fi={};const Zp=()=>{Nt=[],_u=0,Fi={}},Qp=function(t){for(let e=Nt.length-1;e>=0;e--)if(Nt[e].level<t)return Nt[e];return null},Jp=()=>Nt.length>0?Nt[0]:null,jp=(t,e,r,a)=>{var n,i;Er.info("addNode",t,e,r,a);const s=ci();let o=((n=s.mindmap)==null?void 0:n.padding)??ja.mindmap.padding;switch(a){case _e.ROUNDED_RECT:case _e.RECT:case _e.HEXAGON:o*=2}const u={id:_u++,nodeId:en(e,s),level:t,descr:en(r,s),type:a,children:[],width:((i=s.mindmap)==null?void 0:i.maxNodeWidth)??ja.mindmap.maxNodeWidth,padding:o},l=Qp(t);if(l)l.children.push(u),Nt.push(u);else if(Nt.length===0)Nt.push(u);else throw new Error('There can be only one root. No parent could be found for ("'+u.descr+'")')},_e={DEFAULT:0,NO_BORDER:0,ROUNDED_RECT:1,RECT:2,CIRCLE:3,CLOUD:4,BANG:5,HEXAGON:6},ey=(t,e)=>{switch(Er.debug("In get type",t,e),t){case"[":return _e.RECT;case"(":return e===")"?_e.ROUNDED_RECT:_e.CLOUD;case"((":return _e.CIRCLE;case")":return _e.CLOUD;case"))":return _e.BANG;case"{{":return _e.HEXAGON;default:return _e.DEFAULT}},ty=(t,e)=>{Fi[t]=e},ry=t=>{if(!t)return;const e=ci(),r=Nt[Nt.length-1];t.icon&&(r.icon=en(t.icon,e)),t.class&&(r.class=en(t.class,e))},ay=t=>{switch(t){case _e.DEFAULT:return"no-border";case _e.RECT:return"rect";case _e.ROUNDED_RECT:return"rounded-rect";case _e.CIRCLE:return"circle";case _e.CLOUD:return"cloud";case _e.BANG:return"bang";case _e.HEXAGON:return"hexgon";default:return"no-border"}},ny=()=>Er,iy=t=>Fi[t],sy={clear:Zp,addNode:jp,getMindmap:Jp,nodeType:_e,getType:ey,setElementForId:ty,decorateNode:ry,type2Str:ay,getLogger:ny,getElementById:iy},oy=sy,uy=12,ly=function(t,e,r,a){e.append("path").attr("id","node-"+r.id).attr("class","node-bkg node-"+t.type2Str(r.type)).attr("d",`M0 ${r.height-5} v${-r.height+2*5} q0,-5 5,-5 h${r.width-2*5} q5,0 5,5 v${r.height-5} H0 Z`),e.append("line").attr("class","node-line-"+a).attr("x1",0).attr("y1",r.height).attr("x2",r.width).attr("y2",r.height)},fy=function(t,e,r){e.append("rect").attr("id","node-"+r.id).attr("class","node-bkg node-"+t.type2Str(r.type)).attr("height",r.height).attr("width",r.width)},hy=function(t,e,r){const a=r.width,n=r.height,i=.15*a,s=.25*a,o=.35*a,u=.2*a;e.append("path").attr("id","node-"+r.id).attr("class","node-bkg node-"+t.type2Str(r.type)).attr("d",`M0 0 a${i},${i} 0 0,1 ${a*.25},${-1*a*.1}
      a${o},${o} 1 0,1 ${a*.4},${-1*a*.1}
      a${s},${s} 1 0,1 ${a*.35},${1*a*.2}

      a${i},${i} 1 0,1 ${a*.15},${1*n*.35}
      a${u},${u} 1 0,1 ${-1*a*.15},${1*n*.65}

      a${s},${i} 1 0,1 ${-1*a*.25},${a*.15}
      a${o},${o} 1 0,1 ${-1*a*.5},0
      a${i},${i} 1 0,1 ${-1*a*.25},${-1*a*.15}

      a${i},${i} 1 0,1 ${-1*a*.1},${-1*n*.35}
      a${u},${u} 1 0,1 ${a*.1},${-1*n*.65}

    H0 V0 Z`)},cy=function(t,e,r){const a=r.width,n=r.height,i=.15*a;e.append("path").attr("id","node-"+r.id).attr("class","node-bkg node-"+t.type2Str(r.type)).attr("d",`M0 0 a${i},${i} 1 0,0 ${a*.25},${-1*n*.1}
      a${i},${i} 1 0,0 ${a*.25},0
      a${i},${i} 1 0,0 ${a*.25},0
      a${i},${i} 1 0,0 ${a*.25},${1*n*.1}

      a${i},${i} 1 0,0 ${a*.15},${1*n*.33}
      a${i*.8},${i*.8} 1 0,0 0,${1*n*.34}
      a${i},${i} 1 0,0 ${-1*a*.15},${1*n*.33}

      a${i},${i} 1 0,0 ${-1*a*.25},${n*.15}
      a${i},${i} 1 0,0 ${-1*a*.25},0
      a${i},${i} 1 0,0 ${-1*a*.25},0
      a${i},${i} 1 0,0 ${-1*a*.25},${-1*n*.15}

      a${i},${i} 1 0,0 ${-1*a*.1},${-1*n*.33}
      a${i*.8},${i*.8} 1 0,0 0,${-1*n*.34}
      a${i},${i} 1 0,0 ${a*.1},${-1*n*.33}

    H0 V0 Z`)},vy=function(t,e,r){e.append("circle").attr("id","node-"+r.id).attr("class","node-bkg node-"+t.type2Str(r.type)).attr("r",r.width/2)};function dy(t,e,r,a,n){return t.insert("polygon",":first-child").attr("points",a.map(function(i){return i.x+","+i.y}).join(" ")).attr("transform","translate("+(n.width-e)/2+", "+r+")")}const gy=function(t,e,r){const a=r.height,i=a/4,s=r.width-r.padding+2*i,o=[{x:i,y:0},{x:s-i,y:0},{x:s,y:-a/2},{x:s-i,y:-a},{x:i,y:-a},{x:0,y:-a/2}];dy(e,s,a,o,r)},py=function(t,e,r){e.append("rect").attr("id","node-"+r.id).attr("class","node-bkg node-"+t.type2Str(r.type)).attr("height",r.height).attr("rx",r.padding).attr("ry",r.padding).attr("width",r.width)},yy=function(t,e,r,a,n){const i=n.htmlLabels,s=a%(uy-1),o=e.append("g");r.section=s;let u="section-"+s;s<0&&(u+=" section-root"),o.attr("class",(r.class?r.class+" ":"")+"mindmap-node "+u);const l=o.append("g"),f=o.append("g"),h=r.descr.replace(/(<br\/*>)/g,`
`);fl(f,h,{useHtmlLabels:i,width:r.width,classes:"mindmap-node-label"}),i||f.attr("dy","1em").attr("alignment-baseline","middle").attr("dominant-baseline","middle").attr("text-anchor","middle");const d=f.node().getBBox(),[c]=ll(n.fontSize);if(r.height=d.height+c*1.1*.5+r.padding,r.width=d.width+2*r.padding,r.icon)if(r.type===t.nodeType.CIRCLE)r.height+=50,r.width+=50,o.append("foreignObject").attr("height","50px").attr("width",r.width).attr("style","text-align: center;").append("div").attr("class","icon-container").append("i").attr("class","node-icon-"+s+" "+r.icon),f.attr("transform","translate("+r.width/2+", "+(r.height/2-1.5*r.padding)+")");else{r.width+=50;const v=r.height;r.height=Math.max(v,60);const p=Math.abs(r.height-v);o.append("foreignObject").attr("width","60px").attr("height",r.height).attr("style","text-align: center;margin-top:"+p/2+"px;").append("div").attr("class","icon-container").append("i").attr("class","node-icon-"+s+" "+r.icon),f.attr("transform","translate("+(25+r.width/2)+", "+(p/2+r.padding/2)+")")}else if(i){const v=(r.width-d.width)/2,p=(r.height-d.height)/2;f.attr("transform","translate("+v+", "+p+")")}else{const v=r.width/2,p=r.padding/2;f.attr("transform","translate("+v+", "+p+")")}switch(r.type){case t.nodeType.DEFAULT:ly(t,l,r,s);break;case t.nodeType.ROUNDED_RECT:py(t,l,r);break;case t.nodeType.RECT:fy(t,l,r);break;case t.nodeType.CIRCLE:l.attr("transform","translate("+r.width/2+", "+ +r.height/2+")"),vy(t,l,r);break;case t.nodeType.CLOUD:hy(t,l,r);break;case t.nodeType.BANG:cy(t,l,r);break;case t.nodeType.HEXAGON:gy(t,l,r);break}return t.setElementForId(r.id,o),r.height},my=function(t,e){const r=t.getElementById(e.id),a=e.x||0,n=e.y||0;r.attr("transform","translate("+a+","+n+")")};nr.use(Wp);function Hu(t,e,r,a,n){yy(t,e,r,a,n),r.children&&r.children.forEach((i,s)=>{Hu(t,e,i,a<0?s:a,n)})}function by(t,e){e.edges().map((r,a)=>{const n=r.data();if(r[0]._private.bodyBounds){const i=r[0]._private.rscratch;Er.trace("Edge: ",a,n),t.insert("path").attr("d",`M ${i.startX},${i.startY} L ${i.midX},${i.midY} L${i.endX},${i.endY} `).attr("class","edge section-edge-"+n.section+" edge-depth-"+n.depth)}})}function Xu(t,e,r,a){e.add({group:"nodes",data:{id:t.id.toString(),labelText:t.descr,height:t.height,width:t.width,level:a,nodeId:t.id,padding:t.padding,type:t.type},position:{x:t.x,y:t.y}}),t.children&&t.children.forEach(n=>{Xu(n,e,r,a+1),e.add({group:"edges",data:{id:`${t.id}_${n.id}`,source:t.id,target:n.id,depth:a,section:n.section}})})}function Ey(t,e){return new Promise(r=>{const a=il("body").append("div").attr("id","cy").attr("style","display:none"),n=nr({container:document.getElementById("cy"),style:[{selector:"edge",style:{"curve-style":"bezier"}}]});a.remove(),Xu(t,n,e,0),n.nodes().forEach(function(i){i.layoutDimensions=()=>{const s=i.data();return{w:s.width,h:s.height}}}),n.layout({name:"cose-bilkent",quality:"proof",styleEnabled:!1,animate:!1}).run(),n.ready(i=>{Er.info("Ready",i),r(n)})})}function wy(t,e){e.nodes().map((r,a)=>{const n=r.data();n.x=r.position().x,n.y=r.position().y,my(t,n);const i=t.getElementById(n.nodeId);Er.info("Id:",a,"Position: (",r.position().x,", ",r.position().y,")",n),i.attr("transform",`translate(${r.position().x-n.width/2}, ${r.position().y-n.height/2})`),i.attr("attr",`apa-${a})`)})}const xy=async(t,e,r,a)=>{var n,i;Er.debug(`Rendering mindmap diagram
`+t);const s=a.db,o=s.getMindmap();if(!o)return;const u=ci();u.htmlLabels=!1;const l=al(e),f=l.append("g");f.attr("class","mindmap-edges");const h=l.append("g");h.attr("class","mindmap-nodes"),Hu(s,h,o,-1,u);const d=await Ey(o,u);by(f,d),wy(s,d),nl(void 0,l,((n=u.mindmap)==null?void 0:n.padding)??ja.mindmap.padding,((i=u.mindmap)==null?void 0:i.useMaxWidth)??ja.mindmap.useMaxWidth)},Ty={draw:xy},Cy=t=>{let e="";for(let r=0;r<t.THEME_COLOR_LIMIT;r++)t["lineColor"+r]=t["lineColor"+r]||t["cScaleInv"+r],sl(t["lineColor"+r])?t["lineColor"+r]=ol(t["lineColor"+r],20):t["lineColor"+r]=ul(t["lineColor"+r],20);for(let r=0;r<t.THEME_COLOR_LIMIT;r++){const a=""+(17-3*r);e+=`
    .section-${r-1} rect, .section-${r-1} path, .section-${r-1} circle, .section-${r-1} polygon, .section-${r-1} path  {
      fill: ${t["cScale"+r]};
    }
    .section-${r-1} text {
     fill: ${t["cScaleLabel"+r]};
    }
    .node-icon-${r-1} {
      font-size: 40px;
      color: ${t["cScaleLabel"+r]};
    }
    .section-edge-${r-1}{
      stroke: ${t["cScale"+r]};
    }
    .edge-depth-${r-1}{
      stroke-width: ${a};
    }
    .section-${r-1} line {
      stroke: ${t["cScaleInv"+r]} ;
      stroke-width: 3;
    }

    .disabled, .disabled circle, .disabled text {
      fill: lightgray;
    }
    .disabled text {
      fill: #efefef;
    }
    `}return e},Dy=t=>`
  .edge {
    stroke-width: 3;
  }
  ${Cy(t)}
  .section-root rect, .section-root path, .section-root circle, .section-root polygon  {
    fill: ${t.git0};
  }
  .section-root text {
    fill: ${t.gitBranchLabel0};
  }
  .icon-container {
    height:100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .edge {
    fill: none;
  }
  .mindmap-node-label {
    dy: 1em;
    alignment-baseline: middle;
    text-anchor: middle;
    dominant-baseline: middle;
    text-align: center;
  }
`,Sy=Dy,Oy={db:oy,renderer:Ty,parser:Kp,styles:Sy};export{Oy as diagram};
