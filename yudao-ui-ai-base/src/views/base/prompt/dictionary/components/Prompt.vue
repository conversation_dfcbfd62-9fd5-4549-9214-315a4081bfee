<template>
    <div>
        <h4 v-if="type !== '字典词'" class="ms-title mt-2">{{ type }}prompt实例</h4>
        <div v-if="!isDetail && !isAdd" class="mb-4">
            <Button @click="openPage('add')" type="primary" class="mr-2">新建</Button>
            <Button @click="handleBind(model.prompt_name)" type="primary" class="mr-2">+关联</Button>
            <Button @click="openPage('tutorial')" type="primary">教程</Button>
        </div>
        <!-- <Row v-if="isAdd" class="text-xs contentSubContainer">
            字典词新增成功后，开放prompt功能！
            <br />
            （新增完成后，通过“编辑”功能再次进入）
        </Row>
        <div v-else> -->
            <Row style="margin:5px 0;" class="contentSubContainer">
                <span class="text-sm">关联实例：{{ showData?.name || '' }}</span>
                <template v-if="showData.name && !isDetail">
                    <span @click="openPage('edit')" class="ms-link ml-4 mt-0.5">编排/调试</span>
                    <span @click="handleOk('unbind')" class="ms-link ml-4 mt-0.5">取消关联</span>
                    <span @click="getPromptDetail(model.dify_app_uuid)" class="ms-link ml-4 mt-0.5">刷新</span>
                </template>
            </Row>
            <!-- <div v-loading="loading" class="contentSubContainer"> -->
                <!-- <p class=" ms-title">AI模型</p> -->
                <!-- <Tag v-if="showData.model_config.model" class="mt-1">{{ showData..model.name }}</Tag>
                <Empty v-else :imageStyle="imgStyle"></Empty> -->

                <!-- <p class="ms-title">{{ type }}提示词</p>
                <span v-if="showData.model_config.pre_prompt" class="text-xs  w-s"
                    v-html="showData.model_config.pre_prompt"></span>
                <Empty v-else :imageStyle="imgStyle"></Empty>

                <p class="ms-title">变量</p>
                <Table :columns="promptTableHeader" :data-source="tableData" :pagination="false">
                </Table>

                <p class="ms-title">AI引导对话</p>
                <p v-if="showData.model_config.opening_statement" class="text-xs w-s"
                    v-html="showData.model_config.opening_statement"></p>
                <Empty v-else :imageStyle="imgStyle"></Empty>
                <Tag v-if="showData.model_config.suggested_questions"
                    v-for="item in showData.model_config.suggested_questions" class="mt-1">{{
                    item }}</Tag> -->
            <!-- </div> -->
        <!-- </div> -->
    </div>
    <Modal :open="showModal" :title="`关联${type}prompt实例`" :confirm-loading="confirmLoading" @cancel="handleCancel"
        style="top: 16vh;" :maskClosable="false">
        <Spin :spinning="promptLoading">
            <InputSearch v-model:value="searchValue" :placeholder="`请输入${type}名称`"
                    style="width: 100%; margin-bottom: 10px;height:34px;" size="middle" allowClear @search="getPrompts(searchValue)" />
            <Row v-if="!dataList.length && !searchProjectTag">没有找到“{{ type }}”相关的Dify应用，请 <span class="ms-link" @click="openPage('add')">前往创建</span></Row>
            <template v-else>
                <!-- <Select v-model:value="searchProjectTag" style="width: 200px" placeholder="全部项目" @change="handleChange"
                    allowClear>
                    <template v-for="item in allProjectTags">
                        <SelectOption :value="item.id">{{ item.name }}</SelectOption>
                    </template>
                </Select> -->
                <div class="mt-3 o-y">
                    <RadioGroup v-if="promptList.length" v-model:value="valuePrompt">
                        <template v-for="item in promptList" :key="item.id">
                            <Radio :style="radioStyle" :value="item.id">{{ item.name }}</Radio>
                        </template>
                    </RadioGroup>
                    <Empty v-else/>
                </div>
            </template>
        </Spin>
        <template #footer>
            <div class="operateFooter">
                <Button key="back" @click="handleCancel">关闭</Button>
                <Button type="primary" key="submit" @click="handleOk">确定</Button>
            </div>
        </template>
    </Modal>
</template>
<script lang="ts" setup>
import { ref, reactive, watch, onMounted } from 'vue'
import { Button, Modal, RadioGroup, Radio, Spin, Empty, Table, Tag, Row, InputSearch, Select, SelectOption } from 'ant-design-vue'
import { storeToRefs } from 'pinia'
import { promptTableHeader } from './../dictionary.data'
import { useDifyStore } from '@/store/modules/dify'
import { difyPrompt, difyPromptDetail } from '@/api/toolLogin'
import { useMutation } from "@vue/apollo-composable"
import { bindDictPromptGql } from '@/api/dictionary'
import { bindProjectDifyGql } from '@/api/project'
import { bindAssistantDifyGql } from '@/api/aiAssistants'
import { useRouter } from 'vue-router'
const router = useRouter()

const props = defineProps({
    model: {
        type: Object,
        default: () => { }
    },
    gql: {
        type: Object,
        default: () => { }
    },
    type: {
        type: String,
        default: ''
    },
    isDetail: {
        type: Boolean,
        default: false
    },
    isAdd: {
        type: Boolean,
        default: false
    },
});
const searchProjectTag = ref(null);
const imgStyle = { height: '35px', margin: '0 auto' }
let tableData = ref([])
const difyStore = useDifyStore()
let { dictTypeTags, getProjectTag, assistantTags, allProjectTags } = storeToRefs(difyStore)
let showModal = ref(false)
let confirmLoading = ref(false)
const valuePrompt = ref<String>(null);
const radioStyle = reactive({
    display: 'flex',
    height: '30px',
    lineHeight: '30px',
});
let dataPrompt = reactive({
    id: '',
    dify_app_uuid: ''
})
// 各类型更新接口
const updateObj = {
    字典词: bindDictPromptGql,
    项目: bindProjectDifyGql,
    AI助理: bindAssistantDifyGql
}
const {
    mutate: updatePrompt,
    onDone: oDoneUpdate,
} = useMutation(updateObj[props.type], {
    variables: dataPrompt
})

const handleOk = (params) => {
    Object.assign(dataPrompt, {
        dify_app_uuid: params === "unbind" ? '' : valuePrompt.value,
        id: props.model.id
    })
    updatePrompt()
}
oDoneUpdate(({ data }) => {
    if (data && data.data && data.data.dify_app_uuid) {
        getPromptDetail(data.data.dify_app_uuid)
        handleCancel()
    } else {
        Object.assign(showData, {
            name: '',
            model_config: {}
        })
        tableData.value = []
    }
})
const handleCancel = () => {
    showModal.value = false
    valuePrompt.value = ''
}
let promptList = ref([])
let dataList = ref([])
let promptLoading = ref(false)
const getPrompts = async (name) => {
    const obj = {
        字典词: dictTypeTags,
        项目: getProjectTag,
        AI助理: assistantTags
    }
    try {
        promptLoading.value = true
        const res = await difyPrompt(obj[props.type]?.value?.id, name)
        promptLoading.value = false
        if (res.data) {
            promptList.value = [...res.data]
            dataList.value = [...res.data]
        }
        promptLoading.value = false
    } catch (error) {
        promptLoading.value = false
        console.log(error)
    }
}
let loading = ref(false)
let showData = reactive({
    name: '',
    model_config: {}
})
const getPromptDetail = async (params) => {
    try {
        tableData.value = []
        loading.value = true
        const res = await difyPromptDetail(params)
        Object.assign(showData, res)
        if (showData.model_config) {
            let { user_input_form } = showData.model_config
            user_input_form.forEach(item => {
                let arr = Object.values(item)
                arr.forEach(val => {
                    val.requiredLabel = val.required ? '是' : '否'
                })
                tableData.value.push(...arr)
            })
        }
        loading.value = false
    } catch (error) {
        loading.value = false
        console.log(error)
    }
}
onMounted(async () => {
    tableData.value = []
    Object.assign(showData, {
        name: '',
        model_config: {}
    })
    if (props.model?.dify_app_uuid) {
        valuePrompt.value = props.model.dify_app_uuid;
        await init(valuePrompt.value);
    }
});
watch(() => props.model, newVal => {
    if (newVal) {
        tableData.value = []
        Object.assign(showData, {
            name: '',
            model_config: {}
        })
        if (newVal?.dify_app_uuid) {
            valuePrompt.value = newVal.dify_app_uuid
            init(valuePrompt.value)
        }
    }
}, {
    deep: true
})
const init = async (params) => {
    try {
        // await getPrompts()
        await getPromptDetail(params)
    } catch (err) {
        console.log(err)
    }
}
const handleBind = (name) => {
  showModal.value = true
  searchValue.value = name
  getPrompts(name)
}

const openPage = params => {
    const originStr = window.location.origin.indexOf('medsci.cn') !== -1 ? 'https://ai-base.medsci.cn' : 'https://ai-base.medon.com.cn'
    if (params === 'add') {
        window.open(`${originStr}/apps`)
    } else if (params === 'edit') {
        window.open(`${originStr}/app/${showData.id}/configuration`)
    } else {
        router.push({
            path: `/tutorial`,
            query: {
                type: props.type,
            }
        })
    }
}
const searchValue = ref('');
// watch(searchValue, value => {
//     promptList.value = dataList.value.filter(item => {
//         if (item.name.indexOf(value) > -1) {
//             return item
//         }
//     })
// });

const handleChange = async params => {
    if(!params){
        promptList.value = [...dataList.value]
    } else {
        await getPrompts()
        promptList.value = dataList.value.filter(item => {
            if (item.tags.map(item => item.id).includes(params)) {
                return item
            }
        })
    }
}
</script>
<style scoped>
.promptContent {
    height: 60vh;
    overflow-y: auto;
}

.w-s {
    white-space: pre-wrap;
}

:deep(.ant-input-group-addon .ant-btn-icon-only) {
    height: 29px !important;
}
.o-y{
    height: 350px;
    overflow-y: auto;
}
</style>
