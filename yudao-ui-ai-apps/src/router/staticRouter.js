// import ModuleList from "@/common/moduleList";
const routes = [
  {
    name: "layout",
    path: "/:lang?",
    meta: { title: "layout" },
    component: () => import("@/layout/Layout.vue"),
    // redirect: "/:lang?/index",
    children: [
      {
        name: "tool",
        path: "tool/:appUuid",
        meta: { title: "market.researchTool" },
        component: () => import("@/views/aiBase/index.vue"),
        alias: '/:lang?/tool/:uuid?/:appUuid' // 兼容旧路由
      },
    ],
  },
  {
    name: "chat",
    path: "/:lang?/chat/:appUuid",
    meta: { title: "market.medicalChat" },
    component: () => import("@/views/aiChat/index.vue"),
    alias: "/:lang?/chat/:uuid?/:appUuid",
  },
  {
    name: "payType",
    path: "/payType",
    meta: { title: "market.medicalChat" },
    component: () => import("@/views/payType/index.vue"),
  },
  {
    name: "index",
    path: "/:lang?/",
    meta: { title: "faq.xAI" },
    component: () => import("@/views/home/<USER>"),
  },
  {
    name: "payLink",
    path: "/payLink/:payInfo",
    meta: { },
    component: () => import("@/views/payLink/index.vue"),
  },
  {
    name: "login",
    path: "/login/:socialType?",
    meta: { title: "market.login" },
    component: () => import("@/views/login/index.vue"),
  },
  {
    name: "sign-up",
    path: "/sign-up/:socialType?",
    meta: { title: "market.sign-up" },
    component: () => import("@/views/sign-up/index.vue"),
  },
  {
    name: "privacy-policy",
    path: "/privacy-policy",
    meta: { title: "market.privacyPolicy" },
    component: () => import("@/views/aiBase/privacy-policy.vue"),
  },
  {
    name: "destroy",
    path: "/destroy",
    component: () => import("@/views/aiBase/destroy.vue"),
  },
  {
    path: "/:pathMatch(.*)*",
    component: () => import("@/views/404/index.vue"),
  },
];

export default routes;
