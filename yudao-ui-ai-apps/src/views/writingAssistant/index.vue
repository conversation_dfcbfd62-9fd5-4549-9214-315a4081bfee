<template>
  <div class="h-full flex overflow-auto">
    <!-- 左侧导航 -->
    <div class="flex flex-col w-[160px] mr-4">
      <div
        class="nav-item flex items-center font-bold text-[#2d3858] cursor-pointer border border-solid border-top-0 border-left-0 border-right-0 border-gray-200 h-[46px]"
        :class="navActive == item.value ? 'bg-[#7e90b8]' : 'bg-[#f8f8f8]'"
        v-for="(item, index) in leftNav"
        :key="index"
        @click="onNavClick(item)"
      >
        <div class="w-[4px] h-full" v-if="navActive != item.value"></div>
        <div
          class="pl-10 h-full flex items-center"
          :class="
            navActive == item.value
              ? 'border border-solid border-left-4 border-[#2b3858] border-top-0 border-bottom-0 border-right-0'
              : ''
          "
        >
          {{ item.label }}
        </div>
      </div>
    </div>
    <div class="flex-1">
      <!-- 查询 -->
      <div class="flex items-center bg-[#f7f8fa] p-2 rounded-md h-[52px] mb-2">
        <el-input
          class="h-full !text-[24px]"
          v-model="queryTitle"
          placeholder="这里输入关键词或短句，中英文均可"
          clearable
        />
        <el-button type="primary" @click="getResult(1)">查 询</el-button>
      </div>
      <!-- 编辑器 -->
      <Editor
        class="h-[380px] mb-4"
        v-model="htmlvalue"
        @clear="onClear"
        :key="editorKey"
      />
      <!-- 工具栏 -->
      <div class="flex items-center my-8 overflow-hidden">
        <!-- 翻译开关 -->
        <div class="flex items-center mr-4">
          <div class="mr-2" :class="translate ? 'text-[#409eff]' : ''"
            >翻译</div
          >
          <el-switch v-model="translate" />
        </div>
        <!-- 影响因子 -->
        <div class="flex items-center mr-2">
          <span class="mr-2">影响因子：</span>
          <el-checkbox-group v-model="checkList">
            <el-checkbox label="3"> {{ "<3分" }} </el-checkbox>
            <el-checkbox label="5">3-10分</el-checkbox>
            <el-checkbox label="10">{{ ">10分" }}</el-checkbox>
          </el-checkbox-group>
        </div>
        <!-- 年份 -->
        <div class="flex items-center">
          <div class="w-[60px]">年份范围</div>
          <div class="relative flex-1">
            <el-slider
              class="ml-6 !w-[132px]"
              v-model="yearRange"
              range
              :max="2023"
              :min="1990"
              @change="onYearChange"
            />
            <span class="text-[#419eff] absolute font-bold -top-1.5 left-[30%]">{{
              `${yearRange[0]}-${yearRange[1]}`
            }}</span>
          </div>
        </div>
      </div>
      <!-- 查询结果 -->
      <div>
        <div
          class="flex mb-8"
          v-for="(item, index) in result.content"
          :key="index"
        >
          <div class="mr-2">{{ index + 1 }}.</div>
          <div>
            <div
              class="text-[18px] text-[#5e5e5e] cursor-pointer html-value"
              v-html="item.text"
              @click="handleClick(index + 1, item.text)"
            ></div>
            <!-- 翻译 -->
            <div
              class="text-[16px] text-[#0a76f5] mt-4"
              v-html="item.translateText"
              v-if="translate"
            ></div>
          </div>
        </div>
      </div>
      <!-- 分页 -->
      <Pagination
        v-if="result && result.eleTotal"
        class="pb-10"
        :total="result.eleTotal"
        v-model:page="paging.pageNo"
        v-model:limit="paging.pageSize"
        @pagination="getResult"
      />
    </div>
  </div>
</template>

<script setup>
import { aiSearchTitle } from "@/api/index";
import { shuffleArray } from "@/utils/index"
const queryTitle = ref("类风湿关节炎铁死亡特征基因图的构建");
const htmlvalue = ref("");
const editorKey = ref(1);
const translate = ref(true);
const checkList = ref([]);
const yearRange = ref([1990, 2023]);
const result = ref({});
const paging = reactive({
  pageNo: 1,
  pageSize: 20,
});
// 左侧导航
const leftNav = ref([
  { label: 'Title', value: 'title' },
  { label: 'Keywords', value: 'keyword' },
  { label: 'Abstract', value: 'abstract' },
  { label: 'Introduction', value: 'introduction' },
  { label: 'Methods', value: 'methods' },
  { label: 'Results', value: 'results' },
  { label: 'Discussion', value: 'discussion' },
  { label: 'Acknowledge', value: 'acknowleg' },
  { label: '全库检索(CNS)', value: 'all' }
]);
const navActive = ref("title");
// 获取数据
const getResult = (type) => {
  if (!queryTitle.value) return ElMessage.warning("请输入关键词或短句")
  // 查询从第一页开始
  if (type == 1) paging.pageNo = 1;
  let mulSearchConditions = checkList.value.map((item) => {
    return {
      field: "effect",
      opt: 2,
      vals: [item],
      val: "",
      synonymsWordVos: [],
    };
  });
  aiSearchTitle(navActive.value, {
    key: queryTitle.value,
    page: paging.pageNo - 1,
    size: paging.pageSize,
    allMySentence: 0,
    allMyGroupSentence: 0,
    synonymsHistory: 0,
    mulSearchConditions: mulSearchConditions,
    beginYear: yearRange.value[0],
    endYear: yearRange.value[1],
    mySentenceRange: 0,
    sorts: [],
  }).then((res) => {
    if (!res || !res.data) return
    result.value = res.data
    result.value.content = shuffleArray(result.value.content)
  });
};
// 点击粘贴
const handleClick = (index, text) => {
  let str = `${index}.${text}`.replace(/\n/g, "");
  htmlvalue.value += `<p>${str}</p>`;
};
// 编辑器清空时
const onClear = () => {
  editorKey.value++;
};
// 搜索类型变化时
const onNavClick = (item) => {
  navActive.value = item.value;
  queryTitle.value = "";
  result.value = {};
  checkList.value = [];
};
// 年份变化时
const onYearChange = () => {
  getResult();
};

onMounted(() => {
  getResult();
});
</script>

<style lang="scss" scoped>
:deep(.el-input__wrapper) {
  box-shadow: 0 0 0 0px var(--el-input-border-color, var(--el-border-color))
    inset;
  background: #f7f8fa;
  cursor: default;
  .el-input__inner {
    cursor: default !important;
    color: #000;
    font-weight: 400;
  }
}
.html-value:hover {
  color: rgb(114, 205, 254);
  :deep(em) {
    color: rgb(114, 205, 254) !important;
  }
}
.nav-item:hover {
  box-shadow: 3px 3px 3px rgba(50, 50, 50, 0.2);
}
:deep(.el-checkbox) {
  margin-right: 10px;
}
</style>
