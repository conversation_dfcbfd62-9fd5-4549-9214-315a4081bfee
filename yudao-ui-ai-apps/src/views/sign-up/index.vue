<template>
  <div
    class="bg-background text-foreground antialiased min-h-screen flex items-center justify-center"
  >
    <div class="cl-rootBox cl-signUp-root justify-center">
      <div class="cl-cardBox cl-signUp-start">
        <div class="cl-card cl-signUp-start">
          <div class="cl-header">
            <div>
              <h1 class="cl-headerTitle">{{ $t(`tool.create_account`) }}</h1>
              <p class="cl-headerSubtitle">
                {{ $t(`tool.registration_greeting`) }}
              </p>
            </div>
          </div>
          <div class="cl-main">
            <div class="cl-socialButtonsRoot">
              <div class="cl-socialButtons">
                <button
                  class="cl-socialButtonsBlockButton cl-button cl-socialButtonsBlockButton__google"
                >
                  <span class="cl-socialButtonsBlockButton-d">
                    <span>
                      <img
                        src="https://img.medsci.cn/202412/cc7c7687b4804460a85d46c629132724-vcHTUHMGIylQ.png"
                        class="cl-socialButtonsProviderIcon cl-providerIcon cl-socialButtonsProviderIcon__google"
                        alt="Sign in with Google"
                      />
                    </span>
                    <span
                      class="cl-socialButtonsBlockButtonText"
                      @click="handleLogin(35)"
                      >{{ $t(`tool.continue_with_google`) }}</span
                    >
                  </span>
                </button>
              </div>
              <div class="cl-socialButtons">
                <button
                  class="cl-socialButtonsBlockButton cl-button cl-socialButtonsBlockButton__google"
                >
                  <span class="cl-socialButtonsBlockButton-d">
                    <span>
                      <img
                        src="https://img.medsci.cn/202412/f9bf95f463c04d3e801aa6e97ef3d4b8-OSsmrMWR677i.png"
                        class="cl-socialButtonsProviderIcon cl-providerIcon cl-socialButtonsProviderIcon__google"
                        alt="Sign in with Google"
                      />
                    </span>
                    <span
                      class="cl-socialButtonsBlockButtonText"
                      @click="handleLogin(36)"
                      >{{ $t(`tool.continue_with_facebook`) }}</span
                    >
                  </span>
                </button>
              </div>
            </div>
            <div class="cl-dividerRow">
              <div class="cl-dividerLine"></div>
              <p class="cl-dividerText">or</p>
              <div class="cl-dividerLine"></div>
            </div>
            <div class="cl-socialButtonsRoot">
              <el-form
                ref="ruleFormRef"
                style="max-width: 600px"
                :model="ruleForm"
                :rules="rules"
                label-width="auto"
                class="demo-ruleForm"
                label-position="left"
                :size="formSize"
                status-icon
              >
                <el-form-item :label="$t('tool.username')" prop="userName">
                  <el-input v-model="ruleForm.userName" />
                </el-form-item>
                <el-form-item :label="$t('tool.email')" prop="email">
                  <el-input v-model="ruleForm.email" />
                </el-form-item>
                <el-form-item :label="$t('tool.verification_code')" prop="emailCode">
                  <el-input v-model="ruleForm.emailCode">
                    <template #append
                      ><el-button
                        @click="send"
                        :disabled="isDisabled"
                        type="primary"
                        >{{ btnText }}</el-button
                      ></template
                    >
                  </el-input>
                </el-form-item>

                <el-form-item :label="$t('tool.password')" prop="password" style="padding-bottom:20px">
                  <el-input v-model="ruleForm.password" show-password="true" />
                </el-form-item>
                <el-form-item>
                  <div class="cl-internal-1pnppin">
                    <div id="clerk-captcha" class="cl-internal-3s7k9k"></div>
                    <div class="cl-internal-742eeh">
                      <el-button
                        class="cl-formButtonPrimary cl-button 🔒️ cl-internal-ttumny"
                        @click="registers(ruleFormRef)"
                      >
                        <span class="cl-internal-2iusy0"
                          >{{$t('tool.continue')}}<svg
                            class="cl-buttonArrowIcon 🔒️ cl-internal-1c4ikgf"
                          >
                            <path
                              fill="currentColor"
                              stroke="currentColor"
                              stroke-linecap="round"
                              stroke-linejoin="round"
                              stroke-width="1.5"
                              d="m7.25 5-3.5-2.25v4.5L7.25 5Z"
                            /></svg
                        ></span>
                      </el-button>
                    </div>
                  </div>
                </el-form-item>
              </el-form>
            </div>
          </div>
        </div>
        <div class="cl-footer 🔒️ cl-internal-4x6jej">
          <div
            class="cl-footerAction cl-footerAction__signUp cl-internal-1rpdi70"
          >
            <span
              class="cl-footerActionText cl-internal-kyvqj0"
              data-localization-key="signUp.start.actionText"
              >{{$t("tool.alreadyhaveanaccount")}}</span
            ><el-link :href="isUp?'/apps/login':'/login'" class="cl-footerActionLink"
              >{{$t('tool.signIn')}}</el-link
            >
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import {
  socialAuthRedirect,
  socialLogin,
  sendEmailCode,
  register,
} from "@/api/base";
import cookie from "js-cookie";
import { ElMessage } from "element-plus";
import { reactive, ref } from "vue";
import { useI18n } from 'vue-i18n';
const { t } = useI18n();
const route = useRoute();
const router = useRouter();
const socialType = route.params.socialType;
const code = route.query.authCode;
const state = route.query.authState;
const ruleFormRef = ref();
const ruleForm = ref({
  email: "",
  password: "",
  emailCode: "",
  userName: "",
});
const isUp = ref(location?.origin.includes('medon.com.cn')||location?.origin.includes('medsci.cn'))
const rules = reactive({
  userName: [{ required: true, message:t('tool.username_cannot_be_empty'), trigger: "blur" }],
  password: [
    { required: true, message: t('tool.password_cannot_be_empty'), trigger: "blur" },
    { min: 8, max: 20, message: t('tool.pwdLength'), trigger: "blur" },
    {
      pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[\W_])[a-zA-Z\d\W_]{8,20}$/,
      message:t('tool.password_includes_uppercase_lowercase_numbers_and_symbols'),
      trigger: "blur",
    },
  ],
  emailCode: [
    { required: true, message: t('tool.verification_code_cannot_be_empty'), trigger: "blur" },
    {
      min: 6,
      max: 6,
      message: t('tool.verification_code_must_be_6_digits'),
      trigger: "blur",
    },
  ],
  email: [
    { required: true, message: t('tool.email_address_cannot_be_empty'), trigger: "blur" },
    { type: 'email', message: t('tool.please_enter_a_valid_email_address'), trigger: 'blur' }
  ],
});

const isDisabled = ref(false);
const countdown = ref(60);
const btnText = ref(t('tool.send_verification_code'));
const userId = cookie.get("userInfo")
  ? JSON.parse(cookie.get("userInfo"))?.userId
  : "";

const handleLogin = (socialType) => {
  socialAuthRedirect(socialType).then((res) => {
    window.location.href = res;
  });
};
const startCountdown = () => {
  isDisabled.value = true;
  btnText.value = `${countdown.value}${t('tool.retry_after_seconds')}`;
  let timer = setInterval(() => {
    countdown.value -= 1;
    btnText.value = `${countdown.value}${t('tool.retry_after_seconds')}`;
    if (countdown.value <= 0) {
      clearInterval(timer);
      countdown.value = 60;
      btnText.value = t('tool.send_verification_code');
      isDisabled.value = false;
    }
  }, 1000);
};
// 发送验证码
const send = () => {
  if(!ruleForm.value.email){
     ElMessage.error(t('tool.email_does_not_exist'));
    return;
  }
  let params = {
    email: ruleForm.value.email,
    type:"RegisterCode"
  };
  sendEmailCode(params).then((res) => {
    if(res){
      startCountdown();
    }
  });
};
// 注册
const registers = async (formEl) => {
  if (!formEl) return;
  await formEl.validate((valid, fields) => {
    if (valid) {
      register(ruleForm.value).then((res) => {
        if(res){
          ElMessage({
                type: 'success',
                message: t('tool.registersuccess')
            })
          setTimeout(() => {
            location.href = isUp.value ? "/apps/login":'login'
          }, 1000);
        }
      });
    } else {
      console.log("error submit!", fields);
    }
  });
};

onMounted(() => {
  if (!userId) {
    if (socialType && code && state) {
      console.log("登录中...");
      socialLogin(socialType, code, state).then((res) => {
        if (res?.token && res?.htoken) {
          localStorage.setItem("yudaoToken", res.token);
          localStorage.setItem("hasuraToken", res.htoken);
          localStorage.setItem("openid", res.openid);
          localStorage.setItem("socialUserId", res.socialUserId);
          localStorage.setItem("socialType", res.socialType);
          res.userInfo.userId = res.userInfo.openid;
          res.userInfo.plaintextUserId = res.userInfo.socialUserId;
          res.userInfo.token = {
            accessToken: res.userInfo.openid,
            accessTokenExpireTime: 630720000,
            refreshToken: res.userInfo.openid,
          };
          if (location.origin.includes(".medsci.cn")) {
            cookie.set("userInfo", JSON.stringify(res.userInfo), {
              expires: 365,
              domain: ".medsci.cn",
            });
          } else if (location.origin.includes(".medon.com.cn")) {
            cookie.set("userInfo", JSON.stringify(res.userInfo), {
              expires: 365,
              domain: ".medon.com.cn",
            });
          } else {
            cookie.set("userInfo", JSON.stringify(res.userInfo), {
              expires: 365,
            });
          }
          router.push("/");
        } else {
          console.error("登录失败: 未返回 token");
          // ElMessage({
          //   message: "Auth Error",
          //   type: "warning",
          // });
        }
      });
    }
  } else {
    // 回到主页
    router.push("/");
  }
});
</script>
<style lang="scss" scoped>
.justify-center {
  justify-content: center;
}
.el-form-item {
  display: block;
  ::v-deep() {
    .el-form-item__label {
      color: rgb(33, 33, 38);
      font-family: inherit;
      letter-spacing: normal;
      font-weight: 400;
      font-size: 12px;
      line-height: 1;
      height: 20px;
      display: flex;
      -webkit-box-align: center;
      align-items: center;
    }
  }
}

.cl-cardBox {
  box-sizing: border-box;
  display: flex;
  flex-flow: column;
  align-items: stretch;
  justify-content: flex-start;
  isolation: isolate;
  max-width: calc(-40px + 100vw); /* -2.5rem */
  width: auto;
  border-style: solid;
  border-color: rgba(0, 0, 0, 0.07);
  border-radius: 12px; /* 0.75rem */
  color: rgb(33, 33, 38);
  position: relative;
  overflow: hidden;
  border-width: 0px;
  box-shadow: rgba(0, 0, 0, 0.08) 0px 5px 15px 0px,
    rgba(25, 28, 33, 0.2) 0px 15px 35px -5px,
    rgba(0, 0, 0, 0.07) 0px 0px 0px 1px;
}
.cl-card {
  box-sizing: border-box;
  display: flex;
  flex-flow: column;
  align-items: stretch;
  gap: 32px; /* 2rem */
  background-color: white;
  transition-property: background-color, background, border-color, color, fill,
    stroke, opacity, box-shadow, transform;
  transition-duration: 200ms;
  text-align: center;
  z-index: 10;
  border-style: solid;
  border-color: rgba(0, 0, 0, 0.03);
  border-radius: 8px; /* 0.5rem */
  position: relative;
  padding: 32px 40px;
  justify-content: center;
  border-width: 0px;
  box-shadow: rgba(0, 0, 0, 0.08) 0px 0px 32px 0px,
    rgba(25, 28, 33, 0.06) 0px 16px 32px 0px,
    rgba(0, 0, 0, 0.03) 0px 0px 0px 1px;
}
.cl-header {
  box-sizing: border-box;
  display: flex;
  flex-flow: column;
  align-items: stretch;
  justify-content: flex-start;
  gap: 24px; /* 1.5rem */
}
.cl-headerTitle {
  box-sizing: border-box;
  color: rgb(33, 33, 38);
  margin: 0px;
  font-family: inherit;
  letter-spacing: normal;
  font-weight: 700;
  font-size: 18px;
  line-height: 20px; /* 1.41176rem */
}
.cl-headerSubtitle {
  box-sizing: border-box;
  margin: 0px;
  font-size: 14px;
  font-family: inherit;
  letter-spacing: normal;
  font-weight: 400;
  line-height: 22px; /* 1.38462rem */
  color: rgb(116, 118, 134);
  overflow-wrap: break-word;
  text-align: center;
}
.cl-main {
  box-sizing: border-box;
  display: flex;
  flex-flow: column;
  align-items: stretch;
  justify-content: flex-start;
}
.cl-socialButtonsRoot {
  box-sizing: border-box;
  display: flex;
  flex-flow: column;
  align-items: stretch;
  justify-content: flex-start;
  gap: 8px; /* 0.5rem */
}
.cl-socialButtons {
  box-sizing: border-box;
  display: grid;
  align-items: stretch;
  gap: 8px; /* 0.5rem */
  justify-content: center;
  grid-template-columns: repeat(1, 1fr);
  margin-bottom: 5px;
}
.cl-socialButtonsBlockButton:hover {
  background-color: rgba(0, 0, 0, 0.03);
}
.cl-socialButtonsBlockButton {
  padding: 6px 12px; /* 6px 12px */
  border-width: 0px;
  box-shadow: rgba(0, 0, 0, 0.07) 0px 0px 0px 1px,
    rgba(0, 0, 0, 0.08) 0px 2px 3px -1px, rgba(0, 0, 0, 0.02) 0px 1px 0px 0px;
  background: unset;
  display: flex;
  justify-content: center;
  img {
    width: 16px;
    height: auto;
    max-width: 100%;
  }
  .cl-socialButtonsBlockButton-d {
    box-sizing: border-box;
    display: flex;
    flex-flow: row;
    align-items: center;
    justify-content: center;
    gap: 12px; /* 0.75rem */
    width: 70%;
    overflow: hidden;
    span {
      line-height: 1;
    }
  }
  .cl-socialButtonsBlockButtonText {
    box-sizing: border-box;
    width: 70%;
    text-align: left;
    margin: 0px;
    font-size: 12px;
    font-family: inherit;
    letter-spacing: normal;
    font-weight: 500;
    line-height: 22px; /* 1.38462rem */
    color: inherit;
    text-overflow: ellipsis;
    white-space: nowrap;
    color: rgba(0, 0, 0, 0.62);
  }
}
.cl-dividerRow {
  box-sizing: border-box;
  display: flex;
  flex-flow: row;
  align-items: center;
  justify-content: center;
  margin-top: 10px;
  .cl-dividerLine {
    box-sizing: border-box;
    display: flex;
    flex-flow: row;
    align-items: stretch;
    justify-content: flex-start;
    flex: 1 1 0%;
    height: 1px;
    background-color: rgba(0, 0, 0, 0.07);
  }
  .cl-dividerText {
    box-sizing: border-box;
    font-size: 13px; /* 0.8125rem */
    font-family: inherit;
    letter-spacing: normal;
    font-weight: 400;
    line-height: 22px; /* 1.38462rem */
    color: rgb(116, 118, 134);
    margin: 0px 16px; /* 1rem */
  }
}
.cl-footerAction {
  box-sizing: border-box;
  display: flex;
  padding: 16px 32px; /* 1rem 2rem */
  flex-flow: row;
  align-items: center;
  justify-content: center;
  gap: 4px; /* 0.25rem */
  margin: 0px auto;
  .cl-footerActionText {
    box-sizing: border-box;
    margin: 0px;
    font-size: 13px; /* 0.8125rem */
    font-family: inherit;
    letter-spacing: normal;
    font-weight: 400;
    line-height: 22px; /* 1.38462rem */
    color: rgb(116, 118, 134);
  }
  .cl-footerActionLink {
    box-sizing: border-box;
    display: inline-flex;
    align-items: center;
    margin: 0px;
    cursor: pointer;
    text-decoration: none;
    font-family: inherit;
    letter-spacing: normal;
    font-weight: 500;
    font-size: 13px; /* 0.8125rem */
    line-height: 22px; /* 1.38462rem */
    color: rgb(47, 48, 55);
  }
}
.cl-internal-1pnppin {
  box-sizing: border-box;
  display: flex;
  flex-flow: column;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
  width: 100%;
  #clerk-captcha {
    box-sizing: border-box;
    display: none;
    margin-bottom: 24px; /* 1.5rem */
    align-self: center;
  }
  .cl-internal-ttumny {
    margin: 0px;
    padding: 6px 12px; /* 0.375rem 0.75rem */
    border-width: 1px;
    outline: 0px;
    user-select: none;
    cursor: pointer;
    background-color: var(--accent);
    color: var(--accentContrast);
    border-radius: 6px; /* 0.375rem */
    position: relative;
    isolation: isolate;
    display: inline-flex;
    justify-content: center;
    align-items: center;
    transition-property: background-color, background, border-color, color, fill,
      stroke, opacity, box-shadow, transform;
    transition-duration: 100ms;
    font-family: inherit;
    letter-spacing: normal;
    font-weight: 500;
    font-size: 13px; /* 0.8125rem */
    line-height: 22px; /* 1.38462rem */
    box-shadow: rgba(255, 255, 255, 0.07) 0px 16px 16px 0px inset,
      rgba(34, 42, 53, 0.2) 0px 32px 48px 0px,
      rgba(0, 0, 0, 0.24) 0px 16px 16px 0px;
    border-style: solid;
    border-color: var(--accent);
    width: 100%;
    --accent: #2f3037;
    --accentHover: #3b3c45;
    --border: #2f3037;
    --accentContrast: white;
    --alpha: hsla(0, 0%, 0%, 0.03);
  }
  .cl-internal-742eeh {
    box-sizing: border-box;
    display: flex;
    flex-flow: column;
    align-items: stretch;
    justify-content: flex-start;
    gap: 24px; /* 1.5rem */
    width: 100%;
    .cl-internal-ttumny {
    }
    .cl-internal-2iusy0 {
      box-sizing: border-box;
      display: flex;
      flex-flow: row;
      align-items: center;
      justify-content: flex-start;
    }
    .cl-internal-1c4ikgf {
      flex-shrink: 0;
      margin-left: 8px; /* 0.5rem */
      width: 10px;
      height: 10px;
      opacity: 0.62;
    }
  }
}
</style>

